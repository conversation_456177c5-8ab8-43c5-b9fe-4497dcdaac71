<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.WendaoLiveCourseWapMapper">
    
    <resultMap type="WendaoLiveCourseWap" id="WendaoLiveCourseWapResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="liveId"    column="live_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWendaoLiveCourseWapVo">
        select id, teacher_id, live_id, course_id, create_time, update_time from wendao_live_course_wap
    </sql>

    <select id="selectWendaoLiveCourseWapList" parameterType="WendaoLiveCourseWap" resultMap="WendaoLiveCourseWapResult">
        <include refid="selectWendaoLiveCourseWapVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="liveId != null "> and live_id = #{liveId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
        </where>
    </select>
    
    <select id="selectWendaoLiveCourseWapById" parameterType="Long" resultMap="WendaoLiveCourseWapResult">
        <include refid="selectWendaoLiveCourseWapVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWendaoLiveCourseWap" parameterType="WendaoLiveCourseWap" useGeneratedKeys="true" keyProperty="id">
        insert into wendao_live_course_wap
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="liveId != null">live_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="liveId != null">#{liveId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWendaoLiveCourseWap" parameterType="WendaoLiveCourseWap">
        update wendao_live_course_wap
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="liveId != null">live_id = #{liveId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWendaoLiveCourseWapById" parameterType="Long">
        delete from wendao_live_course_wap where id = #{id}
    </delete>

    <delete id="deleteWendaoLiveCourseWapByIds" parameterType="String">
        delete from wendao_live_course_wap where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>