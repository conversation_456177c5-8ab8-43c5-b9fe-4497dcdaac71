<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.PurchasingUserBlacklistMapper">
    
    <resultMap type="PurchasingUserBlacklist" id="PurchasingUserBlacklistResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="customerPlatform"    column="customer_platform"    />
        <result property="customerName"    column="customer_name"    />
        <result property="customerMobile"    column="customer_mobile"    />
        <result property="buyNum"    column="buy_num"    />
        <result property="buySumPrice"    column="buy_sum_price"    />
        <result property="dropCourseSum"    column="drop_course_sum"    />
        <result property="blacklistStatus"    column="blacklist_status"    />
        <result property="reason"    column="reason"    />
        <result property="proofImg"    column="proof_img"    />
        <result property="firstBuyTime"    column="first_buy_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPurchasingUserBlacklistVo">
        select id, customer_id, teacher_id, customer_platform, customer_name, customer_mobile, buy_num, buy_sum_price, drop_course_sum, blacklist_status, reason, proof_img, first_buy_time, create_time, update_time from purchasing_user_blacklist
    </sql>

    <select id="selectPurchasingUserBlacklistList" parameterType="PurchasingUserBlacklist" resultMap="PurchasingUserBlacklistResult">
        <include refid="selectPurchasingUserBlacklistVo"/>
        <where>  
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="customerPlatform != null "> and customer_platform = #{customerPlatform}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="customerMobile != null  and customerMobile != ''"> and customer_mobile = #{customerMobile}</if>
            <if test="buyNum != null "> and buy_num = #{buyNum}</if>
            <if test="buySumPrice != null "> and buy_sum_price = #{buySumPrice}</if>
            <if test="dropCourseSum != null "> and drop_course_sum = #{dropCourseSum}</if>
            <if test="blacklistStatus != null "> and blacklist_status = #{blacklistStatus}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="proofImg != null  and proofImg != ''"> and proof_img = #{proofImg}</if>
            <if test="firstBuyTime != null "> and first_buy_time = #{firstBuyTime}</if>
        </where>
    </select>

    <select id="selectBuySumPrice" resultType="java.math.BigDecimal">
        select sum(buy_sum_price) from purchasing_user_blacklist where teacher_id = #{teacherId}
    </select>

    <select id="selectUserCount" resultType="java.lang.Long">
        select count(1) from purchasing_user_blacklist where teacher_id = #{teacherId}
    </select>
    <select id="selectPhoneCount" resultType="java.lang.Long">
        select count(1) from purchasing_user_blacklist where customer_mobile is not null and teacher_id = #{teacherId}
    </select>
    <select id="selectConsumeCount" resultType="java.lang.Long">
        select sum(buy_num) from purchasing_user_blacklist where teacher_id = #{teacherId}
    </select>
    <select id="selectRefundCount" resultType="java.lang.Long">
        select sum(drop_course_sum) from purchasing_user_blacklist where teacher_id = #{teacherId}
    </select>

    <insert id="insertPurchasingUserBlacklist" parameterType="PurchasingUserBlacklist" useGeneratedKeys="true" keyProperty="id">
        insert into purchasing_user_blacklist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="customerPlatform != null">customer_platform,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="customerMobile != null">customer_mobile,</if>
            <if test="buyNum != null">buy_num,</if>
            <if test="buySumPrice != null">buy_sum_price,</if>
            <if test="dropCourseSum != null">drop_course_sum,</if>
            <if test="blacklistStatus != null">blacklist_status,</if>
            <if test="reason != null">reason,</if>
            <if test="proofImg != null">proof_img,</if>
            <if test="firstBuyTime != null">first_buy_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="customerPlatform != null">#{customerPlatform},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="customerMobile != null">#{customerMobile},</if>
            <if test="buyNum != null">#{buyNum},</if>
            <if test="buySumPrice != null">#{buySumPrice},</if>
            <if test="dropCourseSum != null">#{dropCourseSum},</if>
            <if test="blacklistStatus != null">#{blacklistStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="proofImg != null">#{proofImg},</if>
            <if test="firstBuyTime != null">#{firstBuyTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePurchasingUserBlacklist" parameterType="PurchasingUserBlacklist">
        update purchasing_user_blacklist
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="customerPlatform != null">customer_platform = #{customerPlatform},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="customerMobile != null">customer_mobile = #{customerMobile},</if>
            <if test="buyNum != null">buy_num = #{buyNum},</if>
            <if test="buySumPrice != null">buy_sum_price = #{buySumPrice},</if>
            <if test="dropCourseSum != null">drop_course_sum = #{dropCourseSum},</if>
            <if test="blacklistStatus != null">blacklist_status = #{blacklistStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="proofImg != null">proof_img = #{proofImg},</if>
            <if test="firstBuyTime != null">first_buy_time = #{firstBuyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePurchasingUserBlacklistById" parameterType="Long">
        delete from purchasing_user_blacklist where id = #{id}
    </delete>

    <delete id="deletePurchasingUserBlacklistByIds" parameterType="String">
        delete from purchasing_user_blacklist where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>