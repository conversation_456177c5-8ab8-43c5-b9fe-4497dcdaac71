<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.CourseDirectoryAuditMapper">
    
    <resultMap type="CourseDirectoryAudit" id="CourseDirectoryAuditResult">
        <result property="id"    column="id"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="serialAllNumber"    column="serial_all_number"    />
        <result property="directoryName"    column="directory_name"    />
        <result property="courseDirectoryUrl"    column="course_directory_url"    />
        <result property="duration"    column="duration"    />
        <result property="isTrySee"    column="is_try_see"    />
        <result property="trySeeDuration"    column="try_see_duration"    />
        <result property="removeChapter"    column="remove_chapter"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="chaptyerName"    column="chaptyer_name"    />
        <result property="directoryType"    column="directory_type"    />
        <result property="videoHeight"    column="video_height"    />
        <result property="videoWidth"    column="video_width"    />

        <result property="ksEpisodeNumber"    column="ks_episode_number"    />
        <result property="ksAuditStatus"    column="ks_audit_status"    />
        <result property="ksFailReason"    column="ks_fail_reason"    />
        <result property="directoryContent"    column="directory_content"    />
    </resultMap>

    <sql id="selectCourseDirectoryAuditVo">
        select id, chapter_id, teacher_id, course_id, serial_number,
               serial_all_number, directory_name, course_directory_url, duration,
               is_try_see, try_see_duration, remove_chapter, is_delete, create_time,
               update_time, chaptyer_name, directory_type, video_height, video_width,
               ks_episode_number, ks_audit_status, ks_fail_reason,directory_content from course_directory_audit
    </sql>

    <select id="selectCourseDirectoryAuditList" parameterType="CourseDirectoryAudit" resultMap="CourseDirectoryAuditResult">
        <include refid="selectCourseDirectoryAuditVo"/>
        <where>  
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="serialNumber != null "> and serial_number = #{serialNumber}</if>
            <if test="serialAllNumber != null "> and serial_all_number = #{serialAllNumber}</if>
            <if test="directoryName != null  and directoryName != ''"> and directory_name like concat('%', #{directoryName}, '%')</if>
            <if test="courseDirectoryUrl != null  and courseDirectoryUrl != ''"> and course_directory_url = #{courseDirectoryUrl}</if>
            <if test="duration != null "> and duration = #{duration}</if>
            <if test="isTrySee != null "> and is_try_see = #{isTrySee}</if>
            <if test="trySeeDuration != null "> and try_see_duration = #{trySeeDuration}</if>
            <if test="removeChapter != null "> and remove_chapter = #{removeChapter}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="chaptyerName != null  and chaptyerName != ''"> and chaptyer_name like concat('%', #{chaptyerName}, '%')</if>
            <if test="directoryType != null "> and directory_type = #{directoryType}</if>
            <if test="videoHeight != null "> and video_height = #{videoHeight}</if>
            <if test="videoWidth != null "> and video_width = #{videoWidth}</if>
            <if test="ksEpisodeNumber != null "> and ks_episode_number = #{ksEpisodeNumber}</if>
            <if test="ksAuditStatus != null "> and ks_audit_status = #{ksAuditStatus}</if>
            <if test="ksFailReason != null and ksFailReason != ''"> and ks_fail_reason = #{ksFailReason}</if>
        </where>
    </select>
    
    <select id="selectCourseDirectoryAuditById" parameterType="Long" resultMap="CourseDirectoryAuditResult">
        <include refid="selectCourseDirectoryAuditVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCourseDirectoryAudit" parameterType="CourseDirectoryAudit">
        insert into course_directory_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="serialAllNumber != null">serial_all_number,</if>
            <if test="directoryName != null">directory_name,</if>
            <if test="courseDirectoryUrl != null">course_directory_url,</if>
            <if test="duration != null">duration,</if>
            <if test="isTrySee != null">is_try_see,</if>
            <if test="trySeeDuration != null">try_see_duration,</if>
            <if test="removeChapter != null">remove_chapter,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="chaptyerName != null">chaptyer_name,</if>
            <if test="directoryType != null">directory_type,</if>
            <if test="videoHeight != null">video_height,</if>
            <if test="videoWidth != null">video_width,</if>
            <if test="ksEpisodeNumber != null">ks_episode_number,</if>
            <if test="ksAuditStatus != null">ks_audit_status,</if>
            <if test="ksFailReason != null">ks_fail_reason,</if>
            <if test="directoryContent != null">directory_content,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="serialAllNumber != null">#{serialAllNumber},</if>
            <if test="directoryName != null">#{directoryName},</if>
            <if test="courseDirectoryUrl != null">#{courseDirectoryUrl},</if>
            <if test="duration != null">#{duration},</if>
            <if test="isTrySee != null">#{isTrySee},</if>
            <if test="trySeeDuration != null">#{trySeeDuration},</if>
            <if test="removeChapter != null">#{removeChapter},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="chaptyerName != null">#{chaptyerName},</if>
            <if test="directoryType != null">#{directoryType},</if>
            <if test="videoHeight != null">#{videoHeight},</if>
            <if test="videoWidth != null">#{videoWidth},</if>
            <if test="ksEpisodeNumber != null">#{ksEpisodeNumber},</if>
            <if test="ksAuditStatus != null">#{ksAuditStatus},</if>
            <if test="ksFailReason != null">#{ksFailReason},</if>
            <if test="directoryContent != null">#{directoryContent},</if>
         </trim>
    </insert>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO course_directory_audit(id,chapter_id, teacher_id, course_id, serial_number, serial_all_number, directory_name, course_directory_url, duration, is_try_see, try_see_duration, remove_chapter, chaptyer_name, directory_type, video_height, video_width,ks_episode_number,ks_audit_status,ks_fail_reason,directory_content)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.chapterId},#{item.teacherId},#{item.courseId},#{item.serialNumber},#{item.serialAllNumber},#{item.directoryName},#{item.courseDirectoryUrl},#{item.duration},#{item.isTrySee},#{item.trySeeDuration},#{item.removeChapter},#{item.chaptyerName},#{item.directoryType},#{item.videoHeight},#{item.videoWidth},#{item.ksEpisodeNumber},#{item.ksAuditStatus},#{item.ksFailReason},#{item.directoryContent})
        </foreach>
    </insert>

    <update id="updateCourseDirectoryAudit" parameterType="CourseDirectoryAudit">
        update course_directory_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="serialAllNumber != null">serial_all_number = #{serialAllNumber},</if>
            <if test="directoryName != null">directory_name = #{directoryName},</if>
            <if test="courseDirectoryUrl != null">course_directory_url = #{courseDirectoryUrl},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="isTrySee != null">is_try_see = #{isTrySee},</if>
            <if test="trySeeDuration != null">try_see_duration = #{trySeeDuration},</if>
            <if test="removeChapter != null">remove_chapter = #{removeChapter},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="chaptyerName != null">chaptyer_name = #{chaptyerName},</if>
            <if test="directoryType != null">directory_type = #{directoryType},</if>
            <if test="videoHeight != null">video_height = #{videoHeight},</if>
            <if test="videoWidth != null">video_width = #{videoWidth},</if>
            <if test="ksEpisodeNumber != null">ks_episode_number = #{ksEpisodeNumber},</if>
            <if test="ksAuditStatus != null">ks_audit_status = #{ksAuditStatus},</if>
            <if test="ksFailReason != null">ks_fail_reason = #{ksFailReason},</if>
            <if test="directoryContent != null">directory_content = #{directoryContent},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseDirectoryAuditById" parameterType="Long">
        delete from course_directory_audit where id = #{id}
    </delete>

    <delete id="deleteCourseDirectoryAuditByIds" parameterType="String">
        delete from course_directory_audit where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteCourseDirectoryDyByCourseId">
        delete from course_directory_audit where course_id = #{courseId}
    </delete>
</mapper>