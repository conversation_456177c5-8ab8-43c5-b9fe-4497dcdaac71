<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TAudioGroupRelationMapper">
    
    <resultMap type="TAudioGroupRelation" id="TAudioGroupRelationResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="audioId"    column="audio_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTAudioGroupRelationVo">
        select id, teacher_id, group_id, audio_id, create_time, update_time from t_audio_group_relation
    </sql>

    <select id="selectTAudioGroupRelationList" parameterType="TAudioGroupRelation" resultMap="TAudioGroupRelationResult">
        <include refid="selectTAudioGroupRelationVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="audioId != null "> and audio_id = #{audioId}</if>
        </where>
    </select>
    
    <select id="selectTAudioGroupRelationById" parameterType="Long" resultMap="TAudioGroupRelationResult">
        <include refid="selectTAudioGroupRelationVo"/>
        where id = #{id}
    </select>

    <update id="insertTAudioGroupRelation" parameterType="java.util.List">
        INSERT ignore INTO t_audio_group_relation (teacher_id,group_id,audio_id) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.teacherId},#{item.groupId},#{item.audioId})
        </foreach>
    </update>
        
<!--    <insert id="insertTAudioGroupRelation" parameterType="TAudioGroupRelation" useGeneratedKeys="true" keyProperty="id">-->
<!--        insert into t_audio_group_relation-->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--            <if test="teacherId != null">teacher_id,</if>-->
<!--            <if test="groupId != null">group_id,</if>-->
<!--            <if test="audioId != null">audio_id,</if>-->
<!--            <if test="createTime != null">create_time,</if>-->
<!--            <if test="updateTime != null">update_time,</if>-->
<!--         </trim>-->
<!--        <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--            <if test="teacherId != null">#{teacherId},</if>-->
<!--            <if test="groupId != null">#{groupId},</if>-->
<!--            <if test="audioId != null">#{audioId},</if>-->
<!--            <if test="createTime != null">#{createTime},</if>-->
<!--            <if test="updateTime != null">#{updateTime},</if>-->
<!--         </trim>-->
<!--    </insert>-->

    <update id="updateTAudioGroupRelation" parameterType="TAudioGroupRelation">
        update t_audio_group_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="audioId != null">audio_id = #{audioId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTAudioGroupRelationById" parameterType="Long">
        delete from t_audio_group_relation where id = #{id}
    </delete>

    <delete id="deleteTAudioGroupRelationByIds" parameterType="String">
        delete from t_audio_group_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="removeFromGroup">
        delete from t_audio_group_relation where teacher_id = #{teacherId} and group_id = #{groupId} and audio_id in
        <foreach item="audioId" collection="audioIdArr" open="(" separator="," close=")">
            #{audioId}
        </foreach>
    </delete>
</mapper>