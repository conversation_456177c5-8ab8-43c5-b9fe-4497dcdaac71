<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.MDiscountsCodeMapMapper">
    
    <resultMap type="MDiscountsCodeMap" id="MDiscountsCodeMapResult">
        <result property="id"    column="id"    />
        <result property="discountsId"    column="discounts_id"    />
        <result property="discountsCode"    column="discounts_code"    />
        <result property="useType"    column="use_type"    />
        <result property="userId"    column="user_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="invalidationTime"    column="invalidation_time"    />
        <result property="getTime"    column="get_time"    />
        <result property="useTime"    column="use_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMDiscountsCodeMapVo">
        select id, discounts_id, discounts_code, use_type, user_id, nick_name,invalidation_time, get_time, use_time, create_time, update_time from m_discounts_code_map
    </sql>

    <select id="selectMDiscountsCodeMapList" parameterType="MDiscountsCodeMap" resultMap="MDiscountsCodeMapResult">
        <include refid="selectMDiscountsCodeMapVo"/>
        <where>  
            <if test="discountsId != null "> and discounts_id = #{discountsId}</if>
            <if test="discountsCode != null  and discountsCode != ''"> and discounts_code like concat('%', #{discountsCode}, '%')</if>
            <if test="useType != null "> and use_type = #{useType}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="getTime != null "> and get_time = #{getTime}</if>
            <if test="useTime != null "> and use_time = #{useTime}</if>
        </where>
    </select>

    <select id="SelectDiscountsCode" parameterType="com.wendao101.teacher.vo.MDiscountsAndCodeVO" resultType="com.wendao101.teacher.dto.MDiscountsAndSelectCodeDTO">
        SELECT
        c.id,
        c.discounts_code discountsCode,
        d.tel_number phone,
        c.use_type useType,
        d.nick_name nickName,
        d.avatar_url avatarUrl,
        c.invalidation_time invalidationTime,
        c.get_time getTime,
        c.use_time useTime,
        c.create_time createTime
        FROM
        m_discounts a
        LEFT JOIN course b ON a.course_id = b.id
        LEFT JOIN m_discounts_code_map c ON a.id = c.discounts_id
        LEFT JOIN wendao_user d on c.user_id = d.id
        <where>
            <if test="nickName != null  and nickName != ''">
                and d.nick_name like concat('%', #{nickName}, '%')
            </if>
            <if test="discountsCode != null  and discountsCode != ''">
                and c.discounts_code like concat('%', #{discountsCode}, '%')
            </if>
            <if test="useType != null ">
                and c.use_type = #{useType}
            </if>
            and a.id = #{discountsId} and a.is_delete = 0 and b.is_delete = 0
        </where>
        order by c.id,c.get_time desc
    </select>

    <select id="selectAll" resultType="java.lang.Integer">
        select count(1) from m_discounts_code_map
        where discounts_id = #{discountsId} and use_type != 3
    </select>

    <select id="selectMDiscountsCodeList" parameterType="com.wendao101.teacher.domain.MDiscountsCodeMap" resultType="com.wendao101.teacher.dto.MDiscountsAndCodeDTO">
        SELECT
        c.id,
        a.discounts_name discountsName,
        b.title,
        c.user_id userId,
        a.receive_coupon_start_time receiveCouponStartTime,
        a.receive_coupon_end_time receiveCouponEndTime,
        c.discounts_code discountsCode,
        c.use_type useType,
        d.nick_name nickName,
        c.invalidation_time invalidationTime,
        c.get_time getTime,
        c.use_time useTime
        FROM
        m_discounts a
        LEFT JOIN course b ON a.course_id = b.id
        LEFT JOIN m_discounts_code_map c ON a.id = c.discounts_id
        LEFT JOIN wendao_user d on c.user_id = d.id
        where a.id = #{discountsId} and a.is_delete = 0 and b.is_delete = 0
        order by c.create_time desc
    </select>

    <select id="SelectDiscountsCodeData" parameterType="Long" resultType="com.wendao101.teacher.dto.MDiscountsAndCodeDataDTO">
        SELECT
        a.discounts_name discountsName,
        b.title,
        a.receive_coupon_start_time receiveCouponStartTime,
        a.receive_coupon_end_time receiveCouponEndTime
        FROM
        m_discounts a
        LEFT JOIN course b ON a.course_id = b.id
        where a.id = #{discountsId} and a.is_delete = 0 and b.is_delete = 0
    </select>

    <select id="unusedNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM m_discounts_code_map
        where discounts_id = #{discountsId} and use_type = 0
    </select>
    <select id="haveBeenUsedNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM m_discounts_code_map
        where discounts_id = #{discountsId} and use_type = 1
    </select>
    <select id="residueNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM m_discounts_code_map
        where discounts_id = #{discountsId} and use_type = 3
    </select>

    <select id="getResidueNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM m_discounts_code_map
        where discounts_id = #{discountsId} and use_type = 3
    </select>

    <select id="residueAndCode" resultMap="MDiscountsCodeMapResult">
        <include refid="selectMDiscountsCodeMapVo"/>
        WHERE discounts_id = #{discountsId} and use_type = 3  LIMIT #{length}
    </select>

    <select id="selectCodeById" resultMap="MDiscountsCodeMapResult">
        <include refid="selectMDiscountsCodeMapVo"/>
        WHERE discounts_id = #{discountsId}
    </select>

    <select id="selectMDiscountsCodeMapById" parameterType="Long" resultMap="MDiscountsCodeMapResult">
        <include refid="selectMDiscountsCodeMapVo"/>
        where id = #{id}
    </select>

    <insert id="insertMDiscountsCodeMap" parameterType="MDiscountsCodeMap" useGeneratedKeys="true" keyProperty="id">
        insert into m_discounts_code_map
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="discountsId != null">discounts_id,</if>
            <if test="discountsCode != null">discounts_code,</if>
            <if test="useType != null">use_type,</if>
            <if test="userId != null">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="invalidationTime != null">invalidation_time,</if>
            <if test="getTime != null">get_time,</if>
            <if test="useTime != null">use_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="discountsId != null">#{discountsId},</if>
            <if test="discountsCode != null">#{discountsCode},</if>
            <if test="useType != null">#{useType},</if>
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="invalidationTime != null">#{invalidationTime},</if>
            <if test="getTime != null">#{getTime},</if>
            <if test="useTime != null">#{useTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="isCancellation">
        update m_discounts_code_map SET use_type = 2,invalidation_time = SYSDATE()
        where id = #{id}
    </update>

    <update id="updateMdiscountsStatus">
        update m_discounts_code_map SET use_type = 2,invalidation_time = SYSDATE()
        where discounts_id = #{discountsId} and use_type in (0,3)
    </update>

    <update id="updateMdiscountsContinueStatus">
        update m_discounts_code_map SET use_type = 2,invalidation_time = SYSDATE()
        where discounts_id = #{discountsId} and user_id IS NULL
    </update>

    <update id="updateMDiscountsCodeMap" parameterType="MDiscountsCodeMap">
        update m_discounts_code_map
        <trim prefix="SET" suffixOverrides=",">
            <if test="discountsId != null">discounts_id = #{discountsId},</if>
            <if test="discountsCode != null">discounts_code = #{discountsCode},</if>
            <if test="useType != null">use_type = #{useType},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="invalidationTime != null">invalidation_time = #{invalidationTime},</if>
            <if test="getTime != null">get_time = #{getTime},</if>
            <if test="useTime != null">use_time = #{useTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMDiscountsCodeMapById" parameterType="Long">
        delete from m_discounts_code_map where id = #{id}
    </delete>

    <delete id="deleteMDiscountsCodeMapByIds" parameterType="String">
        delete from m_discounts_code_map where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>