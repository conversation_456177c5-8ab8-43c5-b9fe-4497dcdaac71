<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.DoudianLogisticsSettingMapper">
    
    <resultMap type="DoudianLogisticsSetting" id="DoudianLogisticsSettingResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="logisticsSetting"    column="logistics_setting"    />
        <result property="returnName"    column="return_name"    />
        <result property="returnTel"    column="return_tel"    />
        <result property="provinceId"    column="province_id"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityId"    column="city_id"    />
        <result property="cityName"    column="city_name"    />
        <result property="townId"    column="town_id"    />
        <result property="townName"    column="town_name"    />
        <result property="streetId"    column="street_id"    />
        <result property="streetName"    column="street_name"    />
        <result property="detail"    column="detail"    />
        <result property="afterSaleAddressId"    column="after_sale_address_id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDoudianLogisticsSettingVo">
        select id, teacher_id, logistics_setting, return_name, return_tel,
               province_id, province_name, city_id, city_name, town_id, town_name, street_id,
               street_name, detail, after_sale_address_id, shop_id, create_time, update_time from `wendao101-order`.doudian_logistics_setting
    </sql>

    <select id="selectDoudianLogisticsSettingList" parameterType="DoudianLogisticsSetting" resultMap="DoudianLogisticsSettingResult">
        <include refid="selectDoudianLogisticsSettingVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="logisticsSetting != null "> and logistics_setting = #{logisticsSetting}</if>
            <if test="returnName != null  and returnName != ''"> and return_name like concat('%', #{returnName}, '%')</if>
            <if test="returnTel != null  and returnTel != ''"> and return_tel = #{returnTel}</if>
            <if test="provinceId != null  and provinceId != ''"> and province_id = #{provinceId}</if>
            <if test="provinceName != null  and provinceName != ''"> and province_name like concat('%', #{provinceName}, '%')</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
            <if test="townId != null  and townId != ''"> and town_id = #{townId}</if>
            <if test="townName != null  and townName != ''"> and town_name like concat('%', #{townName}, '%')</if>
            <if test="streetId != null  and streetId != ''"> and street_id = #{streetId}</if>
            <if test="streetName != null  and streetName != ''"> and street_name like concat('%', #{streetName}, '%')</if>
            <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
            <if test="afterSaleAddressId != null "> and after_sale_address_id = #{afterSaleAddressId}</if>
            <if test="shopId != null  and shopId != ''"> and shop_id = #{shopId}</if>
        </where>
    </select>
    
    <select id="selectDoudianLogisticsSettingById" parameterType="Long" resultMap="DoudianLogisticsSettingResult">
        <include refid="selectDoudianLogisticsSettingVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDoudianLogisticsSetting" parameterType="DoudianLogisticsSetting" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.doudian_logistics_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="logisticsSetting != null">logistics_setting,</if>
            <if test="returnName != null">return_name,</if>
            <if test="returnTel != null">return_tel,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="provinceName != null">province_name,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
            <if test="townId != null">town_id,</if>
            <if test="townName != null">town_name,</if>
            <if test="streetId != null">street_id,</if>
            <if test="streetName != null">street_name,</if>
            <if test="detail != null">detail,</if>
            <if test="afterSaleAddressId != null">after_sale_address_id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="logisticsSetting != null">#{logisticsSetting},</if>
            <if test="returnName != null">#{returnName},</if>
            <if test="returnTel != null">#{returnTel},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="provinceName != null">#{provinceName},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="townId != null">#{townId},</if>
            <if test="townName != null">#{townName},</if>
            <if test="streetId != null">#{streetId},</if>
            <if test="streetName != null">#{streetName},</if>
            <if test="detail != null">#{detail},</if>
            <if test="afterSaleAddressId != null">#{afterSaleAddressId},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDoudianLogisticsSetting" parameterType="DoudianLogisticsSetting">
        update `wendao101-order`.doudian_logistics_setting
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="logisticsSetting != null">logistics_setting = #{logisticsSetting},</if>
            <if test="returnName != null">return_name = #{returnName},</if>
            <if test="returnTel != null">return_tel = #{returnTel},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="townId != null">town_id = #{townId},</if>
            <if test="townName != null">town_name = #{townName},</if>
            <if test="streetId != null">street_id = #{streetId},</if>
            <if test="streetName != null">street_name = #{streetName},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="afterSaleAddressId != null">after_sale_address_id = #{afterSaleAddressId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDoudianLogisticsSettingById" parameterType="Long">
        delete from `wendao101-order`.doudian_logistics_setting where id = #{id}
    </delete>

    <delete id="deleteDoudianLogisticsSettingByIds" parameterType="String">
        delete from `wendao101-order`.doudian_logistics_setting where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>