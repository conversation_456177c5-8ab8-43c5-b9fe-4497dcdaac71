<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.DoudianShopConfigMapper">
    
    <resultMap type="DoudianShopConfig" id="DoudianShopConfigResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="appKey"    column="app_key"    />
        <result property="appSecret"    column="app_secret"    />
        <result property="firstCatIdLimit"    column="first_cat_id_limit"    />
        <result property="productMainImgPlaceholder"    column="product_main_img_placeholder"    />
        <result property="productDetailImgPlaceholder"    column="product_detail_img_placeholder"    />
        <result property="isPublic"    column="is_public"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="preMoneyEarlyWarning"    column="pre_money_early_warning"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isSupportThirdCoupon"    column="is_support_third_coupon"    />
        <result property="openThirdCoupon"    column="open_third_coupon"    />
        <result property="wendaoShopName"    column="wendao_shop_name"    />
    </resultMap>

    <sql id="selectDoudianShopConfigVo">
        select id, shop_id, shop_name, app_key, app_secret, first_cat_id_limit, product_main_img_placeholder, product_detail_img_placeholder, is_public, teacher_id, pre_money_early_warning, create_time, update_time, is_support_third_coupon, open_third_coupon, wendao_shop_name from doudian_shop_config
    </sql>

    <select id="selectDoudianShopConfigList" parameterType="DoudianShopConfig" resultMap="DoudianShopConfigResult">
        <include refid="selectDoudianShopConfigVo"/>
        <where>  
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="appKey != null  and appKey != ''"> and app_key = #{appKey}</if>
            <if test="appSecret != null  and appSecret != ''"> and app_secret = #{appSecret}</if>
            <if test="firstCatIdLimit != null  and firstCatIdLimit != ''"> and first_cat_id_limit = #{firstCatIdLimit}</if>
            <if test="productMainImgPlaceholder != null  and productMainImgPlaceholder != ''"> and product_main_img_placeholder = #{productMainImgPlaceholder}</if>
            <if test="productDetailImgPlaceholder != null  and productDetailImgPlaceholder != ''"> and product_detail_img_placeholder = #{productDetailImgPlaceholder}</if>
            <if test="isPublic != null "> and is_public = #{isPublic}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="preMoneyEarlyWarning != null "> and pre_money_early_warning = #{preMoneyEarlyWarning}</if>
            <if test="isSupportThirdCoupon != null "> and is_support_third_coupon = #{isSupportThirdCoupon}</if>
            <if test="openThirdCoupon != null "> and open_third_coupon = #{openThirdCoupon}</if>
            <if test="wendaoShopName != null  and wendaoShopName != ''"> and wendao_shop_name like concat('%', #{wendaoShopName}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="getAllUniqueDoudianShopConfigs" resultMap="DoudianShopConfigResult">
        SELECT DISTINCT app_key, app_secret
        FROM doudian_shop_config
    </select>
    
    <select id="selectDoudianShopConfigById" parameterType="Long" resultMap="DoudianShopConfigResult">
        <include refid="selectDoudianShopConfigVo"/>
        where id = #{id}
    </select>
    <select id="selectDoudianShopConfigByShopId" parameterType="Long" resultMap="DoudianShopConfigResult">
        <include refid="selectDoudianShopConfigVo"/>
        where shop_id = #{shopId}
    </select>

    <insert id="insertDoudianShopConfig" parameterType="DoudianShopConfig" useGeneratedKeys="true" keyProperty="id">
        insert into doudian_shop_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="appKey != null">app_key,</if>
            <if test="appSecret != null">app_secret,</if>
            <if test="firstCatIdLimit != null">first_cat_id_limit,</if>
            <if test="productMainImgPlaceholder != null">product_main_img_placeholder,</if>
            <if test="productDetailImgPlaceholder != null">product_detail_img_placeholder,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="preMoneyEarlyWarning != null">pre_money_early_warning,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isSupportThirdCoupon != null">is_support_third_coupon,</if>
            <if test="openThirdCoupon != null">open_third_coupon,</if>
            <if test="wendaoShopName != null">wendao_shop_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="appKey != null">#{appKey},</if>
            <if test="appSecret != null">#{appSecret},</if>
            <if test="firstCatIdLimit != null">#{firstCatIdLimit},</if>
            <if test="productMainImgPlaceholder != null">#{productMainImgPlaceholder},</if>
            <if test="productDetailImgPlaceholder != null">#{productDetailImgPlaceholder},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="preMoneyEarlyWarning != null">#{preMoneyEarlyWarning},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isSupportThirdCoupon != null">#{isSupportThirdCoupon},</if>
            <if test="openThirdCoupon != null">#{openThirdCoupon},</if>
            <if test="wendaoShopName != null">#{wendaoShopName},</if>
         </trim>
    </insert>

    <update id="updateDoudianShopConfig" parameterType="DoudianShopConfig">
        update doudian_shop_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="appKey != null">app_key = #{appKey},</if>
            <if test="appSecret != null">app_secret = #{appSecret},</if>
            <if test="firstCatIdLimit != null">first_cat_id_limit = #{firstCatIdLimit},</if>
            <if test="productMainImgPlaceholder != null">product_main_img_placeholder = #{productMainImgPlaceholder},</if>
            <if test="productDetailImgPlaceholder != null">product_detail_img_placeholder = #{productDetailImgPlaceholder},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="preMoneyEarlyWarning != null">pre_money_early_warning = #{preMoneyEarlyWarning},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isSupportThirdCoupon != null">is_support_third_coupon = #{isSupportThirdCoupon},</if>
            <if test="openThirdCoupon != null">open_third_coupon = #{openThirdCoupon},</if>
            <if test="wendaoShopName != null">wendao_shop_name = #{wendaoShopName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDoudianShopConfigById" parameterType="Long">
        delete from doudian_shop_config where id = #{id}
    </delete>

    <delete id="deleteDoudianShopConfigByIds" parameterType="String">
        delete from doudian_shop_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
