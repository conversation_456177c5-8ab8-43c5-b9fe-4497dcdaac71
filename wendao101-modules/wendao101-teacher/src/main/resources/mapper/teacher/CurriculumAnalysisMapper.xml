<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.CurriculumAnalysisMapper">
    
    <select id="courseRankingList" parameterType="com.wendao101.teacher.vo.CurriculumAnalysisCourseVO" resultType="com.wendao101.teacher.dto.CurriculumAnalysisCourseDTO">
        SELECT a.*,if(b.course_browsing_num is null,0,b.course_browsing_num) accessUser FROM
        (SELECT
        course_id courseId,
        course_img_url courseImgUrl,
        course_title courseTitle,
        course_price coursePrice,
        original_price originalPrice,
        sum(CASE WHEN order_status in (1,2) THEN pay_price ELSE 0 END) as generalIncome,
        sum( CASE WHEN order_status = 1 THEN 1 ELSE 0 END ) AS purchasingUser,
        sum( CASE WHEN id IS NOT NULL THEN 1 ELSE 0 END ) AS orderUser
        FROM
        `wendao101-order`.course_order
        <where>
                and is_delete = 0
            <if test="teacherId != null">
                and teacher_id = #{teacherId}
            </if>
            <if test="courseTitle != null and courseTitle != ''">
                and course_title like concat('%', #{courseTitle} ,'%')
            </if>
            <if test="orderPlatform != null">
                and order_platform = #{orderPlatform}
            </if>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                AND create_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                AND create_time &lt;= #{endTime}
            </if>
        </where>
            GROUP BY course_id ) a
            LEFT JOIN mam_picture_view b on a.courseId = b.course_audit_id
        <where>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                AND b.view_time &gt;= Date(#{beginTime})
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                AND b.view_time &lt;= Date(#{endTime})
            </if>
            <if test="orderPlatform != null">
                and b.platform = #{orderPlatform}
            </if>
        </where>
            GROUP BY courseId
            <if test="type == 1">
                ORDER BY accessUser desc
            </if>
            <if test="type == 2">
                ORDER BY orderUser desc
            </if>
            <if test="type == 3">
                ORDER BY purchasingUser desc
            </if>
            <if test="type == 4">
                ORDER BY generalIncome desc
            </if>

    </select>

    <select id="viewStudentsList" parameterType="com.wendao101.teacher.vo.ViewStudentVO" resultType="com.wendao101.teacher.dto.ViewStudentDTO">
        SELECT order_id orderId, buyer_user_id buyerUserId, buyer_user_name buyerUserName, buyer_user_mobile buyerUserMobile, course_duration courseDuration
        FROM `wendao101-order`.course_order
            <where>
                <if test="teacherId != null">
                    and  teacher_id = #{teacherId}
                </if>
                <if test="courseId != null">
                    and  course_id = #{courseId}
                </if>
                <if test="buyerUserName != null and buyerUserName != ''">
                    and  buyer_user_name like concat('%', #{buyerUserName}, '%')
                </if>
                and is_delete = 0
                and order_status = 1
            </where>
    </select>

    <select id="cumulativeLearningTimeSum"  resultType="java.lang.Long">
        SELECT SUM(cumulative_learning_time) FROM `wendao101-douyin`.learning_record WHERE order_id = #{orderId}
    </select>

    <select id="lastStudyTime"  resultType="java.util.Date">
        SELECT  reporting_time FROM `wendao101-douyin`.learning_record WHERE order_id = #{orderId} ORDER BY reporting_time desc LIMIT 1
    </select>

    <select id="selectAccessCourseCount" parameterType="com.wendao101.teacher.vo.CurriculumAnalysisVO" resultType="java.lang.Integer">
        SELECT sum(course_browsing_num) FROM mam_picture_view
        <where>
            <if test="teacherId != null">
                and teacher_id = #{teacherId}
            </if>
            <if test="platform != null">
                and platform = #{platform}
            </if>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                and view_time &gt;=  DATE(#{beginTime})
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                and view_time &lt;= DATE(#{endTime})
            </if>
        </where>

    </select>

    <select id="selectInitiatePaymentCount" parameterType="com.wendao101.teacher.vo.CurriculumAnalysisVO" resultType="java.lang.Integer">
        SELECT count(1) FROM `wendao101-order`.course_order
        <where>
            <if test="teacherId != null">
                and teacher_id = #{teacherId}
            </if>
            <if test="platform != null">
                and order_platform = #{platform}
            </if>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                and order_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                and order_time &lt;= #{endTime}
            </if>
            and order_type = 0
            and is_delete = 0
        </where>

    </select>

    <select id="selectPaymentSuccessCount" parameterType="com.wendao101.teacher.vo.CurriculumAnalysisVO" resultType="java.lang.Integer">
        SELECT count(1) FROM `wendao101-order`.course_order
        <where>
            <if test="teacherId != null">
                and teacher_id = #{teacherId}
            </if>
            <if test="platform != null">
                and order_platform = #{platform}
            </if>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                and order_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                and order_time &lt;= #{endTime}
            </if>
            and order_type = 0
            and is_delete = 0
            and order_status = 1
        </where>

    </select>

    <select id="selectInitiatePaymentCountToday" resultType="java.lang.Integer">
        SELECT count(1) FROM `wendao101-order`.course_order
        <where>
            <if test="teacherId != null">
                and teacher_id = #{teacherId}
            </if>
            <if test="platform != null">
                and order_platform = #{platform}
            </if>
            and order_type = 0
            and is_delete = 0
            and DATE(order_time) = #{dateTime}
        </where>
    </select>

    <select id="selectPaymentSuccessCountToday" resultType="java.lang.Integer">
        SELECT count(1) FROM `wendao101-order`.course_order
        <where>
            <if test="teacherId != null">
                and teacher_id = #{teacherId}
            </if>
            <if test="platform != null">
                and order_platform = #{platform}
            </if>
            and order_type = 0
            and is_delete = 0
            and order_status = 1
            and DATE(order_time) = #{dateTime}
        </where>
    </select>

    <select id="selectAccessCourseCountToday" resultType="java.lang.Integer">
        SELECT sum(course_browsing_num) FROM mam_picture_view
        <where>
            <if test="teacherId != null">
                and teacher_id = #{teacherId}
            </if>
            <if test="platform != null">
                and platform = #{platform}
            </if>
            and DATE(view_time) = #{dateTime}
        </where>
    </select>
    
</mapper>