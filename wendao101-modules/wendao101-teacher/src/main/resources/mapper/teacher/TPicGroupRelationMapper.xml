<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TPicGroupRelationMapper">
    
    <resultMap type="TPicGroupRelation" id="TPicGroupRelationResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="picId"    column="pic_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTPicGroupRelationVo">
        select id, teacher_id, group_id, pic_id, create_time, update_time from t_pic_group_relation
    </sql>

    <select id="selectTPicGroupRelationList" parameterType="TPicGroupRelation" resultMap="TPicGroupRelationResult">
        <include refid="selectTPicGroupRelationVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="picId != null "> and pic_id = #{picId}</if>
        </where>
    </select>
    
    <select id="selectTPicGroupRelationById" parameterType="Long" resultMap="TPicGroupRelationResult">
        <include refid="selectTPicGroupRelationVo"/>
        where id = #{id}
    </select>

    <update id="insertTPicGroupRelation" parameterType="java.util.List">
        INSERT ignore INTO t_pic_group_relation (teacher_id,group_id,pic_id) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.teacherId},#{item.groupId},#{item.picId})
        </foreach>
    </update>
        
<!--    <insert id="insertTPicGroupRelation" parameterType="TPicGroupRelation" useGeneratedKeys="true" keyProperty="id">-->
<!--        insert into t_pic_group_relation-->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--            <if test="teacherId != null">teacher_id,</if>-->
<!--            <if test="groupId != null">group_id,</if>-->
<!--            <if test="picId != null">pic_id,</if>-->
<!--            <if test="createTime != null">create_time,</if>-->
<!--            <if test="updateTime != null">update_time,</if>-->
<!--         </trim>-->
<!--        <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--            <if test="teacherId != null">#{teacherId},</if>-->
<!--            <if test="groupId != null">#{groupId},</if>-->
<!--            <if test="picId != null">#{picId},</if>-->
<!--            <if test="createTime != null">#{createTime},</if>-->
<!--            <if test="updateTime != null">#{updateTime},</if>-->
<!--         </trim>-->
<!--    </insert>-->

    <update id="updateTPicGroupRelation" parameterType="TPicGroupRelation">
        update t_pic_group_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="picId != null">pic_id = #{picId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTPicGroupRelationById" parameterType="Long">
        delete from t_pic_group_relation where id = #{id}
    </delete>

    <delete id="deleteTPicGroupRelationByIds" parameterType="String">
        delete from t_pic_group_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="removeFromGroup">
        delete from t_pic_group_relation where teacher_id = #{teacherId} and group_id = #{groupId} and pic_id in
        <foreach item="picId" collection="picIdArr" open="(" separator="," close=")">
            #{picId}
        </foreach>
    </delete>

</mapper>