<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TVideoGroupRelationMapper">
    
    <resultMap type="TVideoGroupRelation" id="TVideoGroupRelationResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="videoId"    column="video_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTVideoGroupRelationVo">
        select id, teacher_id, group_id, video_id, create_time, update_time from t_video_group_relation
    </sql>

    <select id="selectTVideoGroupRelationList" parameterType="TVideoGroupRelation" resultMap="TVideoGroupRelationResult">
        <include refid="selectTVideoGroupRelationVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="videoId != null "> and video_id = #{videoId}</if>
        </where>
    </select>
    
    <select id="selectTVideoGroupRelationById" parameterType="Long" resultMap="TVideoGroupRelationResult">
        <include refid="selectTVideoGroupRelationVo"/>
        where id = #{id}
    </select>

    <update id="insertTVideoGroupRelation" parameterType="java.util.List">
        INSERT ignore INTO t_video_group_relation (teacher_id,group_id,video_id) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.teacherId},#{item.groupId},#{item.videoId})
        </foreach>
    </update>

    <update id="updateTVideoGroupRelation" parameterType="TVideoGroupRelation">
        update t_video_group_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTVideoGroupRelationById" parameterType="Long">
        delete from t_video_group_relation where id = #{id}
    </delete>

    <delete id="deleteTVideoGroupRelationByIds" parameterType="String">
        delete from t_video_group_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="removeFromGroup">
        delete from t_video_group_relation where teacher_id = #{teacherId} and group_id = #{groupId} and video_id in
        <foreach item="videoId" collection="videoIdArr" open="(" separator="," close=")">
            #{videoId}
        </foreach>
    </delete>
</mapper>