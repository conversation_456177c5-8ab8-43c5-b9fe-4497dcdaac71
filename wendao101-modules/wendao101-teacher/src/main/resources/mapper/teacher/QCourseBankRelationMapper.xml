<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.QCourseBankRelationMapper">
    
    <resultMap type="QCourseBankRelation" id="QCourseBankRelationResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="questionBankId"    column="question_bank_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQCourseBankRelationVo">
        select id, teacher_id, course_id, question_bank_id, create_time, update_time from q_course_bank_relation
    </sql>

    <select id="selectQCourseBankRelationList" parameterType="QCourseBankRelation" resultType="com.wendao101.teacher.domain.QCourseBankRelation">
        select a.id as id
                 , a.teacher_id as teacherId
                 , a.course_id as courseId
                 , a.question_bank_id as questionBankId
                 , a.create_time as createTime
                 , a.update_time as updateTime
                , b.title as courseTitle
                , b.course_id_number as courseIdNumber
                , b.price as price
                , b.original_price as originalPrice
                , b.cover_pic_url as coverPicUrl
                , b.publish_platform as publishPlatform
                , c.title as bankTitle
                , c.`desc` as bankDesc
        from q_course_bank_relation a left join course b on a.course_id = b.id left join q_question_bank c on a.question_bank_id = c.id
        <where>
            <if test="teacherId != null "> and a.teacher_id = #{teacherId}</if>
            <if test="courseId != null "> and a.course_id = #{courseId}</if>
            <if test="questionBankId != null "> and a.question_bank_id = #{questionBankId}</if>
            <if test="courseTitle != null  and courseTitle != ''"> and b.title like concat('%', #{courseTitle}, '%')</if>
            <if test="courseIdNumber != null "> and b.course_id_number = #{courseIdNumber}</if>
            <if test="platform == 0">AND JSON_EXTRACT(b.publish_platform, '$.dy') = 1</if>
            <if test="platform == 1">AND JSON_EXTRACT(b.publish_platform, '$.wx') = 1</if>
            <if test="platform == 2">AND JSON_EXTRACT(b.publish_platform, '$.ks') = 1</if>
            <if test="platform == 3">AND JSON_EXTRACT(b.publish_platform, '$.gzh') = 1</if>
            <if test="platform == 4">AND JSON_EXTRACT(b.publish_platform, '$.dn') = 1</if>
            <if test="bankTitle != null  and bankTitle != ''"> and c.title like concat('%', #{bankTitle}, '%')</if>
        </where>
        order by a.create_time desc
    </select>
    
    <select id="selectQCourseBankRelationById" parameterType="Long" resultMap="QCourseBankRelationResult">
        <include refid="selectQCourseBankRelationVo"/>
        where id = #{id}
    </select>
    <select id="countQuestionByBankId" resultType="java.lang.Integer">
        select count(*) from q_bank_question_relation where question_bank_id=#{questionBankId}
    </select>

    <insert id="insertQCourseBankRelation" parameterType="QCourseBankRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT ignore INTO q_course_bank_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="questionBankId != null">question_bank_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="questionBankId != null">#{questionBankId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQCourseBankRelation" parameterType="QCourseBankRelation">
        update q_course_bank_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="questionBankId != null">question_bank_id = #{questionBankId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQCourseBankRelationById" parameterType="Long">
        delete from q_course_bank_relation where id = #{id}
    </delete>

    <delete id="deleteQCourseBankRelationByIds">
        delete from q_course_bank_relation where teacher_id = #{teacherId} And id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteQCourseBankRelationByQBIdsAndCourseId">
        delete from q_course_bank_relation where teacher_id = #{teacherId} and course_id = #{courseId} and question_bank_id in
        <foreach item="qbId" collection="list" open="(" separator="," close=")">
            #{qbId}
        </foreach>
    </delete>
</mapper>