<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.CourseMachineAuditSubMapper">
    
    <resultMap type="CourseMachineAuditSub" id="CourseMachineAuditSubResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="metrialName"    column="metrial_name"    />
        <result property="metrialType"    column="metrial_type"    />
        <result property="sameMetrialName"    column="same_metrial_name"    />
        <result property="sameCourseName"    column="same_course_name"    />
        <result property="sameShopName"    column="same_shop_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="ppid"    column="ppid"    />
        <result property="courseDirectoryUrl"    column="course_directory_url"    />
    </resultMap>

    <sql id="selectCourseMachineAuditSubVo">
        select id, pid, metrial_name, metrial_type, same_metrial_name, same_course_name, same_shop_name, create_time, update_time, ppid, course_directory_url from course_machine_audit_sub
    </sql>

    <select id="selectCourseMachineAuditSubList" parameterType="CourseMachineAuditSub" resultMap="CourseMachineAuditSubResult">
        <include refid="selectCourseMachineAuditSubVo"/>
        <where>  
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="metrialName != null  and metrialName != ''"> and metrial_name like concat('%', #{metrialName}, '%')</if>
            <if test="metrialType != null "> and metrial_type = #{metrialType}</if>
            <if test="sameMetrialName != null  and sameMetrialName != ''"> and same_metrial_name like concat('%', #{sameMetrialName}, '%')</if>
            <if test="sameCourseName != null  and sameCourseName != ''"> and same_course_name like concat('%', #{sameCourseName}, '%')</if>
            <if test="sameShopName != null  and sameShopName != ''"> and same_shop_name like concat('%', #{sameShopName}, '%')</if>
            <if test="ppid != null "> and ppid = #{ppid}</if>
            <if test="courseDirectoryUrl != null  and courseDirectoryUrl != ''"> and course_directory_url = #{courseDirectoryUrl}</if>
        </where>
    </select>
    
    <select id="selectCourseMachineAuditSubById" parameterType="Long" resultMap="CourseMachineAuditSubResult">
        <include refid="selectCourseMachineAuditSubVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCourseMachineAuditSub" parameterType="CourseMachineAuditSub" useGeneratedKeys="true" keyProperty="id">
        insert into course_machine_audit_sub
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pid != null">pid,</if>
            <if test="metrialName != null">metrial_name,</if>
            <if test="metrialType != null">metrial_type,</if>
            <if test="sameMetrialName != null">same_metrial_name,</if>
            <if test="sameCourseName != null">same_course_name,</if>
            <if test="sameShopName != null">same_shop_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="ppid != null">ppid,</if>
            <if test="courseDirectoryUrl != null">course_directory_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pid != null">#{pid},</if>
            <if test="metrialName != null">#{metrialName},</if>
            <if test="metrialType != null">#{metrialType},</if>
            <if test="sameMetrialName != null">#{sameMetrialName},</if>
            <if test="sameCourseName != null">#{sameCourseName},</if>
            <if test="sameShopName != null">#{sameShopName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="ppid != null">#{ppid},</if>
            <if test="courseDirectoryUrl != null">#{courseDirectoryUrl},</if>
         </trim>
    </insert>

    <update id="updateCourseMachineAuditSub" parameterType="CourseMachineAuditSub">
        update course_machine_audit_sub
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="metrialName != null">metrial_name = #{metrialName},</if>
            <if test="metrialType != null">metrial_type = #{metrialType},</if>
            <if test="sameMetrialName != null">same_metrial_name = #{sameMetrialName},</if>
            <if test="sameCourseName != null">same_course_name = #{sameCourseName},</if>
            <if test="sameShopName != null">same_shop_name = #{sameShopName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="ppid != null">ppid = #{ppid},</if>
            <if test="courseDirectoryUrl != null">course_directory_url = #{courseDirectoryUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseMachineAuditSubById" parameterType="Long">
        delete from course_machine_audit_sub where id = #{id}
    </delete>

    <delete id="deleteCourseMachineAuditSubByIds" parameterType="String">
        delete from course_machine_audit_sub where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>