<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.WendaoLiveRoomWapMapper">
    
    <resultMap type="WendaoLiveRoomWap" id="WendaoLiveRoomWapResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="liveId"    column="live_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="groupAdmin"    column="group_admin"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWendaoLiveRoomWapVo">
        select id, teacher_id, live_id, group_id, group_name, group_admin, create_time, update_time from wendao_live_room_wap
    </sql>

    <select id="selectWendaoLiveRoomWapList" parameterType="WendaoLiveRoomWap" resultMap="WendaoLiveRoomWapResult">
        <include refid="selectWendaoLiveRoomWapVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="liveId != null "> and live_id = #{liveId}</if>
            <if test="groupId != null  and groupId != ''"> and group_id = #{groupId}</if>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="groupAdmin != null  and groupAdmin != ''"> and group_admin = #{groupAdmin}</if>
        </where>
    </select>
    
    <select id="selectWendaoLiveRoomWapById" parameterType="Long" resultMap="WendaoLiveRoomWapResult">
        <include refid="selectWendaoLiveRoomWapVo"/>
        where id = #{id}
    </select>
    <select id="selectWendaoLiveRoomWapByLiveId" resultMap="WendaoLiveRoomWapResult">
        <include refid="selectWendaoLiveRoomWapVo"/>
        where live_id = #{liveId}
    </select>

    <insert id="insertWendaoLiveRoomWap" parameterType="WendaoLiveRoomWap" useGeneratedKeys="true" keyProperty="id">
        insert into wendao_live_room_wap
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="liveId != null">live_id,</if>
            <if test="groupId != null">group_id,</if>
            <if test="groupName != null">group_name,</if>
            <if test="groupAdmin != null">group_admin,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="liveId != null">#{liveId},</if>
            <if test="groupId != null">#{groupId},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="groupAdmin != null">#{groupAdmin},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWendaoLiveRoomWap" parameterType="WendaoLiveRoomWap">
        update wendao_live_room_wap
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="liveId != null">live_id = #{liveId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="groupAdmin != null">group_admin = #{groupAdmin},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWendaoLiveRoomWapById" parameterType="Long">
        delete from wendao_live_room_wap where id = #{id}
    </delete>

    <delete id="deleteWendaoLiveRoomWapByIds" parameterType="String">
        delete from wendao_live_room_wap where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>