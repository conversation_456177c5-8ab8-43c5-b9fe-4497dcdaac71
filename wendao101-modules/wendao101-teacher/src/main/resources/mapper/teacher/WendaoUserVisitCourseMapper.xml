<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.WendaoUserVisitCourseMapper">
    
    <resultMap type="WendaoUserVisitCourse" id="WendaoUserVisitCourseResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="platform"    column="platform"    />
        <result property="anonymousOpenid"    column="anonymous_openid"    />
        <result property="openId"    column="open_id"    />
        <result property="unionId"    column="union_id"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="telNumber"    column="tel_number"    />
        <result property="userName"    column="user_name"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="visitTime"    column="visit_time"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="courseIdNumber"    column="course_id_number"    />
        <result property="coverPicUrl"    column="cover_pic_url"    />
        <result property="title"    column="title"    />
        <result property="price"    column="price"    />
        <result property="originalPrice"    column="original_price"    />
        <result property="publishPlatform"    column="publish_platform"    />
    </resultMap>

    <sql id="selectWendaoUserVisitCourseVo">
        select id, user_id, course_id, nick_name, platform, anonymous_openid, open_id, union_id, avatar_url, tel_number, user_name, app_name_type, visit_time, teacher_id, create_time, update_time, course_id_number, cover_pic_url, title, price, original_price, publish_platform from wendao_user_visit_course
    </sql>

    <select id="selectWendaoUserVisitCourseList" parameterType="WendaoUserVisitCourse" resultMap="WendaoUserVisitCourseResult">
        SELECT
        a.id,
        a.user_id,
        a.course_id,
        b.nick_name,
        b.platform,
        b.anonymous_openid,
        b.open_id,
        b.union_id,
        b.avatar_url,
        b.tel_number,
        b.user_name,
        b.app_name_type,
        a.visit_time,
        a.teacher_id,
        a.create_time,
        a.update_time,
        a.course_id_number,
        a.cover_pic_url,
        a.title,
        a.price,
        a.original_price,
        a.publish_platform
        FROM
        wendao_user_visit_course a
        LEFT JOIN wendao_user b ON a.user_id = b.id
        <where>  
            <if test="userId != null "> and a.user_id = #{userId}</if>
            <if test="courseId != null "> and a.course_id = #{courseId}</if>
            <if test="nickName != null  and nickName != ''"> and b.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="platform != null "> and b.platform = #{platform}</if>
            <if test="telNumber != null  and telNumber != ''"> and b.tel_number = #{telNumber}</if>
            <if test="userName != null  and userName != ''"> and b.user_name like concat('%', #{userName}, '%')</if>
            <if test="appNameType != null "> and b.app_name_type = #{appNameType}</if>
            <if test="visitTime != null "> and a.visit_time = #{visitTime}</if>
            <if test="teacherId != null "> and a.teacher_id = #{teacherId}</if>
            <if test="courseIdNumber != null "> and a.course_id_number = #{courseIdNumber}</if>
            <if test="title != null  and title != ''"> and a.title like concat('%', #{title}, '%')</if>
<!--            <if test="publishPlatform != null  and publishPlatform != ''"> and a.publish_platform = #{publishPlatform}</if>-->
<!--            <if test="platform == 1">AND JSON_EXTRACT(a.publish_platform, '$.dy') = 1</if>-->
<!--            <if test="platform == 3">AND JSON_EXTRACT(a.publish_platform, '$.wx') = 1</if>-->
<!--            <if test="platform == 2">AND JSON_EXTRACT(a.publish_platform, '$.ks') = 1</if>-->
        </where>
        order by a.visit_time desc
    </select>
    
    <select id="selectWendaoUserVisitCourseById" parameterType="Long" resultMap="WendaoUserVisitCourseResult">
        <include refid="selectWendaoUserVisitCourseVo"/>
        where id = #{id}
    </select>
    <select id="countGiveAwayCourseOrder" resultType="java.lang.Integer">
        select count(1) from `wendao101-order`.course_order where course_id = #{courseId} and buyer_user_id = #{userId} and order_platform = #{platform} and app_name_type = #{appNameType} and order_status = 1
    </select>

    <insert id="insertWendaoUserVisitCourse" parameterType="WendaoUserVisitCourse" useGeneratedKeys="true" keyProperty="id">
        insert into wendao_user_visit_course
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="platform != null">platform,</if>
            <if test="anonymousOpenid != null">anonymous_openid,</if>
            <if test="openId != null">open_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="telNumber != null">tel_number,</if>
            <if test="userName != null">user_name,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="visitTime != null">visit_time,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="courseIdNumber != null">course_id_number,</if>
            <if test="coverPicUrl != null">cover_pic_url,</if>
            <if test="title != null">title,</if>
            <if test="price != null">price,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="publishPlatform != null">publish_platform,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="platform != null">#{platform},</if>
            <if test="anonymousOpenid != null">#{anonymousOpenid},</if>
            <if test="openId != null">#{openId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="telNumber != null">#{telNumber},</if>
            <if test="userName != null">#{userName},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="visitTime != null">#{visitTime},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="courseIdNumber != null">#{courseIdNumber},</if>
            <if test="coverPicUrl != null">#{coverPicUrl},</if>
            <if test="title != null">#{title},</if>
            <if test="price != null">#{price},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="publishPlatform != null">#{publishPlatform},</if>
         </trim>
    </insert>

    <update id="updateWendaoUserVisitCourse" parameterType="WendaoUserVisitCourse">
        update wendao_user_visit_course
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="anonymousOpenid != null">anonymous_openid = #{anonymousOpenid},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="telNumber != null">tel_number = #{telNumber},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="visitTime != null">visit_time = #{visitTime},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="courseIdNumber != null">course_id_number = #{courseIdNumber},</if>
            <if test="coverPicUrl != null">cover_pic_url = #{coverPicUrl},</if>
            <if test="title != null">title = #{title},</if>
            <if test="price != null">price = #{price},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="publishPlatform != null">publish_platform = #{publishPlatform},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWendaoUserVisitCourseById" parameterType="Long">
        delete from wendao_user_visit_course where id = #{id}
    </delete>

    <delete id="deleteWendaoUserVisitCourseByIds" parameterType="String">
        delete from wendao_user_visit_course where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>