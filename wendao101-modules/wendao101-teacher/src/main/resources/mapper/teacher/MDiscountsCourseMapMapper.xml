<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.MDiscountsCourseMapMapper">
    
    <resultMap type="MDiscountsCourseMap" id="MDiscountsCourseMapResult">
        <result property="id"    column="id"    />
        <result property="discountsId"    column="discounts_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMDiscountsCourseMapVo">
        select id, discounts_id, course_id, create_time, update_time from m_discounts_course_map
    </sql>

    <select id="selectMDiscountsCourseMapList" parameterType="MDiscountsCourseMap" resultMap="MDiscountsCourseMapResult">
        <include refid="selectMDiscountsCourseMapVo"/>
        <where>  
            <if test="discountsId != null "> and discounts_id = #{discountsId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
        </where>
    </select>
    
    <select id="selectMDiscountsCourseMapById" parameterType="Long" resultMap="MDiscountsCourseMapResult">
        <include refid="selectMDiscountsCourseMapVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMDiscountsCourseMap" parameterType="MDiscountsCourseMap" useGeneratedKeys="true" keyProperty="id">
        insert into m_discounts_course_map
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="discountsId != null">discounts_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="discountsId != null">#{discountsId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMDiscountsCourseMap" parameterType="MDiscountsCourseMap">
        update m_discounts_course_map
        <trim prefix="SET" suffixOverrides=",">
            <if test="discountsId != null">discounts_id = #{discountsId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMDiscountsCourseMapById" parameterType="Long">
        delete from m_discounts_course_map where id = #{id}
    </delete>

    <delete id="deleteMDiscountsCourseMapByIds" parameterType="String">
        delete from m_discounts_course_map where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>