<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.WendaoStoreCanvasMapper">
    
    <resultMap type="WendaoStoreCanvas" id="WendaoStoreCanvasResult">
        <result property="canvasId"    column="canvas_id"    />
        <result property="terminal"    column="terminal"    />
        <result property="json"    column="json"    />
        <result property="type"    column="type"    />
        <result property="name"    column="name"    />
        <result property="shopId"    column="shop_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectWendaoStoreCanvasVo">
        select canvas_id, terminal, json, type, name, shop_id, create_time, update_time, is_delete from wendao_store_canvas
    </sql>

    <select id="selectWendaoStoreCanvasList" parameterType="WendaoStoreCanvas" resultMap="WendaoStoreCanvasResult">
        <include refid="selectWendaoStoreCanvasVo"/>
        <where>  
            <if test="terminal != null "> and terminal = #{terminal}</if>
            <if test="json != null  and json != ''"> and json = #{json}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
    </select>
    
    <select id="selectWendaoStoreCanvasByCanvasId" parameterType="Long" resultMap="WendaoStoreCanvasResult">
        <include refid="selectWendaoStoreCanvasVo"/>
        where canvas_id = #{canvasId}
    </select>
        
    <insert id="insertWendaoStoreCanvas" parameterType="WendaoStoreCanvas" useGeneratedKeys="true" keyProperty="canvasId">
        insert into wendao_store_canvas
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="terminal != null">terminal,</if>
            <if test="json != null">json,</if>
            <if test="type != null">type,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="terminal != null">#{terminal},</if>
            <if test="json != null">#{json},</if>
            <if test="type != null">#{type},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updateWendaoStoreCanvas" parameterType="WendaoStoreCanvas">
        update wendao_store_canvas
        <trim prefix="SET" suffixOverrides=",">
            <if test="terminal != null">terminal = #{terminal},</if>
            <if test="json != null">json = #{json},</if>
            <if test="type != null">type = #{type},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where canvas_id = #{canvasId}
    </update>

    <update id="deleteWendaoStoreCanvasByCanvasId">
        update wendao_store_canvas set is_delete=1 where canvas_id = #{canvasId} and shop_id = #{teacherId}
    </update>

    <update id="deleteWendaoStoreCanvasByCanvasIds">
        update wendao_store_canvas set is_delete=1 where shop_id = #{teacherId} and canvas_id in
        <foreach item="canvasId" collection="canvasIdList" open="(" separator="," close=")">
            #{canvasId}
        </foreach>
    </update>
</mapper>