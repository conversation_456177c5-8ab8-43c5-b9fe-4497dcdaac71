<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.WendaoUserGiftCourseOrderMapper">
    
    <resultMap type="WendaoUserGiftCourseOrder" id="WendaoUserGiftCourseOrderResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="buyerUserId"    column="buyer_user_id"    />
        <result property="buyerUserImg"    column="buyer_user_img"    />
        <result property="buyerUserName"    column="buyer_user_name"    />
        <result property="buyerUserMobile"    column="buyer_user_mobile"    />
        <result property="courseId"    column="course_id"    />
        <result property="courseDuration"    column="course_duration"    />
        <result property="courseImgUrl"    column="course_img_url"    />
        <result property="studyDuration"    column="study_duration"    />
        <result property="courseTitle"    column="course_title"    />
        <result property="coursePrice"    column="course_price"    />
        <result property="originalPrice"    column="original_price"    />
        <result property="payPrice"    column="pay_price"    />
        <result property="orderType"    column="order_type"    />
        <result property="orderPlatform"    column="order_platform"    />
        <result property="orderTime"    column="order_time"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWendaoUserGiftCourseOrderVo">
        select id, order_id, order_status, teacher_id, buyer_user_id, buyer_user_img, buyer_user_name, buyer_user_mobile, course_id, course_duration, course_img_url, study_duration, course_title, course_price, original_price, pay_price, order_type, order_platform, order_time, app_name_type, create_time, update_time from wendao_user_gift_course_order
    </sql>

    <select id="selectWendaoUserGiftCourseOrderList" parameterType="WendaoUserGiftCourseOrder" resultMap="WendaoUserGiftCourseOrderResult">
        <include refid="selectWendaoUserGiftCourseOrderVo"/>
        <where>  
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="orderStatus != null "> and order_status = #{orderStatus}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="buyerUserId != null "> and buyer_user_id = #{buyerUserId}</if>
            <if test="buyerUserImg != null  and buyerUserImg != ''"> and buyer_user_img = #{buyerUserImg}</if>
            <if test="buyerUserName != null  and buyerUserName != ''"> and buyer_user_name like concat('%', #{buyerUserName}, '%')</if>
            <if test="buyerUserMobile != null  and buyerUserMobile != ''"> and buyer_user_mobile = #{buyerUserMobile}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="courseDuration != null "> and course_duration = #{courseDuration}</if>
            <if test="courseImgUrl != null  and courseImgUrl != ''"> and course_img_url = #{courseImgUrl}</if>
            <if test="studyDuration != null "> and study_duration = #{studyDuration}</if>
            <if test="courseTitle != null  and courseTitle != ''"> and course_title = #{courseTitle}</if>
            <if test="coursePrice != null "> and course_price = #{coursePrice}</if>
            <if test="originalPrice != null "> and original_price = #{originalPrice}</if>
            <if test="payPrice != null "> and pay_price = #{payPrice}</if>
            <if test="orderType != null "> and order_type = #{orderType}</if>
            <if test="orderPlatform != null "> and order_platform = #{orderPlatform}</if>
            <if test="orderTime != null "> and order_time = #{orderTime}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWendaoUserGiftCourseOrderById" parameterType="Long" resultMap="WendaoUserGiftCourseOrderResult">
        <include refid="selectWendaoUserGiftCourseOrderVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWendaoUserGiftCourseOrder" parameterType="WendaoUserGiftCourseOrder" useGeneratedKeys="true" keyProperty="id">
        insert into wendao_user_gift_course_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="buyerUserId != null">buyer_user_id,</if>
            <if test="buyerUserImg != null">buyer_user_img,</if>
            <if test="buyerUserName != null">buyer_user_name,</if>
            <if test="buyerUserMobile != null">buyer_user_mobile,</if>
            <if test="courseId != null">course_id,</if>
            <if test="courseDuration != null">course_duration,</if>
            <if test="courseImgUrl != null">course_img_url,</if>
            <if test="studyDuration != null">study_duration,</if>
            <if test="courseTitle != null">course_title,</if>
            <if test="coursePrice != null">course_price,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="payPrice != null">pay_price,</if>
            <if test="orderType != null">order_type,</if>
            <if test="orderPlatform != null">order_platform,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="buyerUserId != null">#{buyerUserId},</if>
            <if test="buyerUserImg != null">#{buyerUserImg},</if>
            <if test="buyerUserName != null">#{buyerUserName},</if>
            <if test="buyerUserMobile != null">#{buyerUserMobile},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="courseDuration != null">#{courseDuration},</if>
            <if test="courseImgUrl != null">#{courseImgUrl},</if>
            <if test="studyDuration != null">#{studyDuration},</if>
            <if test="courseTitle != null">#{courseTitle},</if>
            <if test="coursePrice != null">#{coursePrice},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="payPrice != null">#{payPrice},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="orderPlatform != null">#{orderPlatform},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWendaoUserGiftCourseOrder" parameterType="WendaoUserGiftCourseOrder">
        update wendao_user_gift_course_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="buyerUserId != null">buyer_user_id = #{buyerUserId},</if>
            <if test="buyerUserImg != null">buyer_user_img = #{buyerUserImg},</if>
            <if test="buyerUserName != null">buyer_user_name = #{buyerUserName},</if>
            <if test="buyerUserMobile != null">buyer_user_mobile = #{buyerUserMobile},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="courseDuration != null">course_duration = #{courseDuration},</if>
            <if test="courseImgUrl != null">course_img_url = #{courseImgUrl},</if>
            <if test="studyDuration != null">study_duration = #{studyDuration},</if>
            <if test="courseTitle != null">course_title = #{courseTitle},</if>
            <if test="coursePrice != null">course_price = #{coursePrice},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="payPrice != null">pay_price = #{payPrice},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="orderPlatform != null">order_platform = #{orderPlatform},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWendaoUserGiftCourseOrderById" parameterType="Long">
        delete from wendao_user_gift_course_order where id = #{id}
    </delete>

    <delete id="deleteWendaoUserGiftCourseOrderByIds" parameterType="String">
        delete from wendao_user_gift_course_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>