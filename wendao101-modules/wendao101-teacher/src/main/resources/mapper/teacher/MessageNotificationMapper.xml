<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.MessageNotificationMapper">
    
    <resultMap type="MessageNotification" id="MessageNotificationResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="userId"    column="user_id"    />
        <result property="noticeTitle"    column="notice_title"    />
        <result property="noticeContent"    column="notice_content"    />
        <result property="noticeType"    column="notice_type"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="readStatus"    column="read_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMessageNotificationVo">
        select id, teacher_id, user_id, notice_title, notice_content, notice_type, is_delete, read_status, create_time, update_time from message_notification
    </sql>

    <select id="selectMessageNotificationList" parameterType="MessageNotification" resultMap="MessageNotificationResult">
        <include refid="selectMessageNotificationVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="noticeTitle != null  and noticeTitle != ''"> and notice_title = #{noticeTitle}</if>
            <if test="noticeContent != null  and noticeContent != ''"> and notice_content = #{noticeContent}</if>
            <if test="noticeType != null "> and notice_type = #{noticeType}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="readStatus != null "> and read_status = #{readStatus}</if>
        </where>
        ORDER BY create_time desc
    </select>

    <select id="selectMessageNotificationListCount" parameterType="MessageNotification" resultType="java.lang.Long">
        select count(1) from message_notification
        <where>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="noticeTitle != null  and noticeTitle != ''"> and notice_title = #{noticeTitle}</if>
            <if test="noticeContent != null  and noticeContent != ''"> and notice_content = #{noticeContent}</if>
            <if test="noticeType != null "> and notice_type = #{noticeType}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="readStatus != null "> and read_status = #{readStatus}</if>
        </where>
        ORDER BY create_time desc
    </select>
    
    <select id="selectMessageNotificationById" parameterType="Long" resultMap="MessageNotificationResult">
        <include refid="selectMessageNotificationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMessageNotification" parameterType="MessageNotification">
        insert into message_notification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="noticeTitle != null">notice_title,</if>
            <if test="noticeContent != null">notice_content,</if>
            <if test="noticeType != null">notice_type,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="readStatus != null">read_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="noticeTitle != null">#{noticeTitle},</if>
            <if test="noticeContent != null">#{noticeContent},</if>
            <if test="noticeType != null">#{noticeType},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="readStatus != null">#{readStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMessageNotification" parameterType="MessageNotification">
        update message_notification
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="noticeTitle != null">notice_title = #{noticeTitle},</if>
            <if test="noticeContent != null">notice_content = #{noticeContent},</if>
            <if test="noticeType != null">notice_type = #{noticeType},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="readStatus != null">read_status = #{readStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateMessageNotificationStatus" parameterType="MessageNotification">
        update message_notification
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="noticeTitle != null">notice_title = #{noticeTitle},</if>
            <if test="noticeContent != null">notice_content = #{noticeContent},</if>
            <if test="noticeType != null">notice_type = #{noticeType},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="readStatus != null">read_status = #{readStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where read_status = 0 and is_delete = 0 and teacher_id=#{teacherId}
    </update>

    <delete id="deleteMessageNotificationById" parameterType="Long">
        delete from message_notification where id = #{id}
    </delete>

    <delete id="deleteMessageNotificationByIds" parameterType="String">
        delete from message_notification where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>