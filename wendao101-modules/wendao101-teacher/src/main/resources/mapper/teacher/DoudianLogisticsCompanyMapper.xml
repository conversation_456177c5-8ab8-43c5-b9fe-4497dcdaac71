<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.DoudianLogisticsCompanyMapper">
    
    <resultMap type="DoudianLogisticsCompany" id="DoudianLogisticsCompanyResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
    </resultMap>

    <sql id="selectDoudianLogisticsCompanyVo">
        select id, name, code from `wendao101-order`.doudian_logistics_company
    </sql>

    <select id="selectDoudianLogisticsCompanyList" parameterType="DoudianLogisticsCompany" resultMap="DoudianLogisticsCompanyResult">
        <include refid="selectDoudianLogisticsCompanyVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
        </where>
    </select>
    
    <select id="selectDoudianLogisticsCompanyById" parameterType="Long" resultMap="DoudianLogisticsCompanyResult">
        <include refid="selectDoudianLogisticsCompanyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDoudianLogisticsCompany" parameterType="DoudianLogisticsCompany" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.doudian_logistics_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="code != null">code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="code != null">#{code},</if>
         </trim>
    </insert>

    <update id="updateDoudianLogisticsCompany" parameterType="DoudianLogisticsCompany">
        update `wendao101-order`.doudian_logistics_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDoudianLogisticsCompanyById" parameterType="Long">
        delete from `wendao101-order`.doudian_logistics_company where id = #{id}
    </delete>

    <delete id="deleteDoudianLogisticsCompanyByIds" parameterType="String">
        delete from `wendao101-order`.doudian_logistics_company where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>