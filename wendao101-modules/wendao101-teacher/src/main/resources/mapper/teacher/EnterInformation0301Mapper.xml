<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.EnterInformation0301Mapper">
    
    <resultMap type="EnterInformation0301" id="EnterInformation0301Result">
        <result property="id"    column="id"    />
        <result property="entityType"    column="entity_type"    />
        <result property="frontPath"    column="front_path"    />
        <result property="backPath"    column="back_path"    />
        <result property="realName"    column="real_name"    />
        <result property="idNumber"    column="id_number"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="businessLicenseCompanyName"    column="business_license_company_name"    />
        <result property="businessLicenseNo"    column="business_license_no"    />
        <result property="businessLicensePath"    column="business_license_path"    />
        <result property="telNum"    column="tel_num"    />
        <result property="courseForm"    column="course_form"    />
        <result property="firstClassId"    column="first_class_id"    />
        <result property="firstClassPid"    column="first_class_pid"    />
        <result property="firstClassTitle"    column="first_class_title"    />
        <result property="firstClassDouyinClassId"    column="first_class_douyin_class_id"    />
        <result property="secondClassId"    column="second_class_id"    />
        <result property="secondClassPid"    column="second_class_pid"    />
        <result property="secondClassTitle"    column="second_class_title"    />
        <result property="secondClassDouyinClassId"    column="second_class_douyin_class_id"    />
        <result property="shopNickname"    column="shop_nickname"    />
        <result property="shopAvatarUrl"    column="shop_avatar_url"    />
        <result property="shopDesc"    column="shop_desc"    />
        <result property="platform"    column="platform"    />
        <result property="dyAccount"    column="dy_account"    />
        <result property="dyUid"    column="dy_uid"    />
        <result property="ksAccount"    column="ks_account"    />
        <result property="wxAccount"    column="wx_account"    />
        <result property="sphAccount"    column="sph_account"    />
        <result property="serviceBeginTime"    column="service_begin_time"    />
        <result property="serviceEndTime"    column="service_end_time"    />
        <result property="version"    column="version"    />
        <result property="rateType"    column="rate_type"    />
        <result property="rate"    column="rate"    />
        <result property="accountSpecialist"    column="account_specialist"    />
        <result property="customerServiceSpecialist"    column="customer_service_specialist"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="shopAccount"    column="shop_account"    />
        <result property="shopId"    column="shop_id"    />
        <result property="auditType"    column="audit_type"    />
        <result property="rejectReason"    column="reject_reason"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="dyFansNum"    column="dy_fans_num"    />
        <result property="ksFansNum"    column="ks_fans_num"    />
        <result property="sphFansNum"    column="sph_fans_num"    />
        <result property="dyMasterImg"    column="dy_master_img"    />
        <result property="ksMasterImg"    column="ks_master_img"    />
        <result property="wxMasterImg"    column="wx_master_img"    />
        <result property="sphMasterImg"    column="sph_master_img"    />
        <result property="appNameType"    column="app_name_type"    />
    </resultMap>

    <sql id="selectEnterInformation0301Vo">
        select id, entity_type, front_path, back_path, real_name, id_number, teacher_name, business_license_company_name, business_license_no, business_license_path, tel_num, course_form, first_class_id, first_class_pid, first_class_title, first_class_douyin_class_id, second_class_id, second_class_pid, second_class_title, second_class_douyin_class_id, shop_nickname, shop_avatar_url, shop_desc, platform, dy_account, dy_uid, ks_account, wx_account, sph_account, service_begin_time, service_end_time, version, rate_type, rate, account_specialist, customer_service_specialist, create_time, update_time, shop_account, shop_id, audit_type, reject_reason, audit_time, dy_fans_num, ks_fans_num, sph_fans_num, dy_master_img, ks_master_img, wx_master_img, sph_master_img, app_name_type from `wendao-delete-0301`.enter_information_0301
    </sql>

    <select id="selectEnterInformation0301List" parameterType="EnterInformation0301" resultMap="EnterInformation0301Result">
        <include refid="selectEnterInformation0301Vo"/>
        <where>  
            <if test="entityType != null "> and entity_type = #{entityType}</if>
            <if test="frontPath != null  and frontPath != ''"> and front_path = #{frontPath}</if>
            <if test="backPath != null  and backPath != ''"> and back_path = #{backPath}</if>
            <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="idNumber != null  and idNumber != ''"> and id_number = #{idNumber}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="businessLicenseCompanyName != null  and businessLicenseCompanyName != ''"> and business_license_company_name like concat('%', #{businessLicenseCompanyName}, '%')</if>
            <if test="businessLicenseNo != null  and businessLicenseNo != ''"> and business_license_no = #{businessLicenseNo}</if>
            <if test="businessLicensePath != null  and businessLicensePath != ''"> and business_license_path = #{businessLicensePath}</if>
            <if test="telNum != null  and telNum != ''"> and tel_num = #{telNum}</if>
            <if test="courseForm != null "> and course_form = #{courseForm}</if>
            <if test="firstClassId != null "> and first_class_id = #{firstClassId}</if>
            <if test="firstClassPid != null "> and first_class_pid = #{firstClassPid}</if>
            <if test="firstClassTitle != null  and firstClassTitle != ''"> and first_class_title = #{firstClassTitle}</if>
            <if test="firstClassDouyinClassId != null "> and first_class_douyin_class_id = #{firstClassDouyinClassId}</if>
            <if test="secondClassId != null "> and second_class_id = #{secondClassId}</if>
            <if test="secondClassPid != null "> and second_class_pid = #{secondClassPid}</if>
            <if test="secondClassTitle != null  and secondClassTitle != ''"> and second_class_title = #{secondClassTitle}</if>
            <if test="secondClassDouyinClassId != null "> and second_class_douyin_class_id = #{secondClassDouyinClassId}</if>
            <if test="shopNickname != null  and shopNickname != ''"> and shop_nickname like concat('%', #{shopNickname}, '%')</if>
            <if test="shopAvatarUrl != null  and shopAvatarUrl != ''"> and shop_avatar_url = #{shopAvatarUrl}</if>
            <if test="shopDesc != null  and shopDesc != ''"> and shop_desc = #{shopDesc}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="dyAccount != null  and dyAccount != ''"> and dy_account = #{dyAccount}</if>
            <if test="dyUid != null  and dyUid != ''"> and dy_uid = #{dyUid}</if>
            <if test="ksAccount != null  and ksAccount != ''"> and ks_account = #{ksAccount}</if>
            <if test="wxAccount != null  and wxAccount != ''"> and wx_account = #{wxAccount}</if>
            <if test="sphAccount != null  and sphAccount != ''"> and sph_account = #{sphAccount}</if>
            <if test="serviceBeginTime != null "> and service_begin_time = #{serviceBeginTime}</if>
            <if test="serviceEndTime != null "> and service_end_time = #{serviceEndTime}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="rateType != null "> and rate_type = #{rateType}</if>
            <if test="rate != null "> and rate = #{rate}</if>
            <if test="accountSpecialist != null  and accountSpecialist != ''"> and account_specialist = #{accountSpecialist}</if>
            <if test="customerServiceSpecialist != null  and customerServiceSpecialist != ''"> and customer_service_specialist = #{customerServiceSpecialist}</if>
            <if test="shopAccount != null  and shopAccount != ''"> and shop_account = #{shopAccount}</if>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="auditType != null "> and audit_type = #{auditType}</if>
            <if test="rejectReason != null  and rejectReason != ''"> and reject_reason = #{rejectReason}</if>
            <if test="auditTime != null "> and audit_time = #{auditTime}</if>
            <if test="dyFansNum != null "> and dy_fans_num = #{dyFansNum}</if>
            <if test="ksFansNum != null "> and ks_fans_num = #{ksFansNum}</if>
            <if test="sphFansNum != null "> and sph_fans_num = #{sphFansNum}</if>
            <if test="dyMasterImg != null  and dyMasterImg != ''"> and dy_master_img = #{dyMasterImg}</if>
            <if test="ksMasterImg != null  and ksMasterImg != ''"> and ks_master_img = #{ksMasterImg}</if>
            <if test="wxMasterImg != null  and wxMasterImg != ''"> and wx_master_img = #{wxMasterImg}</if>
            <if test="sphMasterImg != null  and sphMasterImg != ''"> and sph_master_img = #{sphMasterImg}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
        </where>
    </select>
    
    <select id="selectEnterInformation0301ById" parameterType="Long" resultMap="EnterInformation0301Result">
        <include refid="selectEnterInformation0301Vo"/>
        where id = #{id}
    </select>
        
    <insert id="insertEnterInformation0301" parameterType="EnterInformation0301" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao-delete-0301`.enter_information_0301
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entityType != null">entity_type,</if>
            <if test="frontPath != null">front_path,</if>
            <if test="backPath != null">back_path,</if>
            <if test="realName != null">real_name,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="businessLicenseCompanyName != null">business_license_company_name,</if>
            <if test="businessLicenseNo != null">business_license_no,</if>
            <if test="businessLicensePath != null">business_license_path,</if>
            <if test="telNum != null">tel_num,</if>
            <if test="courseForm != null">course_form,</if>
            <if test="firstClassId != null">first_class_id,</if>
            <if test="firstClassPid != null">first_class_pid,</if>
            <if test="firstClassTitle != null">first_class_title,</if>
            <if test="firstClassDouyinClassId != null">first_class_douyin_class_id,</if>
            <if test="secondClassId != null">second_class_id,</if>
            <if test="secondClassPid != null">second_class_pid,</if>
            <if test="secondClassTitle != null">second_class_title,</if>
            <if test="secondClassDouyinClassId != null">second_class_douyin_class_id,</if>
            <if test="shopNickname != null">shop_nickname,</if>
            <if test="shopAvatarUrl != null">shop_avatar_url,</if>
            <if test="shopDesc != null">shop_desc,</if>
            <if test="platform != null">platform,</if>
            <if test="dyAccount != null">dy_account,</if>
            <if test="dyUid != null">dy_uid,</if>
            <if test="ksAccount != null">ks_account,</if>
            <if test="wxAccount != null">wx_account,</if>
            <if test="sphAccount != null">sph_account,</if>
            <if test="serviceBeginTime != null">service_begin_time,</if>
            <if test="serviceEndTime != null">service_end_time,</if>
            <if test="version != null">version,</if>
            <if test="rateType != null">rate_type,</if>
            <if test="rate != null">rate,</if>
            <if test="accountSpecialist != null">account_specialist,</if>
            <if test="customerServiceSpecialist != null">customer_service_specialist,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="shopAccount != null">shop_account,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="auditType != null">audit_type,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="dyFansNum != null">dy_fans_num,</if>
            <if test="ksFansNum != null">ks_fans_num,</if>
            <if test="sphFansNum != null">sph_fans_num,</if>
            <if test="dyMasterImg != null">dy_master_img,</if>
            <if test="ksMasterImg != null">ks_master_img,</if>
            <if test="wxMasterImg != null">wx_master_img,</if>
            <if test="sphMasterImg != null">sph_master_img,</if>
            <if test="appNameType != null">app_name_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entityType != null">#{entityType},</if>
            <if test="frontPath != null">#{frontPath},</if>
            <if test="backPath != null">#{backPath},</if>
            <if test="realName != null">#{realName},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="businessLicenseCompanyName != null">#{businessLicenseCompanyName},</if>
            <if test="businessLicenseNo != null">#{businessLicenseNo},</if>
            <if test="businessLicensePath != null">#{businessLicensePath},</if>
            <if test="telNum != null">#{telNum},</if>
            <if test="courseForm != null">#{courseForm},</if>
            <if test="firstClassId != null">#{firstClassId},</if>
            <if test="firstClassPid != null">#{firstClassPid},</if>
            <if test="firstClassTitle != null">#{firstClassTitle},</if>
            <if test="firstClassDouyinClassId != null">#{firstClassDouyinClassId},</if>
            <if test="secondClassId != null">#{secondClassId},</if>
            <if test="secondClassPid != null">#{secondClassPid},</if>
            <if test="secondClassTitle != null">#{secondClassTitle},</if>
            <if test="secondClassDouyinClassId != null">#{secondClassDouyinClassId},</if>
            <if test="shopNickname != null">#{shopNickname},</if>
            <if test="shopAvatarUrl != null">#{shopAvatarUrl},</if>
            <if test="shopDesc != null">#{shopDesc},</if>
            <if test="platform != null">#{platform},</if>
            <if test="dyAccount != null">#{dyAccount},</if>
            <if test="dyUid != null">#{dyUid},</if>
            <if test="ksAccount != null">#{ksAccount},</if>
            <if test="wxAccount != null">#{wxAccount},</if>
            <if test="sphAccount != null">#{sphAccount},</if>
            <if test="serviceBeginTime != null">#{serviceBeginTime},</if>
            <if test="serviceEndTime != null">#{serviceEndTime},</if>
            <if test="version != null">#{version},</if>
            <if test="rateType != null">#{rateType},</if>
            <if test="rate != null">#{rate},</if>
            <if test="accountSpecialist != null">#{accountSpecialist},</if>
            <if test="customerServiceSpecialist != null">#{customerServiceSpecialist},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="shopAccount != null">#{shopAccount},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="auditType != null">#{auditType},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="dyFansNum != null">#{dyFansNum},</if>
            <if test="ksFansNum != null">#{ksFansNum},</if>
            <if test="sphFansNum != null">#{sphFansNum},</if>
            <if test="dyMasterImg != null">#{dyMasterImg},</if>
            <if test="ksMasterImg != null">#{ksMasterImg},</if>
            <if test="wxMasterImg != null">#{wxMasterImg},</if>
            <if test="sphMasterImg != null">#{sphMasterImg},</if>
            <if test="appNameType != null">#{appNameType},</if>
         </trim>
    </insert>

    <update id="updateEnterInformation0301" parameterType="EnterInformation0301">
        update `wendao-delete-0301`.enter_information_0301
        <trim prefix="SET" suffixOverrides=",">
            <if test="entityType != null">entity_type = #{entityType},</if>
            <if test="frontPath != null">front_path = #{frontPath},</if>
            <if test="backPath != null">back_path = #{backPath},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="businessLicenseCompanyName != null">business_license_company_name = #{businessLicenseCompanyName},</if>
            <if test="businessLicenseNo != null">business_license_no = #{businessLicenseNo},</if>
            <if test="businessLicensePath != null">business_license_path = #{businessLicensePath},</if>
            <if test="telNum != null">tel_num = #{telNum},</if>
            <if test="courseForm != null">course_form = #{courseForm},</if>
            <if test="firstClassId != null">first_class_id = #{firstClassId},</if>
            <if test="firstClassPid != null">first_class_pid = #{firstClassPid},</if>
            <if test="firstClassTitle != null">first_class_title = #{firstClassTitle},</if>
            <if test="firstClassDouyinClassId != null">first_class_douyin_class_id = #{firstClassDouyinClassId},</if>
            <if test="secondClassId != null">second_class_id = #{secondClassId},</if>
            <if test="secondClassPid != null">second_class_pid = #{secondClassPid},</if>
            <if test="secondClassTitle != null">second_class_title = #{secondClassTitle},</if>
            <if test="secondClassDouyinClassId != null">second_class_douyin_class_id = #{secondClassDouyinClassId},</if>
            <if test="shopNickname != null">shop_nickname = #{shopNickname},</if>
            <if test="shopAvatarUrl != null">shop_avatar_url = #{shopAvatarUrl},</if>
            <if test="shopDesc != null">shop_desc = #{shopDesc},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="dyAccount != null">dy_account = #{dyAccount},</if>
            <if test="dyUid != null">dy_uid = #{dyUid},</if>
            <if test="ksAccount != null">ks_account = #{ksAccount},</if>
            <if test="wxAccount != null">wx_account = #{wxAccount},</if>
            <if test="sphAccount != null">sph_account = #{sphAccount},</if>
            <if test="serviceBeginTime != null">service_begin_time = #{serviceBeginTime},</if>
            <if test="serviceEndTime != null">service_end_time = #{serviceEndTime},</if>
            <if test="version != null">version = #{version},</if>
            <if test="rateType != null">rate_type = #{rateType},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="accountSpecialist != null">account_specialist = #{accountSpecialist},</if>
            <if test="customerServiceSpecialist != null">customer_service_specialist = #{customerServiceSpecialist},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="shopAccount != null">shop_account = #{shopAccount},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="auditType != null">audit_type = #{auditType},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="dyFansNum != null">dy_fans_num = #{dyFansNum},</if>
            <if test="ksFansNum != null">ks_fans_num = #{ksFansNum},</if>
            <if test="sphFansNum != null">sph_fans_num = #{sphFansNum},</if>
            <if test="dyMasterImg != null">dy_master_img = #{dyMasterImg},</if>
            <if test="ksMasterImg != null">ks_master_img = #{ksMasterImg},</if>
            <if test="wxMasterImg != null">wx_master_img = #{wxMasterImg},</if>
            <if test="sphMasterImg != null">sph_master_img = #{sphMasterImg},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEnterInformation0301ById" parameterType="Long">
        delete from `wendao-delete-0301`.enter_information_0301 where id = #{id}
    </delete>

    <delete id="deleteEnterInformation0301ByIds" parameterType="String">
        delete from `wendao-delete-0301`.enter_information_0301 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>