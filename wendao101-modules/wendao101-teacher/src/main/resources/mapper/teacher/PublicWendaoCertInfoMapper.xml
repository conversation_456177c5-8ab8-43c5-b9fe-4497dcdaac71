<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.PublicWendaoCertInfoMapper">
    
    <resultMap type="PublicWendaoCertInfo" id="PublicWendaoCertInfoResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="entityType"    column="entity_type"    />
        <result property="frontPath"    column="front_path"    />
        <result property="backPath"    column="back_path"    />
        <result property="idNumber"    column="id_number"    />
        <result property="teacherRealName"    column="teacher_real_name"    />
        <result property="idCardExpireTime"    column="id_card_expire_time"    />
        <result property="avatarPath"    column="avatar_path"    />
        <result property="nickName"    column="nick_name"    />
        <result property="teacherDesc"    column="teacher_desc"    />
        <result property="businessLicenseNo"    column="business_license_no"    />
        <result property="businessLicenseExpireTime"    column="business_license_expire_time"    />
        <result property="businessLicenseCompanyName"    column="business_license_company_name"    />
        <result property="businessLicensePath"    column="business_license_path"    />
        <result property="legalPersonFrontPath"    column="legal_person_front_path"    />
        <result property="legalPersonBackPath"    column="legal_person_back_path"    />
        <result property="legalPersonName"    column="legal_person_name"    />
        <result property="legalPersonIdNumber"    column="legal_person_id_number"    />
        <result property="legalPersonIdCardExpireTime"    column="legal_person_id_card_expire_time"    />
        <result property="institutionSceneType"    column="institution_scene_type"    />
        <result property="institutionSubjectType"    column="institution_subject_type"    />
        <result property="firstClassId"    column="first_class_id"    />
        <result property="firstClassPid"    column="first_class_pid"    />
        <result property="firstClassTitle"    column="first_class_title"    />
        <result property="firstClassDouyinClassId"    column="first_class_douyin_class_id"    />
        <result property="secondClassId"    column="second_class_id"    />
        <result property="secondClassPid"    column="second_class_pid"    />
        <result property="secondClassTitle"    column="second_class_title"    />
        <result property="secondClassDouyinClassId"    column="second_class_douyin_class_id"    />
        <result property="certificateType"    column="certificate_type"    />
        <result property="certificatePath"    column="certificate_path"    />
        <result property="certificateExpireTime"    column="certificate_expire_time"    />
        <result property="letterOfAuthorizationPath"    column="letter_of_authorization_path"    />
        <result property="letterExpireTime"    column="letter_expire_time"    />
        <result property="classTeacherFrontPath"    column="class_teacher_front_path"    />
        <result property="classTeacherBackPath"    column="class_teacher_back_path"    />
        <result property="classTeacherName"    column="class_teacher_name"    />
        <result property="classTeacherIdNumber"    column="class_teacher_id_number"    />
        <result property="classTeacherIdCardExpireTime"    column="class_teacher_id_card_expire_time"    />
        <result property="cooperationStatementPath"    column="cooperation_statement_path"    />
        <result property="cooperationStatementExpireTime"    column="cooperation_statement_expire_time"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="jobStatus"    column="job_status"    />
        <result property="douyinAuditMessage"    column="douyin_audit_message"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="basicAuthTaskid"    column="basic_auth_taskid"    />
        <result property="classAuthTaskid"    column="class_auth_taskid"    />
        <result property="entityId"    column="entity_id"    />
        <result property="errMsg"    column="err_msg"    />
        <result property="errCode"    column="err_code"    />
        <result property="authRoleStatus"    column="auth_role_status"    />
        <result property="addRoleTaskid"    column="add_role_taskid"    />
    </resultMap>

    <sql id="selectPublicWendaoCertInfoVo">
        select id, teacher_id, entity_type, front_path, back_path, id_number, teacher_real_name, id_card_expire_time, avatar_path, nick_name, teacher_desc, business_license_no, business_license_expire_time, business_license_company_name, business_license_path, legal_person_front_path, legal_person_back_path, legal_person_name, legal_person_id_number, legal_person_id_card_expire_time, institution_scene_type, institution_subject_type, first_class_id, first_class_pid, first_class_title, first_class_douyin_class_id, second_class_id, second_class_pid, second_class_title, second_class_douyin_class_id, certificate_type, certificate_path, certificate_expire_time, letter_of_authorization_path, letter_expire_time, class_teacher_front_path, class_teacher_back_path, class_teacher_name, class_teacher_id_number, class_teacher_id_card_expire_time, cooperation_statement_path, cooperation_statement_expire_time, audit_status, job_status, douyin_audit_message, create_time, update_time, basic_auth_taskid, class_auth_taskid, entity_id, err_msg, err_code, auth_role_status, add_role_taskid from public_wendao_cert_info
    </sql>

    <select id="selectPublicWendaoCertInfoList" parameterType="PublicWendaoCertInfo" resultMap="PublicWendaoCertInfoResult">
        <include refid="selectPublicWendaoCertInfoVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="entityType != null "> and entity_type = #{entityType}</if>
            <if test="frontPath != null  and frontPath != ''"> and front_path = #{frontPath}</if>
            <if test="backPath != null  and backPath != ''"> and back_path = #{backPath}</if>
            <if test="idNumber != null  and idNumber != ''"> and id_number = #{idNumber}</if>
            <if test="teacherRealName != null  and teacherRealName != ''"> and teacher_real_name like concat('%', #{teacherRealName}, '%')</if>
            <if test="idCardExpireTime != null  and idCardExpireTime != ''"> and id_card_expire_time = #{idCardExpireTime}</if>
            <if test="avatarPath != null  and avatarPath != ''"> and avatar_path = #{avatarPath}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="teacherDesc != null  and teacherDesc != ''"> and teacher_desc = #{teacherDesc}</if>
            <if test="businessLicenseNo != null  and businessLicenseNo != ''"> and business_license_no = #{businessLicenseNo}</if>
            <if test="businessLicenseExpireTime != null  and businessLicenseExpireTime != ''"> and business_license_expire_time = #{businessLicenseExpireTime}</if>
            <if test="businessLicenseCompanyName != null  and businessLicenseCompanyName != ''"> and business_license_company_name like concat('%', #{businessLicenseCompanyName}, '%')</if>
            <if test="businessLicensePath != null  and businessLicensePath != ''"> and business_license_path = #{businessLicensePath}</if>
            <if test="legalPersonFrontPath != null  and legalPersonFrontPath != ''"> and legal_person_front_path = #{legalPersonFrontPath}</if>
            <if test="legalPersonBackPath != null  and legalPersonBackPath != ''"> and legal_person_back_path = #{legalPersonBackPath}</if>
            <if test="legalPersonName != null  and legalPersonName != ''"> and legal_person_name like concat('%', #{legalPersonName}, '%')</if>
            <if test="legalPersonIdNumber != null  and legalPersonIdNumber != ''"> and legal_person_id_number = #{legalPersonIdNumber}</if>
            <if test="legalPersonIdCardExpireTime != null  and legalPersonIdCardExpireTime != ''"> and legal_person_id_card_expire_time = #{legalPersonIdCardExpireTime}</if>
            <if test="institutionSceneType != null  and institutionSceneType != ''"> and institution_scene_type = #{institutionSceneType}</if>
            <if test="institutionSubjectType != null  and institutionSubjectType != ''"> and institution_subject_type = #{institutionSubjectType}</if>
            <if test="firstClassId != null "> and first_class_id = #{firstClassId}</if>
            <if test="firstClassPid != null "> and first_class_pid = #{firstClassPid}</if>
            <if test="firstClassTitle != null  and firstClassTitle != ''"> and first_class_title = #{firstClassTitle}</if>
            <if test="firstClassDouyinClassId != null "> and first_class_douyin_class_id = #{firstClassDouyinClassId}</if>
            <if test="secondClassId != null "> and second_class_id = #{secondClassId}</if>
            <if test="secondClassPid != null "> and second_class_pid = #{secondClassPid}</if>
            <if test="secondClassTitle != null  and secondClassTitle != ''"> and second_class_title = #{secondClassTitle}</if>
            <if test="secondClassDouyinClassId != null "> and second_class_douyin_class_id = #{secondClassDouyinClassId}</if>
            <if test="certificateType != null "> and certificate_type = #{certificateType}</if>
            <if test="certificatePath != null  and certificatePath != ''"> and certificate_path = #{certificatePath}</if>
            <if test="certificateExpireTime != null  and certificateExpireTime != ''"> and certificate_expire_time = #{certificateExpireTime}</if>
            <if test="letterOfAuthorizationPath != null  and letterOfAuthorizationPath != ''"> and letter_of_authorization_path = #{letterOfAuthorizationPath}</if>
            <if test="letterExpireTime != null  and letterExpireTime != ''"> and letter_expire_time = #{letterExpireTime}</if>
            <if test="classTeacherFrontPath != null  and classTeacherFrontPath != ''"> and class_teacher_front_path = #{classTeacherFrontPath}</if>
            <if test="classTeacherBackPath != null  and classTeacherBackPath != ''"> and class_teacher_back_path = #{classTeacherBackPath}</if>
            <if test="classTeacherName != null  and classTeacherName != ''"> and class_teacher_name like concat('%', #{classTeacherName}, '%')</if>
            <if test="classTeacherIdNumber != null  and classTeacherIdNumber != ''"> and class_teacher_id_number = #{classTeacherIdNumber}</if>
            <if test="classTeacherIdCardExpireTime != null  and classTeacherIdCardExpireTime != ''"> and class_teacher_id_card_expire_time = #{classTeacherIdCardExpireTime}</if>
            <if test="cooperationStatementPath != null  and cooperationStatementPath != ''"> and cooperation_statement_path = #{cooperationStatementPath}</if>
            <if test="cooperationStatementExpireTime != null  and cooperationStatementExpireTime != ''"> and cooperation_statement_expire_time = #{cooperationStatementExpireTime}</if>
            <if test="auditStatus != null "> and audit_status = #{auditStatus}</if>
            <if test="jobStatus != null "> and job_status = #{jobStatus}</if>
            <if test="douyinAuditMessage != null  and douyinAuditMessage != ''"> and douyin_audit_message = #{douyinAuditMessage}</if>
            <if test="basicAuthTaskid != null  and basicAuthTaskid != ''"> and basic_auth_taskid = #{basicAuthTaskid}</if>
            <if test="classAuthTaskid != null  and classAuthTaskid != ''"> and class_auth_taskid = #{classAuthTaskid}</if>
            <if test="entityId != null  and entityId != ''"> and entity_id = #{entityId}</if>
            <if test="errMsg != null  and errMsg != ''"> and err_msg = #{errMsg}</if>
            <if test="errCode != null "> and err_code = #{errCode}</if>
            <if test="authRoleStatus != null "> and auth_role_status = #{authRoleStatus}</if>
            <if test="addRoleTaskid != null  and addRoleTaskid != ''"> and add_role_taskid = #{addRoleTaskid}</if>
        </where>
    </select>
    
    <select id="selectPublicWendaoCertInfoById" parameterType="Long" resultMap="PublicWendaoCertInfoResult">
        <include refid="selectPublicWendaoCertInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectDistinctNameNickName" resultMap="PublicWendaoCertInfoResult">
        select distinct business_license_company_name,nick_name from public_wendao_cert_info where business_license_company_name=#{businessLicenseCompanyName} limit 1
    </select>

    <insert id="insertPublicWendaoCertInfo" parameterType="PublicWendaoCertInfo">
        insert into public_wendao_cert_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="entityType != null">entity_type,</if>
            <if test="frontPath != null">front_path,</if>
            <if test="backPath != null">back_path,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="teacherRealName != null">teacher_real_name,</if>
            <if test="idCardExpireTime != null">id_card_expire_time,</if>
            <if test="avatarPath != null">avatar_path,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="teacherDesc != null">teacher_desc,</if>
            <if test="businessLicenseNo != null">business_license_no,</if>
            <if test="businessLicenseExpireTime != null">business_license_expire_time,</if>
            <if test="businessLicenseCompanyName != null">business_license_company_name,</if>
            <if test="businessLicensePath != null">business_license_path,</if>
            <if test="legalPersonFrontPath != null">legal_person_front_path,</if>
            <if test="legalPersonBackPath != null">legal_person_back_path,</if>
            <if test="legalPersonName != null">legal_person_name,</if>
            <if test="legalPersonIdNumber != null">legal_person_id_number,</if>
            <if test="legalPersonIdCardExpireTime != null">legal_person_id_card_expire_time,</if>
            <if test="institutionSceneType != null">institution_scene_type,</if>
            <if test="institutionSubjectType != null">institution_subject_type,</if>
            <if test="firstClassId != null">first_class_id,</if>
            <if test="firstClassPid != null">first_class_pid,</if>
            <if test="firstClassTitle != null">first_class_title,</if>
            <if test="firstClassDouyinClassId != null">first_class_douyin_class_id,</if>
            <if test="secondClassId != null">second_class_id,</if>
            <if test="secondClassPid != null">second_class_pid,</if>
            <if test="secondClassTitle != null">second_class_title,</if>
            <if test="secondClassDouyinClassId != null">second_class_douyin_class_id,</if>
            <if test="certificateType != null">certificate_type,</if>
            <if test="certificatePath != null">certificate_path,</if>
            <if test="certificateExpireTime != null">certificate_expire_time,</if>
            <if test="letterOfAuthorizationPath != null">letter_of_authorization_path,</if>
            <if test="letterExpireTime != null">letter_expire_time,</if>
            <if test="classTeacherFrontPath != null">class_teacher_front_path,</if>
            <if test="classTeacherBackPath != null">class_teacher_back_path,</if>
            <if test="classTeacherName != null">class_teacher_name,</if>
            <if test="classTeacherIdNumber != null">class_teacher_id_number,</if>
            <if test="classTeacherIdCardExpireTime != null">class_teacher_id_card_expire_time,</if>
            <if test="cooperationStatementPath != null">cooperation_statement_path,</if>
            <if test="cooperationStatementExpireTime != null">cooperation_statement_expire_time,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="jobStatus != null">job_status,</if>
            <if test="douyinAuditMessage != null">douyin_audit_message,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="basicAuthTaskid != null">basic_auth_taskid,</if>
            <if test="classAuthTaskid != null">class_auth_taskid,</if>
            <if test="entityId != null">entity_id,</if>
            <if test="errMsg != null">err_msg,</if>
            <if test="errCode != null">err_code,</if>
            <if test="authRoleStatus != null">auth_role_status,</if>
            <if test="addRoleTaskid != null">add_role_taskid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="entityType != null">#{entityType},</if>
            <if test="frontPath != null">#{frontPath},</if>
            <if test="backPath != null">#{backPath},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="teacherRealName != null">#{teacherRealName},</if>
            <if test="idCardExpireTime != null">#{idCardExpireTime},</if>
            <if test="avatarPath != null">#{avatarPath},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="teacherDesc != null">#{teacherDesc},</if>
            <if test="businessLicenseNo != null">#{businessLicenseNo},</if>
            <if test="businessLicenseExpireTime != null">#{businessLicenseExpireTime},</if>
            <if test="businessLicenseCompanyName != null">#{businessLicenseCompanyName},</if>
            <if test="businessLicensePath != null">#{businessLicensePath},</if>
            <if test="legalPersonFrontPath != null">#{legalPersonFrontPath},</if>
            <if test="legalPersonBackPath != null">#{legalPersonBackPath},</if>
            <if test="legalPersonName != null">#{legalPersonName},</if>
            <if test="legalPersonIdNumber != null">#{legalPersonIdNumber},</if>
            <if test="legalPersonIdCardExpireTime != null">#{legalPersonIdCardExpireTime},</if>
            <if test="institutionSceneType != null">#{institutionSceneType},</if>
            <if test="institutionSubjectType != null">#{institutionSubjectType},</if>
            <if test="firstClassId != null">#{firstClassId},</if>
            <if test="firstClassPid != null">#{firstClassPid},</if>
            <if test="firstClassTitle != null">#{firstClassTitle},</if>
            <if test="firstClassDouyinClassId != null">#{firstClassDouyinClassId},</if>
            <if test="secondClassId != null">#{secondClassId},</if>
            <if test="secondClassPid != null">#{secondClassPid},</if>
            <if test="secondClassTitle != null">#{secondClassTitle},</if>
            <if test="secondClassDouyinClassId != null">#{secondClassDouyinClassId},</if>
            <if test="certificateType != null">#{certificateType},</if>
            <if test="certificatePath != null">#{certificatePath},</if>
            <if test="certificateExpireTime != null">#{certificateExpireTime},</if>
            <if test="letterOfAuthorizationPath != null">#{letterOfAuthorizationPath},</if>
            <if test="letterExpireTime != null">#{letterExpireTime},</if>
            <if test="classTeacherFrontPath != null">#{classTeacherFrontPath},</if>
            <if test="classTeacherBackPath != null">#{classTeacherBackPath},</if>
            <if test="classTeacherName != null">#{classTeacherName},</if>
            <if test="classTeacherIdNumber != null">#{classTeacherIdNumber},</if>
            <if test="classTeacherIdCardExpireTime != null">#{classTeacherIdCardExpireTime},</if>
            <if test="cooperationStatementPath != null">#{cooperationStatementPath},</if>
            <if test="cooperationStatementExpireTime != null">#{cooperationStatementExpireTime},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="jobStatus != null">#{jobStatus},</if>
            <if test="douyinAuditMessage != null">#{douyinAuditMessage},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="basicAuthTaskid != null">#{basicAuthTaskid},</if>
            <if test="classAuthTaskid != null">#{classAuthTaskid},</if>
            <if test="entityId != null">#{entityId},</if>
            <if test="errMsg != null">#{errMsg},</if>
            <if test="errCode != null">#{errCode},</if>
            <if test="authRoleStatus != null">#{authRoleStatus},</if>
            <if test="addRoleTaskid != null">#{addRoleTaskid},</if>
         </trim>
        <selectKey resultType="Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>

    <update id="updatePublicWendaoCertInfo" parameterType="PublicWendaoCertInfo">
        update public_wendao_cert_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="entityType != null">entity_type = #{entityType},</if>
            <if test="frontPath != null">front_path = #{frontPath},</if>
            <if test="backPath != null">back_path = #{backPath},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="teacherRealName != null">teacher_real_name = #{teacherRealName},</if>
            <if test="idCardExpireTime != null">id_card_expire_time = #{idCardExpireTime},</if>
            <if test="avatarPath != null">avatar_path = #{avatarPath},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="teacherDesc != null">teacher_desc = #{teacherDesc},</if>
            <if test="businessLicenseNo != null">business_license_no = #{businessLicenseNo},</if>
            <if test="businessLicenseExpireTime != null">business_license_expire_time = #{businessLicenseExpireTime},</if>
            <if test="businessLicenseCompanyName != null">business_license_company_name = #{businessLicenseCompanyName},</if>
            <if test="businessLicensePath != null">business_license_path = #{businessLicensePath},</if>
            <if test="legalPersonFrontPath != null">legal_person_front_path = #{legalPersonFrontPath},</if>
            <if test="legalPersonBackPath != null">legal_person_back_path = #{legalPersonBackPath},</if>
            <if test="legalPersonName != null">legal_person_name = #{legalPersonName},</if>
            <if test="legalPersonIdNumber != null">legal_person_id_number = #{legalPersonIdNumber},</if>
            <if test="legalPersonIdCardExpireTime != null">legal_person_id_card_expire_time = #{legalPersonIdCardExpireTime},</if>
            <if test="institutionSceneType != null">institution_scene_type = #{institutionSceneType},</if>
            <if test="institutionSubjectType != null">institution_subject_type = #{institutionSubjectType},</if>
            <if test="firstClassId != null">first_class_id = #{firstClassId},</if>
            <if test="firstClassPid != null">first_class_pid = #{firstClassPid},</if>
            <if test="firstClassTitle != null">first_class_title = #{firstClassTitle},</if>
            <if test="firstClassDouyinClassId != null">first_class_douyin_class_id = #{firstClassDouyinClassId},</if>
            <if test="secondClassId != null">second_class_id = #{secondClassId},</if>
            <if test="secondClassPid != null">second_class_pid = #{secondClassPid},</if>
            <if test="secondClassTitle != null">second_class_title = #{secondClassTitle},</if>
            <if test="secondClassDouyinClassId != null">second_class_douyin_class_id = #{secondClassDouyinClassId},</if>
            <if test="certificateType != null">certificate_type = #{certificateType},</if>
            <if test="certificatePath != null">certificate_path = #{certificatePath},</if>
            <if test="certificateExpireTime != null">certificate_expire_time = #{certificateExpireTime},</if>
            <if test="letterOfAuthorizationPath != null">letter_of_authorization_path = #{letterOfAuthorizationPath},</if>
            <if test="letterExpireTime != null">letter_expire_time = #{letterExpireTime},</if>
            <if test="classTeacherFrontPath != null">class_teacher_front_path = #{classTeacherFrontPath},</if>
            <if test="classTeacherBackPath != null">class_teacher_back_path = #{classTeacherBackPath},</if>
            <if test="classTeacherName != null">class_teacher_name = #{classTeacherName},</if>
            <if test="classTeacherIdNumber != null">class_teacher_id_number = #{classTeacherIdNumber},</if>
            <if test="classTeacherIdCardExpireTime != null">class_teacher_id_card_expire_time = #{classTeacherIdCardExpireTime},</if>
            <if test="cooperationStatementPath != null">cooperation_statement_path = #{cooperationStatementPath},</if>
            <if test="cooperationStatementExpireTime != null">cooperation_statement_expire_time = #{cooperationStatementExpireTime},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="jobStatus != null">job_status = #{jobStatus},</if>
            <if test="douyinAuditMessage != null">douyin_audit_message = #{douyinAuditMessage},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="basicAuthTaskid != null">basic_auth_taskid = #{basicAuthTaskid},</if>
            <if test="classAuthTaskid != null">class_auth_taskid = #{classAuthTaskid},</if>
            <if test="entityId != null">entity_id = #{entityId},</if>
            <if test="errMsg != null">err_msg = #{errMsg},</if>
            <if test="errCode != null">err_code = #{errCode},</if>
            <if test="authRoleStatus != null">auth_role_status = #{authRoleStatus},</if>
            <if test="addRoleTaskid != null">add_role_taskid = #{addRoleTaskid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePublicWendaoCertInfoById" parameterType="Long">
        delete from public_wendao_cert_info where id = #{id}
    </delete>

    <delete id="deletePublicWendaoCertInfoByIds" parameterType="String">
        delete from public_wendao_cert_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>