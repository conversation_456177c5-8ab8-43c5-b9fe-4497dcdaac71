<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TeacherCourseTemplateMapper">
    
    <resultMap type="TeacherCourseTemplate" id="TeacherCourseTemplateResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="courseTemplateId"    column="course_template_id"    />
        <result property="courseCategoryId"    column="course_category_id"    />
        <result property="type"    column="type"    />
        <result property="coverPicUrl"    column="cover_pic_url"    />
        <result property="templateContent"    column="template_content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="platform"    column="platform"    />
        <result property="name"    column="name"    />
    </resultMap>

    <sql id="selectTeacherCourseTemplateVo">
        select id, teacher_id, course_id, course_template_id, course_category_id, type, cover_pic_url, template_content, create_time, update_time, platform, name from teacher_course_template
    </sql>

    <select id="selectTeacherCourseTemplateList" parameterType="TeacherCourseTemplate" resultMap="TeacherCourseTemplateResult">
        <include refid="selectTeacherCourseTemplateVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="courseTemplateId != null "> and course_template_id = #{courseTemplateId}</if>
            <if test="courseCategoryId != null "> and course_category_id = #{courseCategoryId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="coverPicUrl != null  and coverPicUrl != ''"> and cover_pic_url = #{coverPicUrl}</if>
            <if test="platform != null "> and platform = #{platform}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectTeacherCourseTemplateById" parameterType="Long" resultMap="TeacherCourseTemplateResult">
        <include refid="selectTeacherCourseTemplateVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTeacherCourseTemplate" parameterType="TeacherCourseTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into teacher_course_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="courseTemplateId != null">course_template_id,</if>
            <if test="courseCategoryId != null">course_category_id,</if>
            <if test="type != null">type,</if>
            <if test="coverPicUrl != null">cover_pic_url,</if>
            <if test="templateContent != null">template_content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="platform != null">platform,</if>
            <if test="name != null">name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="courseTemplateId != null">#{courseTemplateId},</if>
            <if test="courseCategoryId != null">#{courseCategoryId},</if>
            <if test="type != null">#{type},</if>
            <if test="coverPicUrl != null">#{coverPicUrl},</if>
            <if test="templateContent != null">#{templateContent},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="platform != null">#{platform},</if>
            <if test="name != null">#{name},</if>
         </trim>
    </insert>

    <update id="updateTeacherCourseTemplate" parameterType="TeacherCourseTemplate">
        update teacher_course_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="courseTemplateId != null">course_template_id = #{courseTemplateId},</if>
            <if test="courseCategoryId != null">course_category_id = #{courseCategoryId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coverPicUrl != null">cover_pic_url = #{coverPicUrl},</if>
            <if test="templateContent != null">template_content = #{templateContent},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="name != null">name = #{name},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherCourseTemplateById" parameterType="Long">
        delete from teacher_course_template where id = #{id}
    </delete>

    <delete id="deleteTeacherCourseTemplateByIds" parameterType="String">
        delete from teacher_course_template where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 