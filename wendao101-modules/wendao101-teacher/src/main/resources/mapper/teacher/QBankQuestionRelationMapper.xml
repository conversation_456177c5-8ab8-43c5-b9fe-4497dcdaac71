<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.QBankQuestionRelationMapper">
    
    <resultMap type="QBankQuestionRelation" id="QBankQuestionRelationResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="questionBankId"    column="question_bank_id"    />
        <result property="examQuestionId"    column="exam_question_id"    />
        <result property="scoringRule"    column="scoring_rule"    />
        <result property="score"    column="score"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQBankQuestionRelationVo">
        select id, teacher_id, serial_number, question_bank_id, exam_question_id, scoring_rule, score, app_name_type, create_time, update_time from q_bank_question_relation
    </sql>

    <select id="selectQBankQuestionRelationList" parameterType="QBankQuestionRelation" resultMap="QBankQuestionRelationResult">
        <include refid="selectQBankQuestionRelationVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="serialNumber != null "> and serial_number = #{serialNumber}</if>
            <if test="questionBankId != null "> and question_bank_id = #{questionBankId}</if>
            <if test="examQuestionId != null "> and exam_question_id = #{examQuestionId}</if>
            <if test="scoringRule != null "> and scoring_rule = #{scoringRule}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
        </where>
    </select>
    
    <select id="selectQBankQuestionRelationById" parameterType="Long" resultMap="QBankQuestionRelationResult">
        <include refid="selectQBankQuestionRelationVo"/>
        where id = #{id}
    </select>
    <select id="selectQExamQuestionListByQuestionBankId" resultType="com.wendao101.teacher.domain.QExamQuestion">
        SELECT
        a.question_bank_id as questionBankId,
        a.score as score,
        a.serial_number as serialNumber,
        b.id,
        b.question_id_number questionIdNumber,
        b.teacher_id teacherId,
        b.question,
        b.question_type questionType,
        b.option_a optionA,
        b.option_b optionB,
        b.option_c optionC,
        b.option_d optionD,
        b.option_e optionE,
        b.option_f optionF,
        b.option_g optionG,
        b.option_h optionH,
        b.space_count spaceCount,
        b.answer,
        b.`explain`,
        b.`explain_pic` as explainPic,
        b.`explain_video` as explainVideo,
        b.difficulty,
        b.false_count falseCount,
        b.true_count trueCount,
        b.wrongRate,
        b.is_delete isDelete,
        b.app_name_type appNameType,
        b.create_time createTime,
        b.update_time updateTime
        FROM
        ( SELECT * FROM q_bank_question_relation t WHERE t.question_bank_id = #{questionBankId} ) a
        LEFT JOIN q_exam_question b ON a.exam_question_id = b.id
        <where>
            <if test="questionIdNumber != null "> and b.question_id_number = #{questionIdNumber}</if>
            <if test="teacherId != null "> and b.teacher_id = #{teacherId}</if>
            <if test="question != null  and question != ''"> and b.question like concat('%', #{question}, '%')</if>
            <if test="questionType != null "> and b.question_type = #{questionType}</if>
            <if test="optionA != null  and optionA != ''"> and b.option_a = #{optionA}</if>
            <if test="optionB != null  and optionB != ''"> and b.option_b = #{optionB}</if>
            <if test="optionC != null  and optionC != ''"> and b.option_c = #{optionC}</if>
            <if test="optionD != null  and optionD != ''"> and b.option_d = #{optionD}</if>
            <if test="optionE != null  and optionE != ''"> and b.option_e = #{optionE}</if>
            <if test="optionF != null  and optionF != ''"> and b.option_f = #{optionF}</if>
            <if test="optionG != null  and optionG != ''"> and b.option_g = #{optionG}</if>
            <if test="optionH != null  and optionH != ''"> and b.option_h = #{optionH}</if>
            <if test="spaceCount != null "> and b.space_count = #{spaceCount}</if>
            <if test="answer != null  and answer != ''"> and b.answer = #{answer}</if>
            <if test="explain != null  and explain != ''"> and b.`explain` = #{explain}</if>
            <if test="difficulty != null "> and b.difficulty = #{difficulty}</if>
            <if test="falseCount != null "> and b.false_count = #{falseCount}</if>
            <if test="trueCount != null "> and b.true_count = #{trueCount}</if>
            <if test="wrongRate != null "> and b.wrongRate = #{wrongRate}</if>
            <if test="isDelete != null "> and b.is_delete = #{isDelete}</if>
            <if test="appNameType != null "> and b.app_name_type = #{appNameType}</if>
        </where>
        order by a.serial_number asc

    </select>
    <select id="selectMaxSerialNumberByBankId" resultType="java.lang.Integer">
        SELECT
            max(serial_number)
        FROM
            q_bank_question_relation
        WHERE
            question_bank_id = #{questionBankId}
    </select>
    <select id="countQExamQuestionListByQuestionBankId" resultType="java.lang.Integer">
        SELECT count(1) FROM q_bank_question_relation WHERE question_bank_id = #{questionBankId}
    </select>

    <insert id="insertQBankQuestionRelation" parameterType="QBankQuestionRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT ignore INTO q_bank_question_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="questionBankId != null">question_bank_id,</if>
            <if test="examQuestionId != null">exam_question_id,</if>
            <if test="scoringRule != null">scoring_rule,</if>
            <if test="score != null">score,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="questionBankId != null">#{questionBankId},</if>
            <if test="examQuestionId != null">#{examQuestionId},</if>
            <if test="scoringRule != null">#{scoringRule},</if>
            <if test="score != null">#{score},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQBankQuestionRelation" parameterType="QBankQuestionRelation">
        update q_bank_question_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="questionBankId != null">question_bank_id = #{questionBankId},</if>
            <if test="examQuestionId != null">exam_question_id = #{examQuestionId},</if>
            <if test="scoringRule != null">scoring_rule = #{scoringRule},</if>
            <if test="score != null">score = #{score},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateExamQuestionSeq">
        update q_bank_question_relation set serial_number = #{serialNumber} where teacher_id = #{teacherId} and question_bank_id = #{questionBankId} and exam_question_id = #{examQuestionId}
    </update>

    <delete id="deleteQBankQuestionRelationById" parameterType="Long">
        delete from q_bank_question_relation where id = #{id}
    </delete>

    <delete id="deleteQBankQuestionRelationByIds" parameterType="String">
        delete from q_bank_question_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteQBankQuestionRelationByBankId">
        delete from q_bank_question_relation where question_bank_id = #{questionBankId}
    </delete>
</mapper>