<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.MDiscountsCodeProvideMapper">
    
    <resultMap type="MDiscountsCodeProvide" id="MDiscountsCodeProvideResult">
        <result property="id"    column="id"    />
        <result property="discountsId"    column="discounts_id"    />
        <result property="discountsCode"    column="discounts_code"    />
        <result property="userId"    column="user_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="phone"    column="phone"    />
        <result property="activityName"    column="activity_name"    />
        <result property="platformType"    column="platform_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMDiscountsCodeProvideVo">
        select id, discounts_id, discounts_code, user_id, teacher_id, phone, activity_name, platform_type, create_time, update_time from m_discounts_code_provide
    </sql>

    <select id="selectMDiscountsCodeProvideList" parameterType="MDiscountsCodeProvide" resultMap="MDiscountsCodeProvideResult">
        <include refid="selectMDiscountsCodeProvideVo"/>
        <where>  
            <if test="discountsId != null "> and discounts_id = #{discountsId}</if>
            <if test="discountsCode != null  and discountsCode != ''"> and discounts_code like concat('%', #{discountsCode}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="activityName != null  and activityName != ''"> and activity_name like concat('%', #{activityName}, '%')</if>
            <if test="platformType != null  and activityName != ''"> and platform_type = #{platformType}</if>
        </where>
    </select>
    
    <select id="selectMDiscountsCodeProvideById" parameterType="Long" resultMap="MDiscountsCodeProvideResult">
        <include refid="selectMDiscountsCodeProvideVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMDiscountsCodeProvide" parameterType="MDiscountsCodeProvide" useGeneratedKeys="true" keyProperty="id">
        insert into m_discounts_code_provide
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="discountsId != null">discounts_id,</if>
            <if test="discountsCode != null">discounts_code,</if>
            <if test="userId != null">user_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="phone != null">phone,</if>
            <if test="activityName != null">activity_name,</if>
            <if test="platformType != null">platform_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="discountsId != null">#{discountsId},</if>
            <if test="discountsCode != null">#{discountsCode},</if>
            <if test="userId != null">#{userId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="activityName != null">#{activityName},</if>
            <if test="platformType != null">#{platformType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMDiscountsCodeProvide" parameterType="MDiscountsCodeProvide">
        update m_discounts_code_provide
        <trim prefix="SET" suffixOverrides=",">
            <if test="discountsId != null">discounts_id = #{discountsId},</if>
            <if test="discountsCode != null">discounts_code = #{discountsCode},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="activityName != null">activity_name = #{activityName},</if>
            <if test="platformType != null">platform_type = #{platformType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMDiscountsCodeProvideById" parameterType="Long">
        delete from m_discounts_code_provide where id = #{id}
    </delete>

    <delete id="deleteMDiscountsCodeProvideByIds" parameterType="String">
        delete from m_discounts_code_provide where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>