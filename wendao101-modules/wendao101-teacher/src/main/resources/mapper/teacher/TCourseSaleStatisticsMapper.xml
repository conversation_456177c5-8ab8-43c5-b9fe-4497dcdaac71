<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TCourseSaleStatisticsMapper">
    
    <resultMap type="TCourseSaleStatistics" id="TCourseSaleStatisticsResult">
        <result property="id"    column="id"    />
        <result property="courseId"    column="course_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="allSaleCount"    column="all_sale_count"    />
        <result property="saleDay"    column="sale_day"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="dySaleCount"    column="dy_sale_count"    />
        <result property="ksSaleCount"    column="ks_sale_count"    />
        <result property="wxSaleCount"    column="wx_sale_count"    />
        <result property="dspSaleCount"    column="dsp_sale_count"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTCourseSaleStatisticsVo">
        select id, course_id, course_name, all_sale_count, sale_day, teacher_id, dy_sale_count, ks_sale_count, wx_sale_count, dsp_sale_count, create_time, update_time from t_course_sale_statistics
    </sql>

    <select id="selectTCourseSaleStatisticsList" parameterType="TCourseSaleStatistics" resultMap="TCourseSaleStatisticsResult">
        <include refid="selectTCourseSaleStatisticsVo"/>
        <where>  
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
            <if test="allSaleCount != null "> and all_sale_count = #{allSaleCount}</if>
            <if test="saleDay != null "> and sale_day = #{saleDay}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="dySaleCount != null "> and dy_sale_count = #{dySaleCount}</if>
            <if test="ksSaleCount != null "> and ks_sale_count = #{ksSaleCount}</if>
            <if test="wxSaleCount != null "> and wx_sale_count = #{wxSaleCount}</if>
            <if test="dspSaleCount != null "> and dsp_sale_count = #{dspSaleCount}</if>
        </where>
    </select>
    
    <select id="selectTCourseSaleStatisticsById" parameterType="Long" resultMap="TCourseSaleStatisticsResult">
        <include refid="selectTCourseSaleStatisticsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTCourseSaleStatistics" parameterType="TCourseSaleStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into t_course_sale_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseId != null">course_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="allSaleCount != null">all_sale_count,</if>
            <if test="saleDay != null">sale_day,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="dySaleCount != null">dy_sale_count,</if>
            <if test="ksSaleCount != null">ks_sale_count,</if>
            <if test="wxSaleCount != null">wx_sale_count,</if>
            <if test="dspSaleCount != null">dsp_sale_count,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseId != null">#{courseId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="allSaleCount != null">#{allSaleCount},</if>
            <if test="saleDay != null">#{saleDay},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="dySaleCount != null">#{dySaleCount},</if>
            <if test="ksSaleCount != null">#{ksSaleCount},</if>
            <if test="wxSaleCount != null">#{wxSaleCount},</if>
            <if test="dspSaleCount != null">#{dspSaleCount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTCourseSaleStatistics" parameterType="TCourseSaleStatistics">
        update t_course_sale_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="allSaleCount != null">all_sale_count = #{allSaleCount},</if>
            <if test="saleDay != null">sale_day = #{saleDay},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="dySaleCount != null">dy_sale_count = #{dySaleCount},</if>
            <if test="ksSaleCount != null">ks_sale_count = #{ksSaleCount},</if>
            <if test="wxSaleCount != null">wx_sale_count = #{wxSaleCount},</if>
            <if test="dspSaleCount != null">dsp_sale_count = #{dspSaleCount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTCourseSaleStatisticsById" parameterType="Long">
        delete from t_course_sale_statistics where id = #{id}
    </delete>

    <delete id="deleteTCourseSaleStatisticsByIds" parameterType="String">
        delete from t_course_sale_statistics where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTCourseSaleStatisticsByIdAndDayCountAndPlatform" resultMap="TCourseSaleStatisticsResult">
        SELECT
        course_id,
        ANY_VALUE ( course_name ) as course_name,
        sum( all_sale_count ) AS all_sale_count,
        sum( dy_sale_count ) AS dy_sale_count,
        sum( ks_sale_count ) AS ks_sale_count,
        sum( wx_sale_count ) AS wx_sale_count,
        sum( dsp_sale_count ) AS dsp_sale_count
        FROM
        t_course_sale_statistics
        <where>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="startDate != null "> and sale_day >= #{startDate}</if>
        </where>
        GROUP BY
        course_id
        order by all_sale_count DESC limit 10
    </select>

</mapper>