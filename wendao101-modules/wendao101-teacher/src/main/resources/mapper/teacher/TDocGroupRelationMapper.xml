<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TDocGroupRelationMapper">
    
    <resultMap type="TDocGroupRelation" id="TDocGroupRelationResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="docId"    column="doc_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTDocGroupRelationVo">
        select id, teacher_id, group_id, doc_id, create_time, update_time from t_doc_group_relation
    </sql>

    <select id="selectTDocGroupRelationList" parameterType="TDocGroupRelation" resultMap="TDocGroupRelationResult">
        <include refid="selectTDocGroupRelationVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="docId != null "> and doc_id = #{docId}</if>
        </where>
    </select>
    
    <select id="selectTDocGroupRelationById" parameterType="Long" resultMap="TDocGroupRelationResult">
        <include refid="selectTDocGroupRelationVo"/>
        where id = #{id}
    </select>

    <update id="insertTDocGroupRelation" parameterType="java.util.List">
        INSERT ignore INTO t_doc_group_relation (teacher_id,group_id,doc_id) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.teacherId},#{item.groupId},#{item.docId})
        </foreach>
    </update>

    <update id="updateTDocGroupRelation" parameterType="TDocGroupRelation">
        update t_doc_group_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="docId != null">doc_id = #{docId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTDocGroupRelationById" parameterType="Long">
        delete from t_doc_group_relation where id = #{id}
    </delete>

    <delete id="deleteTDocGroupRelationByIds" parameterType="String">
        delete from t_doc_group_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="removeFromGroup">
        delete from t_doc_group_relation where teacher_id = #{teacherId} and group_id = #{groupId} and doc_id in
        <foreach item="docId" collection="docIdArr" open="(" separator="," close=")">
            #{docId}
        </foreach>
    </delete>

</mapper>