<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TStatisticsReportMapper">
    
    <resultMap type="TStatisticsReport" id="TStatisticsReportResult">
        <result property="id"    column="id"    />
        <result property="allVisitUserToday"    column="all_visit_user_today"    />
        <result property="allTodayPayCount"    column="all_today_pay_count"    />
        <result property="allTodayCourseIncome"    column="all_today_course_income"    />
        <result property="allGrossCourseIncome"    column="all_gross_course_income"    />
        <result property="dyVisitUserToday"    column="dy_visit_user_today"    />
        <result property="dyTodayPayCount"    column="dy_today_pay_count"    />
        <result property="dyTodayCourseIncome"    column="dy_today_course_income"    />
        <result property="ksVisitUserToday"    column="ks_visit_user_today"    />
        <result property="ksTodayPayCount"    column="ks_today_pay_count"    />
        <result property="ksTodayCourseIncome"    column="ks_today_course_income"    />
        <result property="wxVisitUserToday"    column="wx_visit_user_today"    />
        <result property="wxTodayPayCount"    column="wx_today_pay_count"    />
        <result property="wxTodayCourseIncome"    column="wx_today_course_income"    />
        <result property="sphVisitUserToday"    column="sph_visit_user_today"    />
        <result property="sphTodayPayCount"    column="sph_today_pay_count"    />
        <result property="sphTodayCourseIncome"    column="sph_today_course_income"    />
        <result property="statisticsDay"    column="statistics_day"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTStatisticsReportVo">
        select id, all_visit_user_today, all_today_pay_count, all_today_course_income, all_gross_course_income, dy_visit_user_today, dy_today_pay_count, dy_today_course_income, ks_visit_user_today, ks_today_pay_count, ks_today_course_income, wx_visit_user_today, wx_today_pay_count, wx_today_course_income, sph_visit_user_today, sph_today_pay_count, sph_today_course_income, statistics_day, create_time, update_time from t_statistics_report
    </sql>

    <select id="selectTStatisticsReportList" parameterType="TStatisticsReport" resultMap="TStatisticsReportResult">
        <include refid="selectTStatisticsReportVo"/>
        <where>  
            <if test="allVisitUserToday != null "> and all_visit_user_today = #{allVisitUserToday}</if>
            <if test="allTodayPayCount != null "> and all_today_pay_count = #{allTodayPayCount}</if>
            <if test="allTodayCourseIncome != null "> and all_today_course_income = #{allTodayCourseIncome}</if>
            <if test="allGrossCourseIncome != null "> and all_gross_course_income = #{allGrossCourseIncome}</if>
            <if test="dyVisitUserToday != null "> and dy_visit_user_today = #{dyVisitUserToday}</if>
            <if test="dyTodayPayCount != null "> and dy_today_pay_count = #{dyTodayPayCount}</if>
            <if test="dyTodayCourseIncome != null "> and dy_today_course_income = #{dyTodayCourseIncome}</if>
            <if test="ksVisitUserToday != null "> and ks_visit_user_today = #{ksVisitUserToday}</if>
            <if test="ksTodayPayCount != null "> and ks_today_pay_count = #{ksTodayPayCount}</if>
            <if test="ksTodayCourseIncome != null "> and ks_today_course_income = #{ksTodayCourseIncome}</if>
            <if test="wxVisitUserToday != null "> and wx_visit_user_today = #{wxVisitUserToday}</if>
            <if test="wxTodayPayCount != null "> and wx_today_pay_count = #{wxTodayPayCount}</if>
            <if test="wxTodayCourseIncome != null "> and wx_today_course_income = #{wxTodayCourseIncome}</if>
            <if test="sphVisitUserToday != null "> and sph_visit_user_today = #{sphVisitUserToday}</if>
            <if test="sphTodayPayCount != null "> and sph_today_pay_count = #{sphTodayPayCount}</if>
            <if test="sphTodayCourseIncome != null "> and sph_today_course_income = #{sphTodayCourseIncome}</if>
            <if test="statisticsDay != null "> and statistics_day = #{statisticsDay}</if>
        </where>
    </select>
    
    <select id="selectTStatisticsReportById" parameterType="Long" resultMap="TStatisticsReportResult">
        <include refid="selectTStatisticsReportVo"/>
        where id = #{id}
    </select>

    <select id="selectTStatisticsReportToday" parameterType="com.wendao101.teacher.vo.HomePageImportantDataVO" resultMap="TStatisticsReportResult">
        <include refid="selectTStatisticsReportVo"/>
        where teacher_id = #{teacherId}
        and statistics_day = DATE(NOW())
    </select>

    <select id="selectTStatisticsReportBefore" parameterType="com.wendao101.teacher.vo.HomePageImportantDataVO" resultMap="TStatisticsReportResult">
        <include refid="selectTStatisticsReportVo"/>
        where teacher_id = #{teacherId}
        and statistics_day = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
    </select>

    <select id="getTStatisticsReport" parameterType="com.wendao101.teacher.vo.OrderIncomeVO" resultMap="TStatisticsReportResult">
        <include refid="selectTStatisticsReportVo"/>
        where teacher_id = #{teacherId}
        and statistics_day = #{dateTime}
    </select>
        
    <insert id="insertTStatisticsReport" parameterType="TStatisticsReport" useGeneratedKeys="true" keyProperty="id">
        insert into t_statistics_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="allVisitUserToday != null">all_visit_user_today,</if>
            <if test="allTodayPayCount != null">all_today_pay_count,</if>
            <if test="allTodayCourseIncome != null">all_today_course_income,</if>
            <if test="allGrossCourseIncome != null">all_gross_course_income,</if>
            <if test="dyVisitUserToday != null">dy_visit_user_today,</if>
            <if test="dyTodayPayCount != null">dy_today_pay_count,</if>
            <if test="dyTodayCourseIncome != null">dy_today_course_income,</if>
            <if test="ksVisitUserToday != null">ks_visit_user_today,</if>
            <if test="ksTodayPayCount != null">ks_today_pay_count,</if>
            <if test="ksTodayCourseIncome != null">ks_today_course_income,</if>
            <if test="wxVisitUserToday != null">wx_visit_user_today,</if>
            <if test="wxTodayPayCount != null">wx_today_pay_count,</if>
            <if test="wxTodayCourseIncome != null">wx_today_course_income,</if>
            <if test="sphVisitUserToday != null">sph_visit_user_today,</if>
            <if test="sphTodayPayCount != null">sph_today_pay_count,</if>
            <if test="sphTodayCourseIncome != null">sph_today_course_income,</if>
            <if test="statisticsDay != null">statistics_day,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="allVisitUserToday != null">#{allVisitUserToday},</if>
            <if test="allTodayPayCount != null">#{allTodayPayCount},</if>
            <if test="allTodayCourseIncome != null">#{allTodayCourseIncome},</if>
            <if test="allGrossCourseIncome != null">#{allGrossCourseIncome},</if>
            <if test="dyVisitUserToday != null">#{dyVisitUserToday},</if>
            <if test="dyTodayPayCount != null">#{dyTodayPayCount},</if>
            <if test="dyTodayCourseIncome != null">#{dyTodayCourseIncome},</if>
            <if test="ksVisitUserToday != null">#{ksVisitUserToday},</if>
            <if test="ksTodayPayCount != null">#{ksTodayPayCount},</if>
            <if test="ksTodayCourseIncome != null">#{ksTodayCourseIncome},</if>
            <if test="wxVisitUserToday != null">#{wxVisitUserToday},</if>
            <if test="wxTodayPayCount != null">#{wxTodayPayCount},</if>
            <if test="wxTodayCourseIncome != null">#{wxTodayCourseIncome},</if>
            <if test="sphVisitUserToday != null">#{sphVisitUserToday},</if>
            <if test="sphTodayPayCount != null">#{sphTodayPayCount},</if>
            <if test="sphTodayCourseIncome != null">#{sphTodayCourseIncome},</if>
            <if test="statisticsDay != null">#{statisticsDay},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTStatisticsReport" parameterType="TStatisticsReport">
        update t_statistics_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="allVisitUserToday != null">all_visit_user_today = #{allVisitUserToday},</if>
            <if test="allTodayPayCount != null">all_today_pay_count = #{allTodayPayCount},</if>
            <if test="allTodayCourseIncome != null">all_today_course_income = #{allTodayCourseIncome},</if>
            <if test="allGrossCourseIncome != null">all_gross_course_income = #{allGrossCourseIncome},</if>
            <if test="dyVisitUserToday != null">dy_visit_user_today = #{dyVisitUserToday},</if>
            <if test="dyTodayPayCount != null">dy_today_pay_count = #{dyTodayPayCount},</if>
            <if test="dyTodayCourseIncome != null">dy_today_course_income = #{dyTodayCourseIncome},</if>
            <if test="ksVisitUserToday != null">ks_visit_user_today = #{ksVisitUserToday},</if>
            <if test="ksTodayPayCount != null">ks_today_pay_count = #{ksTodayPayCount},</if>
            <if test="ksTodayCourseIncome != null">ks_today_course_income = #{ksTodayCourseIncome},</if>
            <if test="wxVisitUserToday != null">wx_visit_user_today = #{wxVisitUserToday},</if>
            <if test="wxTodayPayCount != null">wx_today_pay_count = #{wxTodayPayCount},</if>
            <if test="wxTodayCourseIncome != null">wx_today_course_income = #{wxTodayCourseIncome},</if>
            <if test="sphVisitUserToday != null">sph_visit_user_today = #{sphVisitUserToday},</if>
            <if test="sphTodayPayCount != null">sph_today_pay_count = #{sphTodayPayCount},</if>
            <if test="sphTodayCourseIncome != null">sph_today_course_income = #{sphTodayCourseIncome},</if>
            <if test="statisticsDay != null">statistics_day = #{statisticsDay},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTStatisticsReportById" parameterType="Long">
        delete from t_statistics_report where id = #{id}
    </delete>

    <delete id="deleteTStatisticsReportByIds" parameterType="String">
        delete from t_statistics_report where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>