<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.WendaoCertInfoBindMapper">

    <resultMap type="WendaoCertInfoBind" id="WendaoCertInfoBindResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="entityType"    column="entity_type"    />
        <result property="certInfoId"    column="cert_info_id"    />
        <result property="douyinhao"    column="douyinhao"    />
        <result property="openid"    column="openid"    />
        <result property="nickName"    column="nick_name"    />
        <result property="douyinAccountType"    column="douyin_account_type"    />
        <result property="bindStatus"    column="bind_status"    />
        <result property="wendaoUserId"    column="wendao_user_id"    />
        <result property="appid"    column="appid"    />
        <result property="merchantEntityId"    column="merchant_entity_id"    />
        <result property="partnerEntityId"    column="partner_entity_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="shortVideoStatus"    column="short_video_status"    />
        <result property="liveStreamStatus"    column="live_stream_status"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="imBandStatus"    column="im_band_status"    />

    </resultMap>

    <sql id="selectWendaoCertInfoBindVo">
        select id, teacher_id, entity_type, cert_info_id, douyinhao, openid, nick_name, douyin_account_type, bind_status, wendao_user_id, appid,
               merchant_entity_id, partner_entity_id, create_time, update_time, short_video_status, live_stream_status,app_name_type,im_band_status
        from wendao_cert_info_bind
    </sql>

    <select id="selectWendaoCertInfoBindList" resultType="com.wendao101.teacher.domain.WendaoCertInfoBind">
        select w.id id,
        w.teacher_id teacherId,
        w.entity_type entityType,
        w.cert_info_id certInfoId,
        w.douyinhao douyinhao,
        w.openid openid,
        w.nick_name nickName,
        w.douyin_account_type douyinAccountType,
        w.bind_status bindStatus,
        w.wendao_user_id wendaoUserId,
        w.appid appid,
        w.merchant_entity_id merchantEntityId,
        w.partner_entity_id partnerEntityId,
        w.create_time createTime,
        w.update_time updateTime,
        w.short_video_status shortVideoStatus,
        w.live_stream_status liveStreamStatus,
        w.im_band_status imBandStatus,
        w.app_name_type appNameType,
        wc.business_license_company_name businessLicenseCompanyName,
        wc.first_class_title  firstClassTitle,
        wc.second_class_title secondClassTitle,
        wc.teacher_real_name  teacherRealName,
        wc.entity_type wcentityType
        , t.shop_name as shopName
        , t.mobile as mobile
        from wendao_cert_info_bind w
        left join wendao_cert_info wc on w.cert_info_id = wc.id
        LEFT JOIN t_teacher t ON w.teacher_id = t.teacher_id
        <where>
            <if test="teacherId != null "> and w.teacher_id = #{teacherId}</if>
        </where>
    </select>


    <select id="selectWendaoCertInfoBindById" parameterType="Long" resultMap="WendaoCertInfoBindResult">
        <include refid="selectWendaoCertInfoBindVo"/>
        where id = #{id}
    </select>

    <select id="selectWendaoCertInfoByCertIdAndOpenId" resultMap="WendaoCertInfoBindResult">
        <include refid="selectWendaoCertInfoBindVo"/>
        where openid = #{openid} and cert_info_id = #{certInfoId} limit 1
    </select>
    <select id="selectWendaoCertInfoByCertIdAndDouyinhao" resultMap="WendaoCertInfoBindResult">
        <include refid="selectWendaoCertInfoBindVo"/>
        where douyinhao = #{douyinhao} and cert_info_id = #{certInfoId} limit 1
    </select>
    <select id="countNoDouyinhao" resultType="java.lang.Integer">
        select count(*) from wendao_cert_info_bind where douyinhao is null and teacher_id=#{teacherId} and short_video_status in (0,1)
    </select>
    <select id="countBindingByOpenId" resultType="java.lang.Integer">
        select count(*) from wendao_cert_info_bind where openid = #{openid} and short_video_status in (0,1)
    </select>
    <select id="countBindingByDouyinhao" resultType="java.lang.Integer">
        select count(*) from wendao_cert_info_bind where douyinhao = #{douyinhao} and short_video_status in (0,1)
    </select>
    <select id="countBindingImByDouyinhao" resultType="java.lang.Integer">
        select count(*) from wendao_cert_info_bind where douyinhao = #{douyinhao} and im_band_status in (0,1)
    </select>
    <select id="selectCountDouyinHao" resultType="java.lang.Integer">
        select count(*) from wendao_cert_info_bind where douyinhao = #{douyinhao}
    </select>
    <select id="countBindDouyinhao" resultType="java.lang.Integer">
        select count(*) from wendao_cert_info_bind where teacher_id=#{teacherId} and short_video_status = 1
    </select>

    <insert id="insertWendaoCertInfoBind" parameterType="WendaoCertInfoBind" useGeneratedKeys="true" keyProperty="id">
        insert into wendao_cert_info_bind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="entityType != null">entity_type,</if>
            <if test="certInfoId != null">cert_info_id,</if>
            <if test="douyinhao != null">douyinhao,</if>
            <if test="openid != null">openid,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="douyinAccountType != null">douyin_account_type,</if>
            <if test="bindStatus != null">bind_status,</if>
            <if test="wendaoUserId != null">wendao_user_id,</if>
            <if test="appid != null">appid,</if>
            <if test="merchantEntityId != null">merchant_entity_id,</if>
            <if test="partnerEntityId != null">partner_entity_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="shortVideoStatus != null">short_video_status,</if>
            <if test="liveStreamStatus != null">live_stream_status,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="imBandStatus != null">im_band_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="entityType != null">#{entityType},</if>
            <if test="certInfoId != null">#{certInfoId},</if>
            <if test="douyinhao != null">#{douyinhao},</if>
            <if test="openid != null">#{openid},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="douyinAccountType != null">#{douyinAccountType},</if>
            <if test="bindStatus != null">#{bindStatus},</if>
            <if test="wendaoUserId != null">#{wendaoUserId},</if>
            <if test="appid != null">#{appid},</if>
            <if test="merchantEntityId != null">#{merchantEntityId},</if>
            <if test="partnerEntityId != null">#{partnerEntityId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="shortVideoStatus != null">#{shortVideoStatus},</if>
            <if test="liveStreamStatus != null">#{liveStreamStatus},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="imBandStatus != null">#{imBandStatus},</if>
         </trim>
    </insert>

    <update id="updateWendaoCertInfoBind" parameterType="WendaoCertInfoBind">
        update wendao_cert_info_bind
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="entityType != null">entity_type = #{entityType},</if>
            <if test="certInfoId != null">cert_info_id = #{certInfoId},</if>
            <if test="douyinhao != null">douyinhao = #{douyinhao},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="douyinAccountType != null">douyin_account_type = #{douyinAccountType},</if>
            <if test="bindStatus != null">bind_status = #{bindStatus},</if>
            <if test="wendaoUserId != null">wendao_user_id = #{wendaoUserId},</if>
            <if test="appid != null">appid = #{appid},</if>
            <if test="merchantEntityId != null">merchant_entity_id = #{merchantEntityId},</if>
            <if test="partnerEntityId != null">partner_entity_id = #{partnerEntityId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="shortVideoStatus != null">short_video_status = #{shortVideoStatus},</if>
            <if test="liveStreamStatus != null">live_stream_status = #{liveStreamStatus},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="imBandStatus != null">im_band_status = #{imBandStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWendaoCertInfoBindById" parameterType="Long">
        delete from wendao_cert_info_bind where id = #{id}
    </delete>

    <delete id="deleteWendaoCertInfoBindByIds" parameterType="String">
        delete from wendao_cert_info_bind where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>