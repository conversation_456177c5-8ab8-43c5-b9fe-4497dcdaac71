<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.LearningMaterialsMapper">

    <sql id="selectStudyDataVo">

    </sql>
<!--    <insert id="bindingCourseById" parameterType="com.wendao101.teacher.domain.StudyData">-->
<!--        INSERT INTO learning_materials-->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--            <if test="teacherId != null">teacher_id,</if>-->
<!--            <if test="courseId != null">course_id,</if>-->
<!--            <if test="id != null">study_data_id,</if>-->
<!--            <if test="createTime != null">create_time,</if>-->
<!--            <if test="updateTime != null">update_time,</if>-->
<!--        </trim>-->
<!--        <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--            <if test="teacherId != null">#{teacherId},</if>-->
<!--            <if test="courseId != null">#{courseId},</if>-->
<!--            <if test="id != null">#{id},</if>-->
<!--            <if test="createTime != null">#{createTime},</if>-->
<!--            <if test="updateTime != null">#{updateTime},</if>-->
<!--        </trim>-->
<!--    </insert>-->
    <insert id="upload" parameterType="com.wendao101.teacher.domain.StudyData">
        INSERT INTO study_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="studyFileName != null">study_file_name,</if>
            <if test="studyFileType != null">study_file_type,</if>
            <if test="studyFileUrl != null">study_file_url,</if>
            <if test="studyFileSize != null">study_file_size,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="teacherId != null">#{teacherId},</if>
        <if test="studyFileName != null">#{studyFileName},</if>
        <if test="studyFileType != null">#{studyFileType},</if>
        <if test="studyFileUrl != null">#{studyFileUrl},</if>
        <if test="studyFileSize != null">#{studyFileSize},</if>
        <if test="createTime != null">#{createTime},</if>
        <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <insert id="bindingCourseById">
        insert into learning_materials (teacher_id, course_id, study_data_id) VALUES
        <foreach collection="studyDatas" item="s" separator=",">
            (#{s.teacherId},#{s.courseId},#{s.id})
        </foreach>
    </insert>
    <delete id="deleteLearningMaterialsById" parameterType="java.lang.Long">
        DELETE FROM learning_materials
        <where>
            <if test="id != null">study_data_id = #{id}</if>
        </where>
    </delete>
    <delete id="removCourseById">
        DELETE FROM learning_materials
        <where>
            <if test="id != null">and study_data_id = #{id}</if>
            <if test="courseId != null">and course_id = #{courseId}</if>
        </where>
    </delete>
    <delete id="deleteMaterialsById">
        DELETE FROM study_data
        <where>
            <if test="id != null">id = #{id}</if>
        </where>
    </delete>

    <select id="listByTeacher" resultType="com.wendao101.teacher.domain.StudyData">
        SELECT
            l.study_data_id AS id
             , l.teacher_id as teacherId
             , s.study_file_name studyFileName
             , s.study_file_type studyFileType
             , s.study_file_size studyFileSize
             , s.study_file_url  studyFileUrl
             , COUNT(l.study_data_id) AS courseNum
        FROM learning_materials as l INNER JOIN study_data s
        <where>
            <if test="teacherId != null">l.teacher_id = #{teacherId} </if>
        and l.study_data_id = s.id
        </where>
        GROUP BY l.study_data_id
        UNION
        SELECT
        s.id AS id
        , s.teacher_id as teacherId
        , s.study_file_name studyFileName
        , s.study_file_type studyFileType
        , s.study_file_size studyFileSize
        , s.study_file_url  studyFileUrl
        , IF(s.course_id IS NULL,0,1) AS courseNum
        FROM study_data s
        <where>
            <if test="teacherId != null">and s.teacher_Id = #{teacherId}</if>
            AND s.course_id is NULL
            AND s.id NOT IN (
            SELECT l.study_data_id
            FROM learning_materials l
            )
        </where>
    </select>
    <select id="selectByLearningMaterialsById" resultType="com.wendao101.teacher.domain.StudyData"
            parameterType="java.lang.Long">
        SELECT s.id as id
             , l.course_id as courseId
             , l.teacher_id as teacherId
             , s.study_file_name  as studyFileName
             , c.title as courseName
        FROM learning_materials l
                 JOIN course c ON c.id = l.course_id
                 JOIN study_data s ON s.id = l.study_data_id
       <where>
           <if test="id != null">and l.study_data_id = #{id}</if>
       </where>
        order by creat_time
    </select>
    <select id="selectAll" resultType="com.wendao101.teacher.vo.MaterialsVO"
            parameterType="com.wendao101.teacher.domain.StudyData">
        SELECT DISTINCT  s.app_type as appType
        ,	t.shop_name as shopName
        , t.teacher_id as teacherId
        , t.mobile as mobile
        , (SELECT COUNT(teacher_id) FROM study_data s WHERE t.teacher_id = s.teacher_id) as materialsNum
        FROM t_teacher t
        JOIN study_data s
        <where>
            <if test="appType != null and appType != ''">and s.app_type = #{appType}</if>
            <if test="searchValue != null and searchValue != ''">and (
                t.shop_name like concat('%', #{searchValue}, '%')
                or t.teacher_id like concat('%', #{searchValue}, '%')
                or t.mobile  like concat('%', #{searchValue}, '%')
                )
            </if>
        </where>
        <if test="isAsc != 0">
            ORDER BY materialsNum DESC
        </if>
        <if test="isAsc == 0">
            ORDER BY materialsNum asc
        </if>
    </select>


</mapper>
