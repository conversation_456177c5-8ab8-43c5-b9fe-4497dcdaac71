<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TeacherSubAccountMapper">
    
    <resultMap type="TeacherSubAccount" id="TeacherSubAccountResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="accountName"    column="account_name"    />
        <result property="userName"    column="user_name"    />
        <result property="password"    column="password"    />
        <result property="accountStatus"    column="account_status"    />
        <result property="permissionList"    column="permission_list"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTeacherSubAccountVo">
        select id, teacher_id, account_name, user_name, password, account_status, permission_list, is_delete, app_name_type, create_time, update_time from teacher_sub_account
    </sql>

    <select id="selectTeacherSubAccountList" parameterType="TeacherSubAccount" resultMap="TeacherSubAccountResult">
        <include refid="selectTeacherSubAccountVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="accountStatus != null "> and account_status = #{accountStatus}</if>
            <if test="permissionList != null  and permissionList != ''"> and permission_list = #{permissionList}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
            <if test="startTime != null  "> <![CDATA[ and create_time >= #{startTime} ]]></if>
            <if test="endTime != null  "> <![CDATA[ and create_time <= #{endTime} ]]></if>
        </where>
    </select>
    
    <select id="selectTeacherSubAccountById" parameterType="Long" resultMap="TeacherSubAccountResult">
        <include refid="selectTeacherSubAccountVo"/>
        where id = #{id}
    </select>
    <select id="countSameUserName" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            ( SELECT mobile AS userName FROM t_teacher WHERE mobile = #{userName} and app_name_type=#{appNameType} UNION SELECT user_name AS userName FROM teacher_sub_account WHERE user_name = #{userName} and app_name_type=#{appNameType} AND is_delete = 0 ) b
    </select>
    <select id="selectTeacherSubAccountByUserNameAndAppNameType" resultMap="TeacherSubAccountResult">
        <include refid="selectTeacherSubAccountVo"/>
        where user_name = #{userName} and app_name_type = #{appNameType} and is_delete = 0 limit 1
    </select>

    <insert id="insertTeacherSubAccount" parameterType="TeacherSubAccount" useGeneratedKeys="true" keyProperty="id">
        insert into teacher_sub_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="accountName != null">account_name,</if>
            <if test="userName != null">user_name,</if>
            <if test="password != null">password,</if>
            <if test="accountStatus != null">account_status,</if>
            <if test="permissionList != null">permission_list,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="accountName != null">#{accountName},</if>
            <if test="userName != null">#{userName},</if>
            <if test="password != null">#{password},</if>
            <if test="accountStatus != null">#{accountStatus},</if>
            <if test="permissionList != null">#{permissionList},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTeacherSubAccount" parameterType="TeacherSubAccount">
        update teacher_sub_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="password != null">password = #{password},</if>
            <if test="accountStatus != null">account_status = #{accountStatus},</if>
            <if test="permissionList != null">permission_list = #{permissionList},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherSubAccountById" parameterType="Long">
        delete from teacher_sub_account where id = #{id}
    </delete>

    <update id="deleteTeacherSubAccountByIds">
        update teacher_sub_account set is_delete=1 where teacher_id = #{teacherId} and id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>