<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.CourseMachineAuditMapper">
    
    <resultMap type="CourseMachineAudit" id="CourseMachineAuditResult">
        <result property="id"    column="id"    />
        <result property="courseIdNumber"    column="course_id_number"    />
        <result property="courseName"    column="course_name"    />
        <result property="totalCourseCount"    column="total_course_count"    />
        <result property="shopId"    column="shop_id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="certName"    column="cert_name"    />
        <result property="firstClassDouyinClassId"    column="first_class_douyin_class_id"    />
        <result property="firstClassTitle"    column="first_class_title"    />
        <result property="secondClassDouyinClassId"    column="second_class_douyin_class_id"    />
        <result property="secondClassTitle"    column="second_class_title"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="uploadTime"    column="upload_time"    />
        <result property="lastAuditTime"    column="last_audit_time"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="sameCourseCount"    column="same_course_count"    />
        <result property="sameShopCount"    column="same_shop_count"    />
        <result property="humanAuditStatus"    column="human_audit_status"    />
        <result property="humanAuditReason"    column="human_audit_reason"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="type"    column="type"    />
        <result property="courseType"    column="course_type"    />
        <result property="appNameType"    column="app_name_type"    />
    </resultMap>

    <sql id="selectCourseMachineAuditVo">
        select id, course_id_number, course_name, total_course_count, shop_id, shop_name, cert_name, first_class_douyin_class_id, first_class_title, second_class_douyin_class_id, second_class_title, is_delete, upload_time, last_audit_time, audit_status, same_course_count, same_shop_count, human_audit_status, human_audit_reason, create_time, update_time, type, course_type, app_name_type from course_machine_audit
    </sql>

    <select id="selectCourseMachineAuditList" parameterType="CourseMachineAudit" resultMap="CourseMachineAuditResult">
        <include refid="selectCourseMachineAuditVo"/>
        <where>  
            <if test="courseIdNumber != null "> and course_id_number = #{courseIdNumber}</if>
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
            <if test="totalCourseCount != null "> and total_course_count = #{totalCourseCount}</if>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="certName != null  and certName != ''"> and cert_name like concat('%', #{certName}, '%')</if>
            <if test="firstClassDouyinClassId != null "> and first_class_douyin_class_id = #{firstClassDouyinClassId}</if>
            <if test="firstClassTitle != null  and firstClassTitle != ''"> and first_class_title = #{firstClassTitle}</if>
            <if test="secondClassDouyinClassId != null "> and second_class_douyin_class_id = #{secondClassDouyinClassId}</if>
            <if test="secondClassTitle != null  and secondClassTitle != ''"> and second_class_title = #{secondClassTitle}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="uploadTime != null "> and upload_time = #{uploadTime}</if>
            <if test="lastAuditTime != null "> and last_audit_time = #{lastAuditTime}</if>
            <if test="auditStatus != null "> and audit_status = #{auditStatus}</if>
            <if test="sameCourseCount != null "> and same_course_count = #{sameCourseCount}</if>
            <if test="sameShopCount != null "> and same_shop_count = #{sameShopCount}</if>
            <if test="humanAuditStatus != null "> and human_audit_status = #{humanAuditStatus}</if>
            <if test="humanAuditReason != null  and humanAuditReason != ''"> and human_audit_reason = #{humanAuditReason}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="courseType != null  and courseType != ''"> and course_type = #{courseType}</if>
            <if test="courseInfo != null  and courseInfo != ''"> and ( course_id_number like concat('%', #{courseInfo}, '%') or course_name like concat('%', #{courseInfo}, '%') )</if>


            <if test="shopInfo != null  and shopInfo != ''"> and (shop_id like concat('%', #{shopInfo}, '%') or shop_name like concat('%', #{shopInfo}, '%'))</if>


            <if test="params.beginTime != null and params.beginTime != ''">
                AND upload_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND upload_time &lt;= #{params.endTime}
            </if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCourseMachineAuditById" parameterType="Long" resultMap="CourseMachineAuditResult">
        <include refid="selectCourseMachineAuditVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCourseMachineAudit" parameterType="CourseMachineAudit" useGeneratedKeys="true" keyProperty="id">
        insert into course_machine_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseIdNumber != null">course_id_number,</if>
            <if test="courseName != null">course_name,</if>
            <if test="totalCourseCount != null">total_course_count,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="certName != null">cert_name,</if>
            <if test="firstClassDouyinClassId != null">first_class_douyin_class_id,</if>
            <if test="firstClassTitle != null">first_class_title,</if>
            <if test="secondClassDouyinClassId != null">second_class_douyin_class_id,</if>
            <if test="secondClassTitle != null">second_class_title,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="uploadTime != null">upload_time,</if>
            <if test="lastAuditTime != null">last_audit_time,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="sameCourseCount != null">same_course_count,</if>
            <if test="sameShopCount != null">same_shop_count,</if>
            <if test="humanAuditStatus != null">human_audit_status,</if>
            <if test="humanAuditReason != null">human_audit_reason,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="type != null">type,</if>
            <if test="courseType != null">course_type,</if>
            <if test="appNameType != null">app_name_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseIdNumber != null">#{courseIdNumber},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="totalCourseCount != null">#{totalCourseCount},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="certName != null">#{certName},</if>
            <if test="firstClassDouyinClassId != null">#{firstClassDouyinClassId},</if>
            <if test="firstClassTitle != null">#{firstClassTitle},</if>
            <if test="secondClassDouyinClassId != null">#{secondClassDouyinClassId},</if>
            <if test="secondClassTitle != null">#{secondClassTitle},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
            <if test="lastAuditTime != null">#{lastAuditTime},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="sameCourseCount != null">#{sameCourseCount},</if>
            <if test="sameShopCount != null">#{sameShopCount},</if>
            <if test="humanAuditStatus != null">#{humanAuditStatus},</if>
            <if test="humanAuditReason != null">#{humanAuditReason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="type != null">#{type},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="appNameType != null">#{appNameType},</if>
         </trim>
    </insert>

    <update id="updateCourseMachineAudit" parameterType="CourseMachineAudit">
        update course_machine_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseIdNumber != null">course_id_number = #{courseIdNumber},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="totalCourseCount != null">total_course_count = #{totalCourseCount},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="certName != null">cert_name = #{certName},</if>
            <if test="firstClassDouyinClassId != null">first_class_douyin_class_id = #{firstClassDouyinClassId},</if>
            <if test="firstClassTitle != null">first_class_title = #{firstClassTitle},</if>
            <if test="secondClassDouyinClassId != null">second_class_douyin_class_id = #{secondClassDouyinClassId},</if>
            <if test="secondClassTitle != null">second_class_title = #{secondClassTitle},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
            <if test="lastAuditTime != null">last_audit_time = #{lastAuditTime},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="sameCourseCount != null">same_course_count = #{sameCourseCount},</if>
            <if test="sameShopCount != null">same_shop_count = #{sameShopCount},</if>
            <if test="humanAuditStatus != null">human_audit_status = #{humanAuditStatus},</if>
            <if test="humanAuditReason != null">human_audit_reason = #{humanAuditReason},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="courseType != null">course_type = #{courseType},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseMachineAuditById" parameterType="Long">
        delete from course_machine_audit where id = #{id}
    </delete>

    <delete id="deleteCourseMachineAuditByIds" parameterType="String">
        delete from course_machine_audit where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>