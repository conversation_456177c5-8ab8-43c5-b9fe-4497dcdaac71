# Tomcat
server:
  port: 9500

# Spring
spring: 
  application:
    # 应用名称
    name: wendao101-teacher
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      username: wendaonacos
      password: gI6d8Y6ljLSc
      discovery:
        # 服务注册地址
        server-addr: @nacos.server.address@
      config:
        # 配置中心地址
        server-addr: @nacos.server.address@
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
logging:
  level:
    com.wendao101.teacher.mapper: error
