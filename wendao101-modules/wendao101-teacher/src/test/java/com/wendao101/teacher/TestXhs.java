package com.wendao101.teacher;

import com.alibaba.fastjson.JSON;
import com.xiaohongshu.fls.opensdk.client.*;
import com.xiaohongshu.fls.opensdk.entity.BaseResponse;
import com.xiaohongshu.fls.opensdk.entity.common.request.GetCategoriesRequest;
import com.xiaohongshu.fls.opensdk.entity.common.response.GetCategoriseResponse;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

public class TestXhs {
    private static final String appId = "86c5974f3a754ff9a732";
    private static final String appSecret = "a103ff558ae977d7b015bb05ccb05809";
    /**
     * AfterSaleClient
     * BoutiqueClient
     * CommonClient
     * DataClient
     * ExpressClient
     * FinanceClient
     * InventoryClient
     * InvoiceClient
     * MaterialClient
     * OauthClient
     * OrderClient
     * PackageClient
     * ProductClient
     * 创建所有的client
     */
    private static final AfterSaleClient afterSaleClient = new AfterSaleClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final BoutiqueClient boutiqueClient = new BoutiqueClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final CommonClient commonClient = new CommonClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final DataClient dataClient = new DataClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final ExpressClient expressClient = new ExpressClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final FinanceClient financeClient = new FinanceClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final InventoryClient inventoryClient = new InventoryClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final InvoiceClient invoiceClient = new InvoiceClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final MaterialClient materialClient = new MaterialClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final OauthClient oauthClient = new OauthClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final OrderClient orderClient = new OrderClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final PackageClient packageClient = new PackageClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final ProductClient productClient = new ProductClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    public static void main(String[] args) {
        GetCategoriesRequest request = new GetCategoriesRequest();
        request.setCategoryId(null);
        String accessToken = "token-326f5b40f6f041c78bdb3c9321fd06bf-db9f108e7f5042ab88f66651a75774b8";
        if(StringUtils.isNotBlank(accessToken)){
            try {
                BaseResponse<GetCategoriseResponse> result = commonClient.execute(request,accessToken);
                if(result.isSuccess()){
                    System.out.println("接口请求成功!");
                    GetCategoriseResponse data = result.getData();
                    System.out.println("data:"+ JSON.toJSONString(data.getCategoryV3s()));
                }else{
                    System.out.println("接口请求失败!");
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
