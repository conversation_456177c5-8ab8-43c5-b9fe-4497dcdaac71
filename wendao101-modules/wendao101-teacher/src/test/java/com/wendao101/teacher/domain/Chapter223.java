package com.wendao101.teacher.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

/**
 * 章节对象 chapter
 * 
 * <AUTHOR>
 * @date 2023-07-29
 */
@Data
@Document(collection = "Chapter223")
public class Chapter223
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程主键id */
    @Excel(name = "课程主键id")
    private Long courseId;

    /** 章节名称 */
    @Excel(name = "章节名称")
    private String chapterName;

    /** 删除（0未删除1已删除） */
    @Excel(name = "删除", readConverterExp = "0=未删除1已删除")
    private Integer isDelete;


    private Date today;

    private List<Comment> list;
}
