package com.wendao101.teacher.aliyunlive;

import com.alibaba.fastjson2.JSON;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.live.model.v20161101.DescribeLiveSnapshotConfigRequest;
import com.aliyuncs.live.model.v20161101.DescribeLiveSnapshotConfigResponse;
import com.aliyuncs.profile.DefaultProfile;

public class AliyunLiveTest {
    public static void main(String[] args) {
        DefaultProfile profile = DefaultProfile.getProfile("cn-shanghai", "LTAI5tRfCNfcpvFCRaM1ydiy", "******************************");
        IAcsClient client = new DefaultAcsClient(profile);
        DescribeLiveSnapshotConfigRequest describeLiveStreamSnapshotInfoRequest = new DescribeLiveSnapshotConfigRequest();
        describeLiveStreamSnapshotInfoRequest.setDomainName("aplay.wendao101.com");
        DescribeLiveSnapshotConfigResponse describeLiveSnapshotConfigResponse = null;
        try {
            describeLiveSnapshotConfigResponse = client.getAcsResponse(describeLiveStreamSnapshotInfoRequest);
            System.out.println(JSON.toJSONString(describeLiveSnapshotConfigResponse));
        } catch (ClientException e) {
            e.printStackTrace();
        }

    }

}
