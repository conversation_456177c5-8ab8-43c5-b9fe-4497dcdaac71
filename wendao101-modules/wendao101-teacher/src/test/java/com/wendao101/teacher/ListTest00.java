package com.wendao101.teacher;

import java.util.Arrays;
import java.util.List;

public class ListTest00 {
    public static void main(String[] args) {
        List<String> stringList = Arrays.asList("example1", "doudian_promoter_12377", "doudian_promoter_12300", "doudian_promoter_123", "doudian_promoter_456", "example2");

        String result = stringList.stream()
                .filter(s -> s.startsWith("doudian_promoter_")) // 筛选以 "doudian_promoter_" 开头的字符串
                .reduce((first, second) -> second) // 使用 reduce 获取最后一个匹配的字符串
                .orElse(null); // 如果没有找到，返回 null

        System.out.println(result); // 输出结果
    }
}
