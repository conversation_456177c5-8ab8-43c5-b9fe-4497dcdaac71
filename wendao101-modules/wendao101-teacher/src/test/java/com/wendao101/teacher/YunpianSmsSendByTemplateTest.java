package com.wendao101.teacher;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;

public class YunpianSmsSendByTemplateTest {
    // 云片API密钥
    private static final String APIKEY = "102222afce402a62dd7b44276e736fff";
    private static final String ENCODING = "UTF-8";
    private static final String API_URL = "https://sms.yunpian.com/v2/sms/tpl_single_send.json";

    /**
     * 长链接转短链接
     * @param args
     * @throws IOException
     */
    public static void main000(String[] args) throws IOException {
        String shorten = shorten("https://teacher.wendao101.com/login?redirect=%2Findex_content");
        System.out.println(shorten);
        /**
         * {"code":0,"msg":"OK","short_url":{"enter_url":"https://dx5.cn/4aH5B3","short_url":"https://dx5.cn/4aH5B3","name":"2024-11-18 11:38:26 创建的短链","sid":"4aH5B3","long_url":"https://teacher.wendao101.com/login?redirect=%2Findex_content"}}
         */
    }

    public static void main(String[] args) throws IOException {
        //https://dx5.cn/4aT4Ed
        /**
         * 【问到】尊敬的用户您好！老师赠送了您《一门》课程,可以点击链接开始学习https://dx5.cn/4aT4Ed
         * //6057660
         */

//        long tplId = 6064630;
//        Map<String, String> tplParams = new HashMap<>();
//        tplParams.put("courseName", "一门");
//        tplParams.put("wxcode", "4aT4Ed");
//        try {
//            String result = tplSingleSend("18658823687", tplId, tplParams);
//            System.out.println("发送结果：" + result);
//        } catch (Exception e) {
//            System.err.println("发送失败：" + e.getMessage());
//        }

        sendClassroomRegistrationSuccess("18658823687","569845");

//        Map<String, String> params = new HashMap<String, String>();
//        params.put("apikey", APIKEY);
//        params.put("text", "【问到】尊敬的用户您好！老师赠送了您《一门》课程,可以点击链接开始学习dx5.cn/4aT4Ed");
//        params.put("mobile", "18658823687");
//        String post = post("https://sms.yunpian.com/v2/sms/single_send.json", params);
//        System.out.println(JSON.toJSONString(post));
    }


    public static void main222(String[] args) {
        String mobile = "18658823687";  // 测试手机号
        
//        // 测试验证码发送
        String code = RandomStringUtils.randomNumeric(8);
        //sendCode(mobile, code);
        //发送结果：{"code":0,"msg":"发送成功","count":1,"fee":0.04,"unit":"RMB","mobile":"18667016502","sid":80330246378}
//
//        // 测试课程审核通过通知
//        sendCourseApprovalNotice(mobile, "Java高级开发课程");
//
//        // 测试教师入驻通知
//        sendTeacherRegistrationNotice(mobile, "123456");
        
        // 测试入驻申请提交通知
        // sendApplicationSubmitNotice(mobile);
        
//        // 测试入驻申请未通过通知
//        sendApplicationRejectNotice(mobile, "资质证明材料不完整");
//
//        // 测试课堂入驻成功通知
//        sendClassroomRegistrationSuccess(mobile, "888888");
//
//        // 测试教材发货通知
//        sendMaterialShipmentNotice(mobile, "Python入门到精通", "Python核心编程教材", "SF1234567890");
//
//        // 测试拒绝退款通知
//        sendRefundRejectNotice(mobile, "ORDER2024031500001");
//
//        // 测试课程封禁通知
//        sendCourseBanNotice(mobile, "系统自动封禁", "ROLE001", "违规内容", "编程开发");
//
//        // 测试视频号购买成功通知
//        sendVideoAccountPurchaseSuccess(mobile, "Web全栈开发实战", "wx123456");
//
//        // 测试订单创建待支付通知
//        sendOrderCreatedNotice(mobile, "数据分析实战课程", "wx789012");
//
//        // 测试关注课程学习链接通知
//        sendFollowedCourseNotice(mobile, "AI人工智能入门", "wx345678");
//
//        // 测试课程助理联系方式通知
//        sendCourseAssistantNotice(mobile, "抖音", "区块链技术精讲", "张老师");
//
//        // 测试抖音课程学习链接通知
//        sendDouyinCourseLink(mobile, "微服务架构实战", "dy123456");
//
//        // 测试抖店余额不足提醒
//        sendBalanceLowNotice(mobile, "王老师", "1000");
//
//        // 测试快手平台课程审核通过通知
//        sendKuaishouCourseApprovalNotice(mobile, "前端开发进阶课程");
//
//        // 测试小红书购买成功通知
//        sendXiaohongshuPurchaseSuccess(mobile, "设计模式精讲", "USER001", "ORDER001");
//
//        // 测试快手平台课程学习链接
//        sendKuaishouCourseLink(mobile, "Spring Boot实战", "ks123456");
//
//        // 测试课程助理二维码通知
//        sendCourseAssistantQRCode(mobile, "视频号", "Docker容器技术", "COURSE001", "TEACHER001");
//
//        // 测试提现申请驳回通知
//        sendWithdrawRejectionNotice(mobile, "银行卡信息有误");
//
//        // 测试教材发货通知（含物流公司）
//        sendMaterialShipmentNoticeWithCompany(mobile, "Java并发编程", "Java并发编程实战", "YT1234567890", "圆通快递");
//
//        // 测试课程赠送通知
        sendCourseGiftNotice(mobile, "一门", code);
//
//        // 测试产品购买通知
//        sendProductPurchaseNotice(mobile, "Vue.js实战课程", "prod123456");
    }

    /**
     * 按模版发送短信
     * 模版内容：【问到】您的验证码是#code#。如非本人操作，请忽略本短信
     * @param mobile
     * @param code
     */
//    public static void sendCode(String mobile,String code) {
//        // 接收短信的手机号码
//        //String mobile = "18667016502";
//        // 模板ID
//        long tplId = 6056480;
//
//        // 构建模板参数
//        Map<String, String> tplParams = new HashMap<>();
//        tplParams.put("code", code);
//
//        try {
//            String result = tplSingleSend(mobile, tplId, tplParams);
//            System.out.println("发送结果：" + result);
//        } catch (Exception e) {
//            System.err.println("发送失败：" + e.getMessage());
//        }
//    }

    /**
     * 发送课程审核通过的通知短信
     * 模版内容：【问到】您发布的《#kcmc#》课程已经通过抖音平台审核，已成功上架可以正常售卖。
     * @param mobile 手机号码
     * @param courseName 课程名称
     */
    public static void sendCourseApprovalNotice(String mobile, String courseName) {
        // 模板ID，需要替换成实际的模板ID
        long tplId = 6056492; // 这里的模板ID需要替换成您在云片网申请的实际模板ID

        // 构建模板参数
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("kcmc", courseName);

        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送教师入驻成功的通知短信
     * 模版内容：【问到】恭喜您成功入驻问到好课，登录账号：#sjhm#,临时密码:#dlmm#,电脑端网址：https://goodteacher.wendao101.com ，推荐使用谷歌浏览器登录
     * @param mobile 手机号码（同时也是登录账号）
     * @param password 临时密码
     */
    public static void sendTeacherRegistrationNotice(String mobile, String password) {
        // 模板ID，需要替换成实际的模板ID
        long tplId = 6056488; // 这里需要替换成您在云片网申请的实际模板ID

        // 构建模板参数
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("sjhm", mobile);    // 手机号码参数
        tplParams.put("dlmm", password);  // 登录密码参数

        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送入驻申请提交通知
     * @param mobile 手机号码
     */
    public static void sendApplicationSubmitNotice(String mobile) {
        long tplId = 6056500;
        Map<String, String> tplParams = new HashMap<>();
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送入驻申请未通过通知
     * @param mobile 手机号码
     * @param reason 未通过原因
     */
    public static void sendApplicationRejectNotice(String mobile, String reason) {
        long tplId = 6056504;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("reason", reason);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送入驻成功通知（课堂版本）
     * @param mobile 手机号码
     * @param password 临时密码
     */
    public static void sendClassroomRegistrationSuccess(String mobile, String password) {
        long tplId = 6056506;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("sjhm", mobile);
        tplParams.put("dlmm", password);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送教材发货通知
     * @param mobile 手机号码
     * @param title 课程标题
     * @param material 教材名称
     * @param expNumber 物流单号
     */
    public static void sendMaterialShipmentNotice(String mobile, String title, String material, String expNumber) {
        long tplId = 6056496;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("title", title);
        tplParams.put("material", material);
        tplParams.put("expNumber", expNumber);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送拒绝退款通知
     * @param mobile 手机号码
     * @param orderId 订单号
     */
    public static void sendRefundRejectNotice(String mobile, String orderId) {
        long tplId = 6056508;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("orderId", orderId);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送课程封禁通知
     */
    public static void sendCourseBanNotice(String mobile, String systemType, String roleId, 
            String reason, String class1) {
        long tplId = 6056510;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("systemType", systemType);
        tplParams.put("roleId", roleId);
        tplParams.put("reason", reason);
        tplParams.put("class1", class1);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送视频号购买成功通知
     * @param mobile 手机号码
     * @param courseName 课程名称
     * @param wxcode 微信跳转码
     */
    public static void sendVideoAccountPurchaseSuccess(String mobile, String courseName, String wxcode) {
        long tplId = 6056512;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("courseName", courseName);
        tplParams.put("wxcode", wxcode);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送订单创建待支付通知
     * @param mobile 手机号码
     * @param courseName 课程名称
     * @param wxcode 微信跳转码
     */
    public static void sendOrderCreatedNotice(String mobile, String courseName, String wxcode) {
        long tplId = 6056522;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("courseName", courseName);
        tplParams.put("wxcode", wxcode);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送关注课程学习链接通知
     * @param mobile 手机号码
     * @param courseName 课程名称
     * @param wxcode 微信跳转码
     */
    public static void sendFollowedCourseNotice(String mobile, String courseName, String wxcode) {
        long tplId = 6056520;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("courseName", courseName);
        tplParams.put("wxcode", wxcode);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送课程购买成功助理联系方式通知
     * @param mobile 手机号码
     * @param platform 平台名称
     * @param title 课程标题
     * @param name 助理姓名
     */
    public static void sendCourseAssistantNotice(String mobile, String platform, String title, String name) {
        long tplId = 6056518;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("platform", platform);
        tplParams.put("title", title);
        tplParams.put("name", name);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送抖音课程学习链接通知
     * @param mobile 手机号码
     * @param courseName 课程名称
     * @param wxcode 跳转码
     */
    public static void sendDouyinCourseLink(String mobile, String courseName, String wxcode) {
        long tplId = 6056524;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("courseName", courseName);
        tplParams.put("wxcode", wxcode);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送抖店余额不足提醒
     * @param mobile 手机号码
     * @param teacherName 教师姓名
     * @param money 余额金额
     */
    public static void sendBalanceLowNotice(String mobile, String teacherName, String money) {
        long tplId = 6056538;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("teacherName", teacherName);
        tplParams.put("money", money);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送快手平台课程审核通过通知
     * @param mobile 手机号码
     * @param courseName 课程名称
     */
    public static void sendKuaishouCourseApprovalNotice(String mobile, String courseName) {
        long tplId = 6056534;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("kcmc", courseName);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送小红书购买成功通知
     * @param mobile 手机号码
     * @param courseName 课程名称
     * @param usercode 用户码
     * @param ordercode 订单码
     */
    public static void sendXiaohongshuPurchaseSuccess(String mobile, String courseName, 
            String usercode, String ordercode) {
        long tplId = 6056528;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("courseName", courseName);
        tplParams.put("usercode", usercode);
        tplParams.put("ordercode", ordercode);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送快手平台课程学习链接
     * @param mobile 手机号码
     * @param courseName 课程名称
     * @param wxcode 跳转码
     */
    public static void sendKuaishouCourseLink(String mobile, String courseName, String wxcode) {
        long tplId = 6056526;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("courseName", courseName);
        tplParams.put("wxcode", wxcode);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送课程助理二维码通知
     * @param mobile 手机号码
     * @param platform 平台名称
     * @param title 课程标题
     * @param cid 课程ID
     * @param tid 教师ID
     */
    public static void sendCourseAssistantQRCode(String mobile, String platform, 
            String title, String cid, String tid) {
        long tplId = 6056516;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("platform", platform);
        tplParams.put("title", title);
        tplParams.put("cid", cid);
        tplParams.put("tid", tid);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送提现申请驳回通知
     * @param mobile 手机号码
     * @param reason 驳回原因
     */
    public static void sendWithdrawRejectionNotice(String mobile, String reason) {
        long tplId = 6056494;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("reason", reason);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送教材发货通知（含物流公司）
     * @param mobile 手机号码
     * @param title 课程标题
     * @param material 教材名称
     * @param expNumber 物流单号
     * @param expName 物流公司名称
     */
    public static void sendMaterialShipmentNoticeWithCompany(String mobile, String title, 
            String material, String expNumber, String expName) {
        long tplId = 6056530;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("title", title);
        tplParams.put("material", material);
        tplParams.put("expNumber", expNumber);
        tplParams.put("expName", expName);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送课程赠送通知
     * @param mobile 手机号码
     * @param courseName 课程名称
     * @param wxcode 微信跳转码
     */
    public static void sendCourseGiftNotice(String mobile, String courseName, String wxcode) {
        long tplId = 6056532;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("courseName", courseName);
        tplParams.put("wxcode", wxcode);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送产品购买通知
     * @param mobile 手机号码
     * @param productName 产品名称
     * @param code 跳转码
     */
    public static void sendProductPurchaseNotice(String mobile, String productName, String code) {
        long tplId = 6056536;
        Map<String, String> tplParams = new HashMap<>();
        tplParams.put("productName", productName);
        tplParams.put("code", code);
        try {
            String result = tplSingleSend(mobile, tplId, tplParams);
            System.out.println("发送结果：" + result);
        } catch (Exception e) {
            System.err.println("发送失败：" + e.getMessage());
        }
    }

    public static String tplSingleSend(String mobile, long tplId, Map<String, String> tplParams) throws IOException {
        Map<String, String> params = new HashMap<>();
        params.put("apikey", APIKEY);
        params.put("mobile", mobile);
        params.put("tpl_id", String.valueOf(tplId));

        // 构建模板变量值字符串
        StringBuilder tplValue = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : tplParams.entrySet()) {
            if (!first) {
                tplValue.append("&");
            }
            tplValue.append(URLEncoder.encode("#" + entry.getKey() + "#", ENCODING))
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue(), ENCODING));
            first = false;
        }
        params.put("tpl_value", tplValue.toString());

        return post(API_URL, params);
    }

    private static String post(String url, Map<String, String> params) throws IOException {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);

        // 构建表单参数
        StringBuilder postData = new StringBuilder();
        for (Map.Entry<String, String> param : params.entrySet()) {
            if (postData.length() != 0) {
                postData.append('&');
            }
            postData.append(URLEncoder.encode(param.getKey(), ENCODING));
            postData.append('=');
            postData.append(URLEncoder.encode(param.getValue(), ENCODING));
        }

        // 设置请求头
        post.setHeader("Accept", "application/json;charset=utf-8");
        post.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

        // 设置请求体
        StringEntity entity = new StringEntity(postData.toString());
        post.setEntity(entity);

        // 发送请求并获取响应
        HttpResponse response = client.execute(post);
        String result = EntityUtils.toString(response.getEntity(), ENCODING);

        client.close();
        return result;
    }

    /**
     * 长链接转短链接
     * @param long_url
     * @return
     * @throws IOException
     */
    public static String shorten(String long_url) throws IOException{
        Map<String, String> params = new HashMap<String, String>();
        params.put("apikey", APIKEY);
        params.put("long_url", long_url);
        return post("https://sms.yunpian.com/v2/short_url/shorten.json", params);
    }

}
