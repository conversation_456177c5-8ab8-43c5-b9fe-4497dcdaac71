package com.wendao101.teacher;

import com.alibaba.fastjson.JSON;
import com.xiaohongshu.fls.opensdk.client.*;
import com.xiaohongshu.fls.opensdk.entity.BaseResponse;
import com.xiaohongshu.fls.opensdk.entity.data.DecryptedInfo;
import com.xiaohongshu.fls.opensdk.entity.data.request.BatchDecryptRequest;
import com.xiaohongshu.fls.opensdk.entity.data.response.BatchDecryptResponse;
import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class TestDecpt {
    private static final String appId = "86c5974f3a754ff9a732";
    private static final String appSecret = "a103ff558ae977d7b015bb05ccb05809";
    /**
     * AfterSaleClient
     * BoutiqueClient
     * CommonClient
     * DataClient
     * ExpressClient
     * FinanceClient
     * InventoryClient
     * InvoiceClient
     * MaterialClient
     * OauthClient
     * OrderClient
     * PackageClient
     * ProductClient
     * 创建所有的client
     */
    private static final AfterSaleClient afterSaleClient = new AfterSaleClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final BoutiqueClient boutiqueClient = new BoutiqueClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final CommonClient commonClient = new CommonClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final DataClient dataClient = new DataClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final ExpressClient expressClient = new ExpressClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final FinanceClient financeClient = new FinanceClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final InventoryClient inventoryClient = new InventoryClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final InvoiceClient invoiceClient = new InvoiceClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final MaterialClient materialClient = new MaterialClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final OauthClient oauthClient = new OauthClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final OrderClient orderClient = new OrderClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final PackageClient packageClient = new PackageClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    private static final ProductClient productClient = new ProductClient("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller", appId, "2.0", appSecret);
    public static void main(String[] args) {
        BatchDecryptRequest request11 = new BatchDecryptRequest();

        BatchDecryptRequest.baseInfo baseInfo = new BatchDecryptRequest.baseInfo();
        baseInfo.setDataTag("P733044589136267691");
        baseInfo.setEncryptedData("#AXVlCWku+0AmzJ9L5H5+1kXvMN+YRR9rezT9RZ0Qp/8=#AXVlCWku+0AmzJ9L5H5+1hUh3v8A/+kQ0aJN2G26R68DOR1tGwoq9iP+L2u3fw0bHVHnErsWvdicPlo9Edyv+9viQdRWYdm1MsGUBG+Vu1I=#2##");
        BatchDecryptRequest.baseInfo baseInfo1 = new BatchDecryptRequest.baseInfo();
        baseInfo1.setDataTag("P733044589136267691");
        baseInfo1.setEncryptedData("#AXVlCWku+0AmzJ9L5H5+1ufjGzBc2KUgkcA+Lgf/zmE=#AXVlCWku+0AmzJ9L5H5+1hUh3v8A/+kQ0aJN2G26R68DOR1tGwoq9iP+L2u3fw0bmHPq9WUhnL960sscVEpAQqEUDGOGr5St4Al0A6fzzK0=#3##");
        List<BatchDecryptRequest.baseInfo> list= new ArrayList<>();
        list.add(baseInfo);
        list.add(baseInfo1);
        request11.setActionType("1");
        request11.setAppUserId("665072d4e300000000000001");
        request11.setBaseInfos(list);
        //BatchDecryptResponse batchDecryptInfo = xhsGoodsService.getBatchDecryptInfo(request11);


        try {
            BaseResponse<BatchDecryptResponse> execute = dataClient.execute(request11, "token-326f5b40f6f041c78bdb3c9321fd06bf-db9f108e7f5042ab88f66651a75774b8");
            if(execute.isSuccess()){
                System.out.println("接口请求成功!");
                System.out.println(JSON.toJSONString(execute));
//                BatchDecryptResponse data = execute.getData();
//                List<DecryptedInfo> dataInfoList = data.getDataInfoList();
//                //GetOrderReceiverInfoResponse data = execute.getData();
//                if(CollectionUtils.isNotEmpty(data.getReceiverInfos())){
//                    //上传失败
//                    return data.getReceiverInfos().get(0);
//                }
            }else{
                System.out.println("修改价格失败!");
                System.out.println(JSON.toJSONString(execute));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
