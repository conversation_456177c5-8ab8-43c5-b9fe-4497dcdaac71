package com.wendao101.teacher;

import com.wendao101.teacher.domain.User;
import com.wendao101.teacher.service.IUserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class UserServiceTest {
    @Autowired
    private IUserService userService;

    @Test
    public void testInsert() {
        User user = new User();
        user.setPlatform(1);
        user.setOpenId("fkdshfjsdfh");
        user.setUnionId("fdhfjdshfj");
        int i = userService.insertUser(user);
        System.out.println(user.getId());
    }
}
