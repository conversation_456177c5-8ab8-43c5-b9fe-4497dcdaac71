package com.wendao101.teacher;

import com.alibaba.fastjson.JSON;
import com.doudian.open.api.address_getProvince.AddressGetProvinceRequest;
import com.doudian.open.api.address_getProvince.AddressGetProvinceResponse;
import com.doudian.open.api.brand_list.BrandListRequest;
import com.doudian.open.api.brand_list.BrandListResponse;
import com.doudian.open.api.brand_list.param.BrandListParam;
import com.doudian.open.api.coupons_syncV2.CouponsSyncV2Request;
import com.doudian.open.api.coupons_syncV2.CouponsSyncV2Response;
import com.doudian.open.api.coupons_syncV2.param.CertListItem;
import com.doudian.open.api.coupons_syncV2.param.CouponsSyncV2Param;
import com.doudian.open.api.coupons_verifyV2.CouponsVerifyV2Request;
import com.doudian.open.api.coupons_verifyV2.CouponsVerifyV2Response;
import com.doudian.open.api.coupons_verifyV2.param.CouponsVerifyV2Param;
import com.doudian.open.api.material_queryMaterialDetail.MaterialQueryMaterialDetailRequest;
import com.doudian.open.api.material_queryMaterialDetail.MaterialQueryMaterialDetailResponse;
import com.doudian.open.api.material_queryMaterialDetail.param.MaterialQueryMaterialDetailParam;
import com.doudian.open.api.material_uploadImageSync.MaterialUploadImageSyncRequest;
import com.doudian.open.api.material_uploadImageSync.MaterialUploadImageSyncResponse;
import com.doudian.open.api.material_uploadImageSync.data.MaterialUploadImageSyncData;
import com.doudian.open.api.material_uploadImageSync.param.MaterialUploadImageSyncParam;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptRequest;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptResponse;
import com.doudian.open.api.order_batchDecrypt.param.CipherInfosItem;
import com.doudian.open.api.order_batchDecrypt.param.OrderBatchDecryptParam;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailRequest;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailResponse;
import com.doudian.open.api.order_orderDetail.data.OrderOrderDetailData;
import com.doudian.open.api.order_orderDetail.data.SkuOrderListItem;
import com.doudian.open.api.order_orderDetail.param.OrderOrderDetailParam;
import com.doudian.open.api.product_addV2.ProductAddV2Request;
import com.doudian.open.api.product_addV2.ProductAddV2Response;
import com.doudian.open.api.product_addV2.param.ProductAddV2Param;
import com.doudian.open.api.product_addV2.param.SellPropertiesItem;
import com.doudian.open.api.product_addV2.param.SpecPricesV2Item;
import com.doudian.open.api.product_getCatePropertyV2.ProductGetCatePropertyV2Request;
import com.doudian.open.api.product_getCatePropertyV2.ProductGetCatePropertyV2Response;
import com.doudian.open.api.product_getCatePropertyV2.data.DataItem;
import com.doudian.open.api.product_getCatePropertyV2.data.OptionsItem;
import com.doudian.open.api.product_getCatePropertyV2.data.ProductGetCatePropertyV2Data;
import com.doudian.open.api.product_getCatePropertyV2.param.ProductGetCatePropertyV2Param;
import com.doudian.open.api.product_getCategoryPropertyValue.ProductGetCategoryPropertyValueRequest;
import com.doudian.open.api.product_getCategoryPropertyValue.ProductGetCategoryPropertyValueResponse;
import com.doudian.open.api.product_getCategoryPropertyValue.param.ProductGetCategoryPropertyValueParam;
import com.doudian.open.api.product_getProductUpdateRule.ProductGetProductUpdateRuleRequest;
import com.doudian.open.api.product_getProductUpdateRule.ProductGetProductUpdateRuleResponse;
import com.doudian.open.api.product_getProductUpdateRule.param.ProductGetProductUpdateRuleParam;
import com.doudian.open.api.product_publishPreCheck.ProductPublishPreCheckRequest;
import com.doudian.open.api.product_publishPreCheck.ProductPublishPreCheckResponse;
import com.doudian.open.api.product_publishPreCheck.param.ProductPublishPreCheckParam;
import com.doudian.open.api.shop_getShopCategory.ShopGetShopCategoryRequest;
import com.doudian.open.api.shop_getShopCategory.ShopGetShopCategoryResponse;
import com.doudian.open.api.shop_getShopCategory.param.ShopGetShopCategoryParam;
import com.doudian.open.api.sms_public_template.SmsPublicTemplateRequest;
import com.doudian.open.api.sms_public_template.SmsPublicTemplateResponse;
import com.doudian.open.api.sms_public_template.param.SmsPublicTemplateParam;
import com.doudian.open.api.sms_send.SmsSendRequest;
import com.doudian.open.api.sms_send.SmsSendResponse;
import com.doudian.open.api.sms_send.param.SmsSendParam;
import com.doudian.open.api.sms_sign_apply.SmsSignApplyRequest;
import com.doudian.open.api.sms_sign_apply.SmsSignApplyResponse;
import com.doudian.open.api.sms_sign_apply.param.SmsSignApplyParam;
import com.doudian.open.api.sms_template_apply.SmsTemplateApplyRequest;
import com.doudian.open.api.sms_template_apply.SmsTemplateApplyResponse;
import com.doudian.open.api.sms_template_apply.param.SmsTemplateApplyParam;
import com.doudian.open.api.token_create.TokenCreateRequest;
import com.doudian.open.api.token_create.TokenCreateResponse;
import com.doudian.open.api.token_create.param.TokenCreateParam;
import com.doudian.open.core.*;
import com.doudian.open.spi.demo_spi.DemoSpiRequest;
import com.doudian.open.spi.demo_spi.data.DemoSpiData;
import com.doudian.open.spi.demo_spi.param.DemoSpiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class BfydDoudianAPITest {

    public static final String access_token_str = "{\"code\":\"10000\",\"data\":{\"accessToken\":\"dn9j111t8a8-1keo7rz8v1pgy0000sly3mj\",\"encryptOperator\":\"\",\"expiresIn\":597030,\"operatorName\":\"\",\"refreshToken\":\"01py121t8a8-1keo7rz8v1pgy0000sly3mj\",\"scope\":\"SCOPE\",\"shopBizType\":0,\"shopId\":3043376,\"shopName\":\"零基础学裁剪\"},\"logId\":\"20241026142038D50FC5B2067BC1765B44\",\"msg\":\"success\",\"subCode\":\"\",\"subMsg\":\"\",\"success\":true}";
    public static void main(String[] args) {
        //getAccessTokenFromApi();
        sendSms();
        //https://z.douyin.com/xEcuol6
//        String link = "https://z.douyin.com/xEcuol6";
//        String linkLast = link.substring(link.lastIndexOf("/") + 1);
//        System.out.println(linkLast);
    }

    public static void sendSms(){
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppKey("7424461012612924978");
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppSecret("9c382f4e-d934-4d66-bed8-ae94f7494327");
        AccessToken accessToken = getAccessToken();
        SmsSendRequest request = new SmsSendRequest();
        request.setAppKey("7424461012612924978");
        SmsSendParam param = request.getParam();
        param.setSmsAccount("801e36da");
        param.setSign("零基础学裁剪");
        param.setTemplateId("ST_801e8a4f");
        param.setTemplateParam("{\"productName\":\"" + "裁剪第一阶段课程" + "\",\"learnUrl\":\"" + "z.douyin.com/xEcuol6" + "\"}");
        param.setPostTel("***********");
        SmsSendResponse response = request.execute(accessToken);
    }

    private static void checkCategory() {
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppKey("7424461012612924978");
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppSecret("9c382f4e-d934-4d66-bed8-ae94f7494327");
        AccessToken accessToken = getAccessToken();

        ProductPublishPreCheckRequest request = new ProductPublishPreCheckRequest();
        request.setAppKey("7424461012612924978");
        ProductPublishPreCheckParam param = request.getParam();
        param.setCheckTypes(Collections.singletonList("shop_use_category_access"));
        param.setCategoryId(23250L);
        ProductPublishPreCheckResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

    private static void createTemplate() {
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppKey("7424461012612924978");
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppSecret("9c382f4e-d934-4d66-bed8-ae94f7494327");
        AccessToken accessToken = getAccessToken();
        SmsTemplateApplyRequest request = new SmsTemplateApplyRequest();
        request.setAppKey("7424461012612924978");
        SmsTemplateApplyParam param = request.getParam();
        param.setSmsAccount("801e36da");
        param.setTemplateType("CN_NTC");
        param.setTemplateName("购买课程商品后短信看课");
        param.setTemplateContent("您已购买${productName}，点击链接: ${learnUrl}，老师带你学习！");
        SmsTemplateApplyResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }


    public static void createSign() {
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppKey("7424461012612924978");
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppSecret("9c382f4e-d934-4d66-bed8-ae94f7494327");
        AccessToken accessToken = getAccessToken();
        SmsSignApplyRequest request = new SmsSignApplyRequest();
        request.setAppKey("7424461012612924978");
        SmsSignApplyParam param = request.getParam();
        param.setSmsAccount("801e36da");
        param.setSign("零基础学裁剪");
        SmsSignApplyResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

    private static AccessToken getAccessToken () {
        return JSON.parseObject(access_token_str, AccessToken.class);
    }

    private static void getAccessTokenFromApi() {
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppKey("7424461012612924978");
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppSecret("9c382f4e-d934-4d66-bed8-ae94f7494327");
        //获取accessToken
        TokenCreateRequest request = new TokenCreateRequest();
        request.setAppKey("7424461012612924978");
        TokenCreateParam param = request.getParam();
        param.setCode("");
        param.setGrantType("authorization_self");
//        param.setTestShop("1");
        param.setShopId("3043376");
//        param.setAuthId("112334");
//        param.setAuthSubjectType("WuLiuShang");
        TokenCreateResponse response = request.execute(null);
        String jsonString = JSON.toJSONString(response);
        System.out.println(jsonString);
        /**
         * access_token的有效期为7天
         * {"code":"10000","data":
         * {"accessToken":"bb8f19a2-d5e7-4e4e-a0a3-7c0639030c6b","encryptOperator":"","expiresIn":604800,"operatorName":"","refreshToken":"fd41ac0b-03ed-49b7-a5f7-82ddd2b6616c","scope":"SCOPE","shopBizType":0,"shopId":184833512,"shopName":"问到课堂"},
         * "logId":"20240826165642D8A132E98C310204FD05","msg":"success","subCode":"","subMsg":"","success":true}
         */
    }
}
