package com.wendao101.teacher;

import com.wendao101.common.core.utils.livechat.ImPostUtil;
import com.wendao101.common.core.utils.livechat.TLSSigAPIv2;

import java.util.concurrent.TimeUnit;

public class TecentIM {
    private static final TLSSigAPIv2 tlsSigAPIv2 = new TLSSigAPIv2(ImPostUtil.sdkAppId, ImPostUtil.secret);
    public static void main(String[] args) {
       String sign = tlsSigAPIv2.genUserSig("share_8887777", ImPostUtil.EXPIRETIME);
        System.out.println(sign);
    }
}
