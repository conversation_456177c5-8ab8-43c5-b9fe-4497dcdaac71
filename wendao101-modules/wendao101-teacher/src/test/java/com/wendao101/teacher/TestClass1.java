package com.wendao101.teacher;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.CertAlipayRequest;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayFundTransUniTransferModel;
import com.alipay.api.domain.Participant;
import com.alipay.api.request.AlipayFundTransUniTransferRequest;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import com.mongodb.client.MongoCollection;
import org.bson.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Set;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestClass1 {
    @Autowired
    private MongoTemplate mongoTemplate;

    @Test
    public void test() {
        MongoCollection<Document> mongoCollection = mongoTemplate.createCollection("bl_comment");
    }

    /**
     * 查看集合
     */
    @Test
    public void testShowCollection() {
        Set<String> collections = mongoTemplate.getCollectionNames();
        for (String collection : collections) {
            System.out.println(collection);
        }
    }

    /**
     * 删除集合
     */
    @Test
    public void testRemoveCollection() {
        mongoTemplate.dropCollection("bl_comment");
    }

    public static void main(String[] args) {
//        int num = 7;
//        for (int i = 7; i >= 1; i--) {
//            String str = "2023-01-05";
//
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//            Calendar calendar = Calendar.getInstance();
//            calendar.set(Calendar.YEAR, 2023);
//            calendar.set(Calendar.MONTH, 0);
//            calendar.set(Calendar.DAY_OF_MONTH, 5);
//            System.out.println(calendar.getTime());
//            calendar.add(Calendar.DAY_OF_MONTH, -i);
//            Date newBeginTimeDate = calendar.getTime();
//            String format = simpleDateFormat.format(newBeginTimeDate);
//            System.out.println(format);

        // 将时分秒,毫秒域清零
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(new Date());
//            calendar.set(Calendar.HOUR_OF_DAY, 0);
//            calendar.set(Calendar.MINUTE, 0);
//            calendar.set(Calendar.SECOND, 0);
//            calendar.set(Calendar.MILLISECOND, 0);
//            Date date = calendar.getTime();

//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        //当天日期
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(new Date());
//        String date = sdf.format(calendar.getTime());
//        System.out.println(date);

//        BigDecimal percentage = BigDecimal.valueOf(10).divide(BigDecimal.valueOf(100));
//        System.out.println(percentage);
//        BigDecimal bigDecimal = new BigDecimal(100).multiply(percentage).setScale(2, RoundingMode.HALF_UP);
//        System.out.println(bigDecimal);
//        long timestamp = System.currentTimeMillis();
//        System.out.println(timestamp);

        System.out.println(100-40L);
        String result = "发放失败";
        String[] split = result.split("@@");
        System.out.println(Arrays.toString(split));
        //生成16位数字+小写英文 uuid
//            RandomStringUtils.randomAlphanumeric(16);


//        }

    }

//    @Test44555
//    public ResultMap updatewithdrawal(String id, Integer mType) throws AlipayApiException {
//        //根据提现订单id查询出提现记录对象
//        Withdraw w = userService.getwithdrawalsById(id);
//        if (w.getmManner() == 1) {
//            //调用转账到个人支付宝
//            Map<String, Object> map = alipayService.alipayWithdraw(id, w.getmMoney(), w.getuId(), w.getAccountpayeename(), w.getAccountpayee());
//            return ResultMap.ok(200, "success").put("data", map);
//        } else if (w.getmManner() == 2) {
//            //这是微信提现
//            Map<String, String> b = userService.updatewithdrawal(id, mType);
//            return ResultMap.ok(200, "success").put("data", b);
//        }
//        return ResultMap.error(305, "提现异常");
//    }
        public void test6 () throws IOException, AlipayApiException {
            String privateKey = "<-- 请填写您的应用私钥，例如：MIIEvQIBADANB ... ... -->";
            CertAlipayRequest alipayConfig = new com.alipay.api.CertAlipayRequest();
            alipayConfig.setPrivateKey(privateKey);
            alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
            alipayConfig.setAppId("<-- 请填写您的AppId，例如：**************** -->");
            alipayConfig.setCharset("UTF8");
            alipayConfig.setSignType("RSA2");
            alipayConfig.setEncryptor("");
            alipayConfig.setFormat("json");
            alipayConfig.setCertPath("<-- 请填写您的应用公钥证书文件路径，例如：/foo/appCertPublicKey_2019051064521003.crt -->");
            alipayConfig.setAlipayPublicCertPath("<-- 请填写您的支付宝公钥证书文件路径，例如：/foo/alipayCertPublicKey_RSA2.crt -->");
            alipayConfig.setRootCertPath("<-- 请填写您的支付宝根证书文件路径，例如：/foo/alipayRootCert.crt -->");
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();
            AlipayFundTransUniTransferModel model = new AlipayFundTransUniTransferModel();
            model.setOutBizNo("201806300001");
            model.setRemark("201905代发");
                model.setBusinessParams("{\"payer_show_name_use_alias\":\"true\"}");
            model.setBizScene("DIRECT_TRANSFER");
            Participant payeeInfo = new Participant();
            payeeInfo.setIdentity("****************");
            payeeInfo.setIdentityType("ALIPAY_USER_ID");
            payeeInfo.setName("黄龙国际有限公司");
            model.setPayeeInfo(payeeInfo);
            model.setTransAmount("23.00");
            model.setProductCode("TRANS_ACCOUNT_NO_PWD");
            model.setOrderTitle("201905代发");
            request.setBizModel(model);
            AlipayFundTransUniTransferResponse response = alipayClient.certificateExecute(request);
            System.out.println(response.getBody());
            if (response.isSuccess()) {
                System.out.println("调用成功");
            } else {
                System.out.println("调用失败");
                // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
                // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                // System.out.println(diagnosisUrl);
            }
        }

    }





