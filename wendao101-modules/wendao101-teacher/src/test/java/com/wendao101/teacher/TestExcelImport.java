package com.wendao101.teacher;

import cn.hutool.core.util.PhoneUtil;
import com.alibaba.fastjson2.JSON;
import com.wendao101.common.core.utils.poi.ExcelXlsxReader;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.teacher.dto.TelNumberImportDTO;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

public class TestExcelImport {
    public static void main(String[] args) {
        File file = new File("C:\\Users\\<USER>\\Desktop\\shenqing\\导入模板 (2) - 副本.xlsx");
        ExcelXlsxReader reader = new ExcelXlsxReader();
        try {
            reader.process(new FileInputStream(file));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<List<String>> rowList = reader.getRows();
        List<TelNumberImportDTO> telNumberList = importFromExcel(rowList);
        System.out.println(JSON.toJSONString(telNumberList));
        //return AjaxResult.success("导入成功!",telNumberList);
    }

    private static  List<TelNumberImportDTO> importFromExcel(List<List<String>> rowList) {
        List<TelNumberImportDTO> telNumberList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rowList)) {
            for (int i = 1; i < rowList.size(); i++) {
                List<String> list = rowList.get(i);
                System.out.println(list.size());
                if (!list.isEmpty()) {
                    System.out.println("list row:"+JSON.toJSONString(list));
                    TelNumberImportDTO telNumberImportDTO = new TelNumberImportDTO();
                    String telNumber = list.get(0).trim();
                    // 校验不为空
                    if (StringUtils.isBlank(telNumber)) {
                        continue;
                    }
                    telNumberImportDTO.setTelNumber(list.get(0).trim());
                    telNumberImportDTO.setIsCorrect(true);
                    //校验手机号是否正确
                    if (!PhoneUtil.isMobile(telNumber)) {
                        telNumberImportDTO.setErrorMessage("手机号格式不正确!");
                        telNumberImportDTO.setIsCorrect(false);
                    }
                    telNumberList.add(telNumberImportDTO);
                }
            }
        }
        return telNumberList;
    }
}
