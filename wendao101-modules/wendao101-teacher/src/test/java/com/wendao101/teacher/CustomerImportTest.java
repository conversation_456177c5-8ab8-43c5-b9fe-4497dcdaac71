package com.wendao101.teacher;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.utils.poi.ExcelUtil;
import com.wendao101.teacher.domain.QExamQuestion;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class CustomerImportTest {
    public static void main(String[] args) throws IOException {
        //读取文件内容为文本
        String fileName = "C:\\Users\\<USER>\\Desktop\\无标题1.txt";
        String s = FileUtils.readFileToString(new File(fileName), "UTF-8");
        Long teacherId = 254127478L;
        String[] split = s.split(";");
        List<QExamQuestion> qList = new ArrayList<>();
        for(String a:split){
            QExamQuestion qExamQuestion = new QExamQuestion();
            qExamQuestion.setTeacherId(teacherId);
            qExamQuestion.setAppNameType(2);
            a = a.replace("questions.push(","");
            //去掉最后一个字符
            a = a.substring(0,a.length()-1);
            //System.out.println(a);
            //转换成JSONObject
            JSONObject jsonObject = JSON.parseObject(a);
            Long id = jsonObject.getLong("id");
            //System.out.println("id:"+id);
            String string = jsonObject.getString("description");
            //System.out.println("description:"+string);
            qExamQuestion.setQuestion(string);
            //题目类型，1：单选题，2：多选题，3：判断题
            Integer type = jsonObject.getInteger("type");
            qExamQuestion.setQuestionType(type);
            String option = jsonObject.getString("option");
            if(type==3){
                //判断题
                qExamQuestion.setOptionA("正确");
                qExamQuestion.setOptionB("错误");
            }else{
                JSONObject jsonObject1 = JSON.parseObject(option);
                String a1 = jsonObject1.getString("a");
                String b1 = jsonObject1.getString("b");
                String c1 = jsonObject1.getString("c");
                String d1 = jsonObject1.getString("d");
                String e1 = jsonObject1.getString("e");
                String f1 = jsonObject1.getString("f");
                String g1 = jsonObject1.getString("g");
                String h1 = jsonObject1.getString("h");

                qExamQuestion.setOptionA(a1);
                qExamQuestion.setOptionB(b1);
                qExamQuestion.setOptionC(c1);
                qExamQuestion.setOptionD(d1);
                qExamQuestion.setOptionE(e1);
                qExamQuestion.setOptionF(f1);
                qExamQuestion.setOptionG(g1);
                qExamQuestion.setOptionH(h1);
//                System.out.println("a:"+a1);
//                System.out.println("b:"+b1);
//                System.out.println("c:"+c1);
//                System.out.println("d:"+d1);
//                System.out.println("e:"+e1);
//                System.out.println("f:"+f1);
//                System.out.println("g:"+g1);
//                System.out.println("h:"+h1);
            }
            //System.out.println("option:"+option);

            //answer
            String answer = jsonObject.getString("answer");
            JSONObject jsonObject1 = JSON.parseObject(answer);
            if(type==3){
                Boolean answer1 = jsonObject1.getBoolean("answer");
                if(answer1){
                    qExamQuestion.setAnswer("A");
                }else{
                    qExamQuestion.setAnswer("B");
                }
                String analysis = jsonObject1.getString("analysis");
                qExamQuestion.setExplain(analysis);

            }else{
                JSONArray answer1 = jsonObject1.getJSONArray("answer");
                //循环
                List<String> list = new ArrayList<>();
                for(Object o:answer1){
                    String s1 = o.toString();
                    list.add(s1);
                }
                String join = StringUtils.join(list, ",");
                qExamQuestion.setAnswer(join.toUpperCase());
                //analysis
                String analysis = jsonObject1.getString("analysis");
                qExamQuestion.setExplain(analysis);
            }
            qExamQuestion.setDifficulty(1);
            System.out.println("qExamQuestion:"+JSON.toJSONString(qExamQuestion));
            qList.add(qExamQuestion);


        }

        ExcelUtil<QExamQuestion> util = new ExcelUtil<QExamQuestion>(QExamQuestion.class);
       util.exportExcelAndFile(qList, "题库列表", "熔化焊接与热切割作业(常考题).xlsx");
        //题目Id



    }
}
