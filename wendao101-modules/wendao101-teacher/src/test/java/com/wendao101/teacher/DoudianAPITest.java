package com.wendao101.teacher;

import com.alibaba.fastjson.JSON;
import com.doudian.open.api.address_getAreasByProvince.AddressGetAreasByProvinceRequest;
import com.doudian.open.api.address_getAreasByProvince.AddressGetAreasByProvinceResponse;
import com.doudian.open.api.address_getAreasByProvince.param.AddressGetAreasByProvinceParam;
import com.doudian.open.api.address_getProvince.AddressGetProvinceRequest;
import com.doudian.open.api.address_getProvince.AddressGetProvinceResponse;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailRequest;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailResponse;
import com.doudian.open.api.afterSale_Detail.data.AfterSaleDetailData;
import com.doudian.open.api.afterSale_Detail.param.AfterSaleDetailParam;
import com.doudian.open.api.afterSale_operate.AfterSaleOperateRequest;
import com.doudian.open.api.afterSale_operate.AfterSaleOperateResponse;
import com.doudian.open.api.afterSale_operate.param.AfterSaleOperateParam;
import com.doudian.open.api.afterSale_operate.param.ItemsItem;
import com.doudian.open.api.brand_list.BrandListRequest;
import com.doudian.open.api.brand_list.BrandListResponse;
import com.doudian.open.api.brand_list.param.BrandListParam;
import com.doudian.open.api.coupons_syncV2.CouponsSyncV2Request;
import com.doudian.open.api.coupons_syncV2.CouponsSyncV2Response;
import com.doudian.open.api.coupons_syncV2.param.CertListItem;
import com.doudian.open.api.coupons_syncV2.param.CouponsSyncV2Param;
import com.doudian.open.api.coupons_verifyV2.CouponsVerifyV2Request;
import com.doudian.open.api.coupons_verifyV2.CouponsVerifyV2Response;
import com.doudian.open.api.coupons_verifyV2.param.CouponsVerifyV2Param;
import com.doudian.open.api.material_queryMaterialDetail.MaterialQueryMaterialDetailRequest;
import com.doudian.open.api.material_queryMaterialDetail.MaterialQueryMaterialDetailResponse;
import com.doudian.open.api.material_queryMaterialDetail.param.MaterialQueryMaterialDetailParam;
import com.doudian.open.api.material_uploadImageSync.MaterialUploadImageSyncRequest;
import com.doudian.open.api.material_uploadImageSync.MaterialUploadImageSyncResponse;
import com.doudian.open.api.material_uploadImageSync.data.MaterialUploadImageSyncData;
import com.doudian.open.api.material_uploadImageSync.param.MaterialUploadImageSyncParam;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptRequest;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptResponse;
import com.doudian.open.api.order_batchDecrypt.param.CipherInfosItem;
import com.doudian.open.api.order_batchDecrypt.param.OrderBatchDecryptParam;
import com.doudian.open.api.order_logisticsCompanyList.OrderLogisticsCompanyListRequest;
import com.doudian.open.api.order_logisticsCompanyList.OrderLogisticsCompanyListResponse;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailRequest;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailResponse;
import com.doudian.open.api.order_orderDetail.data.OrderOrderDetailData;
import com.doudian.open.api.order_orderDetail.data.SkuOrderListItem;
import com.doudian.open.api.order_orderDetail.param.OrderOrderDetailParam;
import com.doudian.open.api.product_addV2.ProductAddV2Request;
import com.doudian.open.api.product_addV2.ProductAddV2Response;
import com.doudian.open.api.product_addV2.param.ProductAddV2Param;
import com.doudian.open.api.product_addV2.param.SellPropertiesItem;
import com.doudian.open.api.product_addV2.param.SpecPricesV2Item;
import com.doudian.open.api.product_getCatePropertyV2.ProductGetCatePropertyV2Request;
import com.doudian.open.api.product_getCatePropertyV2.ProductGetCatePropertyV2Response;
import com.doudian.open.api.product_getCatePropertyV2.data.DataItem;
import com.doudian.open.api.product_getCatePropertyV2.data.OptionsItem;
import com.doudian.open.api.product_getCatePropertyV2.data.ProductGetCatePropertyV2Data;
import com.doudian.open.api.product_getCatePropertyV2.param.ProductGetCatePropertyV2Param;
import com.doudian.open.api.product_getCategoryPropertyValue.ProductGetCategoryPropertyValueRequest;
import com.doudian.open.api.product_getCategoryPropertyValue.ProductGetCategoryPropertyValueResponse;
import com.doudian.open.api.product_getCategoryPropertyValue.param.ProductGetCategoryPropertyValueParam;
import com.doudian.open.api.product_getProductUpdateRule.ProductGetProductUpdateRuleRequest;
import com.doudian.open.api.product_getProductUpdateRule.ProductGetProductUpdateRuleResponse;
import com.doudian.open.api.product_getProductUpdateRule.param.ProductGetProductUpdateRuleParam;
import com.doudian.open.api.shop_getShopCategory.ShopGetShopCategoryRequest;
import com.doudian.open.api.shop_getShopCategory.ShopGetShopCategoryResponse;
import com.doudian.open.api.shop_getShopCategory.param.ShopGetShopCategoryParam;
import com.doudian.open.api.sms_public_template.SmsPublicTemplateRequest;
import com.doudian.open.api.sms_public_template.SmsPublicTemplateResponse;
import com.doudian.open.api.sms_public_template.param.SmsPublicTemplateParam;
import com.doudian.open.api.sms_send.SmsSendRequest;
import com.doudian.open.api.sms_send.SmsSendResponse;
import com.doudian.open.api.sms_send.param.SmsSendParam;
import com.doudian.open.api.sms_sign_apply.SmsSignApplyRequest;
import com.doudian.open.api.sms_sign_apply.SmsSignApplyResponse;
import com.doudian.open.api.sms_sign_apply.param.SmsSignApplyParam;
import com.doudian.open.api.sms_sign_search.SmsSignSearchRequest;
import com.doudian.open.api.sms_sign_search.SmsSignSearchResponse;
import com.doudian.open.api.sms_sign_search.param.SmsSignSearchParam;
import com.doudian.open.api.sms_template_apply.SmsTemplateApplyRequest;
import com.doudian.open.api.sms_template_apply.SmsTemplateApplyResponse;
import com.doudian.open.api.sms_template_apply.param.SmsTemplateApplyParam;
import com.doudian.open.api.sms_template_apply_list.SmsTemplateApplyListRequest;
import com.doudian.open.api.sms_template_apply_list.SmsTemplateApplyListResponse;
import com.doudian.open.api.sms_template_apply_list.param.SmsTemplateApplyListParam;
import com.doudian.open.api.sms_template_search.SmsTemplateSearchRequest;
import com.doudian.open.api.sms_template_search.SmsTemplateSearchResponse;
import com.doudian.open.api.sms_template_search.param.SmsTemplateSearchParam;
import com.doudian.open.api.token_create.TokenCreateRequest;
import com.doudian.open.api.token_create.TokenCreateResponse;
import com.doudian.open.api.token_create.param.TokenCreateParam;
import com.doudian.open.core.*;
import com.doudian.open.spi.demo_spi.DemoSpiRequest;
import com.doudian.open.spi.demo_spi.data.DemoSpiData;
import com.doudian.open.spi.demo_spi.param.DemoSpiParam;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.teacher.domain.DoudianCourse;
import com.wendao101.teacher.domain.DoudianShopConfig;
import com.wendao101.teacher.domain.TTeacher;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class DoudianAPITest {
    static {
        GlobalConfig.initDefaultAppKeyAndAppSecret("7406208857338316322","7d1a5657-6a2f-431c-b90c-3d50d98dfa67");
    }
    private static final String wendao_service_tel_number = "4000061319";
    public static final String access_token_str = "{\"accessToken\":\"6n0a0k4lve1k9ohxev989b600025e6ki-11\",\"data\":{\"accessToken\":\"6n0a0k4lve1k9ohxev989b600025e6ki-11\",\"expiresIn\":604800,\"refreshToken\":\"ji86wd21tk1k9ohxev989b600025e6ki-12\",\"scope\":\"SCOPE\",\"shopBizType\":0,\"shopId\":\"129992130\",\"shopName\":\"问道知识小店\"},\"expireIn\":604800,\"logId\":\"20250412112009E37361068DD9A7800CF3\",\"refreshToken\":\"ji86wd21tk1k9ohxev989b600025e6ki-12\",\"scope\":\"SCOPE\",\"shopId\":\"129992130\",\"shopName\":\"问道知识小店\",\"success\":false}";

    //private static final String access_token_str = "{\"accessToken\":\"f9a6e056-6d6c-43b2-a660-46f022bd3be9\",\"data\":{\"accessToken\":\"f9a6e056-6d6c-43b2-a660-46f022bd3be9\",\"expiresIn\":604800,\"refreshToken\":\"cdd92c67-200e-4c15-b807-1e83af0def6b\",\"scope\":\"SCOPE\",\"shopBizType\":0L,\"shopId\":\"184833512\",\"shopName\":\"章鱼博士课堂\"},\"expireIn\":604800,\"logId\":\"202409091657030D779D32DE4C5D02DCC0\",\"refreshToken\":\"cdd92c67-200e-4c15-b807-1e83af0def6b\",\"scope\":\"SCOPE\",\"shopId\":\"184833512\",\"shopName\":\"章鱼博士课堂\",\"success\":false}";
    //private static final String access_token_str="{\"accessToken\":\"e7aa0f95-0fbf-4c1b-ad93-ecb8ce99b5fa\",\"data\":{\"accessToken\":\"e7aa0f95-0fbf-4c1b-ad93-ecb8ce99b5fa\",\"expiresIn\":604800,\"refreshToken\":\"24ad90a1-a0ed-4f46-be19-cb994026be14\",\"scope\":\"SCOPE\",\"shopBizType\":0,\"shopId\":\"188161674\",\"shopName\":\"成美知识小店\"},\"expireIn\":604800,\"logId\":\"2024091120190024F066962A5445FD231F\",\"refreshToken\":\"24ad90a1-a0ed-4f46-be19-cb994026be14\",\"scope\":\"SCOPE\",\"shopId\":\"188161674\",\"shopName\":\"成美知识小店\",\"success\":false}";
    //private static final String access_token_str="{\"code\":\"10000\",\"data\":{\"accessToken\":\"d539454d-7484-4dfe-9432-310b4a18c56a\",\"encryptOperator\":\"\",\"expiresIn\":604800,\"operatorName\":\"\",\"refreshToken\":\"56878274-e4a6-4494-b831-320d4b40f8cb\",\"scope\":\"SCOPE\",\"shopBizType\":0,\"shopId\":146046627,\"shopName\":\"问鼎知识小店\"},\"logId\":\"20240928140942A298FA23974703BB1967\",\"msg\":\"success\",\"subCode\":\"\",\"subMsg\":\"\",\"success\":true}\n";
    //private static final String access_token_str="{\"code\":\"10000\",\"data\":{\"accessToken\":\"6655ae9b-09d3-4312-8692-7d3e2588daa1\",\"encryptOperator\":\"\",\"expiresIn\":604800,\"operatorName\":\"\",\"refreshToken\":\"85074ca0-be99-4889-8ebf-650a6849e1f7\",\"scope\":\"SCOPE\",\"shopBizType\":0,\"shopId\":193036387,\"shopName\":\"问到知识小店\"},\"logId\":\"20241014162432623F5C6853116200D82E\",\"msg\":\"success\",\"subCode\":\"\",\"subMsg\":\"\",\"success\":true}";
    public static AccessToken getAccessToken () {
        return JSON.parseObject(access_token_str, AccessToken.class);
    }

//    public static void main(String[] args) {
//        AccessToken accessToken = getAccessToken();
//        ProductGetProductUpdateRuleRequest request = new ProductGetProductUpdateRuleRequest();
//        ProductGetProductUpdateRuleParam param = request.getParam();
//        param.setCategoryId(23313L);
//        //param.setSenses(1001);
//        //param.setStandardBrandId(20319L);
//        //param.setSpuId(23291L);
//        ProductGetProductUpdateRuleResponse response = request.execute(accessToken);
//        System.out.println("response:" + JSON.toJSONString(response));
//    }

    public static void main666(String[] args) {
        //146046627
        String orderIdStr="6937315347650582279";
        AccessToken accessToken = getAccessToken();
//        List<String> sList = new ArrayList<>();
//        sList.add("111");
//        sList.add("222");
//        System.out.println(StringUtils.join(sList, "|"));
        CouponsSyncV2Request couponsSyncV2Request = new CouponsSyncV2Request();
        CouponsSyncV2Param couponsSyncV2Param = couponsSyncV2Request.getParam();
        //子订单Id
        couponsSyncV2Param.setOrderId(orderIdStr);
        //couponsSyncV2Param.setSkuId(1L);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<CertListItem> listCert = new ArrayList<>();
        for(int i=0;i<17;i++){
            CertListItem certListItem = new CertListItem();
            //领取地址
            String getCouponsUrl = "https://go.wendao101.com/dd/" + "LuaKBTd"+String.valueOf(i);
            certListItem.setCertLink(getCouponsUrl);
            certListItem.setCertNo("LuaKBTd"+String.valueOf(i));
            certListItem.setGrantTime(sdf.format(new Date()));
            listCert.add(certListItem);
        }

        couponsSyncV2Param.setCertList(listCert);
        CouponsSyncV2Response couponsSyncV2Response = couponsSyncV2Request.execute(accessToken);
        System.out.println("同步卡券结果:" + com.alibaba.fastjson2.JSON.toJSONString(couponsSyncV2Response));
        //存入redis
        //redisService.setCacheObject(doudian_order_coupons_redis_prefix+orderIdStr, urlCode, 29L, TimeUnit.DAYS);
    }
    public static void main3366544(String[] args) {
        //String a = "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_f1875d8f9238bc6c7b29cccf2e12a420_sx_608972_www800-800|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_2bc83b0d043ae741a234d42c0e2466ee_sx_371096_www800-800|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_1bda2dd26af13ebe6f87d63a16fcd7ff_sx_171286_www750-750";
String a = "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_bff9e631f04c0cd2a3ebd6674bd8ee27_sx_839270_www2093-3495";
        String[] split = a.split("\\|");
        for (String s : split) {
            System.out.println(s);
        }
    }

    public static void main0002588(String[] args) {

        Long shopId = 184833512L;
        System.out.println(String.valueOf(shopId));
    }
//    public static void main(String[] args) {
//        AccessToken accessToken = getAccessToken();
//        OrderOrderDetailRequest request = new OrderOrderDetailRequest();
//        OrderOrderDetailParam param = request.getParam();
//        param.setShopOrderId("69337389598********");
//        OrderOrderDetailResponse response = request.execute(accessToken);
//        System.out.println(JSON.toJSONString(response));
//        //解密测试
//        // 69337389598********
//
//    }

//    public static void main(String[] args) {
//        //【问到课堂】您已购买《新手小白学口琴入门二十讲（全集）》，点击链接: go.wendao***.com/dd/njkWVH*Q，老师带你学习！
//        AccessToken accessToken = getAccessToken();
//        //发送短信链接
//        String smsUrl = "go.wendao101.com/dd/" + "njkWVH3Q";
//        SmsSendRequest request = new SmsSendRequest();
//        SmsSendParam param = request.getParam();
//        param.setSmsAccount("7f07982f");
//        param.setSign("问到课堂");
//        param.setTemplateId("ST_7f078d7a");
//        param.setTemplateParam("{\"productName\":\"" + "课程" + "\",\"learnUrl\":\"" + smsUrl + "\"}");
//        param.setPostTel("***********");
//        SmsSendResponse response = request.execute(accessToken);
//        System.out.println(com.alibaba.fastjson.JSON.toJSONString(response));
//    }

    public static void main66987(String[] args) {

        //List<DoudianShopConfig> configs = doudianShopConfigService.getAllUniqueDoudianShopConfigs();
            String configName = "7406208857338316322";
            GlobalConfig.getGlobalConfig(configName).setAppKey("7406208857338316322");
            GlobalConfig.getGlobalConfig(configName).setAppSecret("7d1a5657-6a2f-431c-b90c-3d50d98dfa67");
        //GlobalConfig.initDefaultAppKeyAndAppSecret("7406208857338316322","7d1a5657-6a2f-431c-b90c-3d50d98dfa67");
//        String shopIdStr = "*********";
//        TokenCreateRequest request = new TokenCreateRequest();
//        request.setAppKey("7406208857338316322");
//        TokenCreateParam param = request.getParam();
//        param.setCode("");
//        param.setGrantType("authorization_self");
//        param.setShopId(shopIdStr);
//        TokenCreateResponse response = request.execute(null);
//        String jsonString = JSON.toJSONString(response);
//        System.out.println("创建token成功!token内容:" + jsonString);
        //GlobalConfig globalConfig = GlobalConfig.getGlobalConfig("7406208857338316322");
        TokenCreateRequest request = new TokenCreateRequest();
        request.setAppKey("7406208857338316322");
        TokenCreateParam param = request.getParam();
        param.setCode("");
        param.setGrantType("authorization_self");
        param.setShopId("*********");
        TokenCreateResponse response = request.execute(null);
        String jsonString = JSON.toJSONString(response);
        System.out.println(jsonString);
    }

    public static void main699999888(String[] args) {
        String configName = "7406208857338316322";
        GlobalConfig.getGlobalConfig(configName).setAppKey("7406208857338316322");
        GlobalConfig.getGlobalConfig(configName).setAppSecret("7d1a5657-6a2f-431c-b90c-3d50d98dfa67");
        AccessToken accessToken = getAccessToken();
        SmsSignApplyRequest request = new SmsSignApplyRequest();
        request.setAppKey("7406208857338316322");
        SmsSignApplyParam param = request.getParam();
        param.setSmsAccount("7f07982f");
        param.setSign("问天知识小店");
        SmsSignApplyResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

//    public static void main(String[] args) {
//        AccessToken accessToken = getAccessToken();
//        //查询抖店数据
//        AfterSaleDetailRequest afterSaleDetailRequest = new AfterSaleDetailRequest();
//        AfterSaleDetailParam afterSaleDetailParam = afterSaleDetailRequest.getParam();
//        afterSaleDetailParam.setAfterSaleId("146604224435850700");
//        afterSaleDetailParam.setNeedOperationRecord(true);
//        AfterSaleDetailResponse afterSaleDetailResponse = afterSaleDetailRequest.execute(accessToken);
//        AfterSaleDetailData afterSaleDetailData = afterSaleDetailResponse.getData();
//        if(afterSaleDetailData.getProcessInfo().getAfterSaleInfo().getAfterSaleType()==2L){
//            //401
//            AfterSaleOperateRequest request = new AfterSaleOperateRequest();
//            AfterSaleOperateParam param = request.getParam();
//            param.setType(401);
//            ItemsItem itemsItem = new ItemsItem();
//            itemsItem.setAftersaleId("146604224435850700");
//            param.setItems(Collections.singletonList(itemsItem));
//            AfterSaleOperateResponse response = request.execute(accessToken);
//            if("10000".equals(response.getCode())){
//                System.out.println("同意仅退款成功");
//            }else{
//                System.out.println("同意仅退款失败:"+response.getMsg()+",subMsg:"+response.getSubMsg());
//            }
//        }
//        if(afterSaleDetailData.getProcessInfo().getAfterSaleInfo().getAfterSaleType()==1L&&(afterSaleDetailData.getProcessInfo().getAfterSaleInfo().getAfterSaleStatus()==6L||afterSaleDetailData.getProcessInfo().getAfterSaleInfo().getAfterSaleStatus()==27L)){
//            AfterSaleOperateRequest request = new AfterSaleOperateRequest();
//            AfterSaleOperateParam param = request.getParam();
//            param.setType(201);
//            ItemsItem itemsItem = new ItemsItem();
//            itemsItem.setAftersaleId("146604224435850700");
//            param.setItems(Collections.singletonList(itemsItem));
//            AfterSaleOperateResponse response = request.execute(accessToken);
//            if("10000".equals(response.getCode())){
//                System.out.println("同意仅退款成功");
//            }else{
//                System.out.println("同意仅退款失败:"+response.getMsg()+",subMsg:"+response.getSubMsg());
//            }
//        }else{
//            System.out.println("同意退款失败:请联系客服在问到课堂抖店处理!");
//        }
//    }
public static void main989(String[] args) {
    AccessToken accessToken = getAccessToken();
    CouponsSyncV2Request couponsSyncV2Request = new CouponsSyncV2Request();
    CouponsSyncV2Param couponsSyncV2Param = couponsSyncV2Request.getParam();
    //子订单Id
    couponsSyncV2Param.setOrderId("6934233141179979021");
    //couponsSyncV2Param.setSkuId(1L);
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    CertListItem certListItem = new CertListItem();
    //领取地址
    String getCouponsUrl = "https://go.wendao101.com/dd/UYbhU866L";
    certListItem.setCertLink(getCouponsUrl);
    certListItem.setCertNo("UYbhU866L");
    certListItem.setGrantTime(sdf.format(new Date()));
    couponsSyncV2Param.setCertList(Collections.singletonList(certListItem));
    CouponsSyncV2Response couponsSyncV2Response = couponsSyncV2Request.execute(accessToken);
    System.out.println("同步卡券结果:" + com.alibaba.fastjson2.JSON.toJSONString(couponsSyncV2Response));
    //存入redis
    //redisService.setCacheObject(doudian_order_coupons_redis_prefix+orderIdStr, urlCode, 29L, TimeUnit.DAYS);
}

    public static void main0001(String[] args) {
        AccessToken accessToken = getAccessToken();
        AddressGetProvinceRequest request = new AddressGetProvinceRequest();
        AddressGetProvinceResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

//    public static void main(String[] args) {
//        AccessToken accessToken = getAccessToken();
//        AddressGetAreasByProvinceRequest request = new AddressGetAreasByProvinceRequest();
//        AddressGetAreasByProvinceParam param = request.getParam();
//        param.setProvinceId(34L);
//        AddressGetAreasByProvinceResponse response = request.execute(accessToken);
//        System.out.println(JSON.toJSONString(response));
//    }
//public static void main(String[] args) {
//    //private AccessToken getAccessToken(Long shopId) {
//        String shopIdStr = "193036387";
//        //AccessToken accessToken = redisService.getCacheObject(doudian_access_token_prefix + shopIdStr);
//        //if (accessToken == null) {
//            TokenCreateRequest request = new TokenCreateRequest();
//            TokenCreateParam param = request.getParam();
//            param.setCode("");
//            param.setGrantType("authorization_self");
//            param.setShopId(shopIdStr);
//            TokenCreateResponse response = request.execute(null);
//            String jsonString = JSON.toJSONString(response);
//    System.out.println(jsonString);
//            //System.out.println("创建token成功!token内容:" + jsonString);
//    //AccessToken accessToken = JSON.parseObject(jsonString, AccessToken.class);
//            //redisService.setCacheObject(doudian_access_token_prefix + shopIdStr, accessToken, accessToken.getExpireIn() - 600L, TimeUnit.SECONDS);
//        //}
//        //return accessToken;
//    //}
//}

    public static void main22(String[] args) {
        List<String> list = new ArrayList<>();
        list.add("Qua4ATds");


        for(String key:list){
            AccessToken accessToken = getAccessToken();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            CouponsVerifyV2Request request = new CouponsVerifyV2Request();
            CouponsVerifyV2Param param = request.getParam();
            param.setCertNo(key);
            param.setVerifyTime(sdf.format(new Date()));
            CouponsVerifyV2Response response = request.execute(accessToken);
            System.out.println("核销卡券结果:"+ com.alibaba.fastjson2.JSON.toJSONString(response));
        }

    }

//    public static void main(String[] args) {
//        AccessToken accessToken = getAccessToken();
//        OrderOrderDetailRequest request = new OrderOrderDetailRequest();
//        OrderOrderDetailParam param = request.getParam();
//        param.setShopOrderId("6933961403201230363");
//        OrderOrderDetailResponse response = request.execute(accessToken);
//        System.out.println(JSON.toJSONString(response));
//    }

//        public static void main(String[] args) {
//        AccessToken accessToken = getAccessToken();
//            OrderLogisticsCompanyListRequest request = new OrderLogisticsCompanyListRequest();
//        //OrderOrderDetailParam param = request.getParam();
//        //param.setShopOrderId("6933961403201230363");
//            OrderLogisticsCompanyListResponse response = request.execute(accessToken);
//            //System.out.println(JSON.toJSONString(response));
//            List<com.doudian.open.api.order_logisticsCompanyList.data.DataItem> data = response.getData();
//            for (com.doudian.open.api.order_logisticsCompanyList.data.DataItem dataItem : data) {
//                //System.out.println(dataItem.getCompanyName());
//                // insert into Logistics_company (id,name,code) values (%d,'%s','%s');
//                String sqlTemplate  = "insert into doudian_logistics_company (id,name,code) values (%d,'%s','%s');";
//                String sql = String.format(sqlTemplate, dataItem.getId(), dataItem.getName(), dataItem.getCode());
//                System.out.println(sql);
//            }
//        }
public static void mainxxxxx(String[] args) {
    String picSuffix_1 = "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_ad237b49249d2b7b66e134d7219ea018_sx_370834_www1500-1500" +
            "|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_61509c3df6761f6768486c6b3262a08c_sx_482907_www1500-1500";
    String picSuffix_2 = "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_7729f1f8dd68f1f81ee83fcef6eec53e_sx_370028_www1500-1500" +
            "|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_61509c3df6761f6768486c6b3262a08c_sx_482907_www1500-1500";
    String descPrefix_1 = "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_c2a297cbb231cd43f6838e97e993afa2_sx_622934_www1500-1500" +
            "|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_2bb0de0f9cb5a1e31ab9d3fd9d5a1c24_sx_388190_www1500-1362" +
            "|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_e69ac47c8631f27655b0229efd47ed8b_sx_465111_www1500-1362" +
            "|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_0a91d85c7035f07d37fb53df5d395156_sx_524224_www1500-1500";
    String descPrefix_2 = "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_c2a297cbb231cd43f6838e97e993afa2_sx_622934_www1500-1500" + "|" +
            "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_8d056074880852dd7c0942ba0722109d_sx_378001_www1500-1362" + "|" +
            "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_e69ac47c8631f27655b0229efd47ed8b_sx_465111_www1500-1362" + "|" +
            "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/dRFxmaKx_m_0a91d85c7035f07d37fb53df5d395156_sx_524224_www1500-1500";

    //String pic = doudianCourse.getPic();
    String[] split = picSuffix_1.split("\\|");
    for (String s : split) {
        AccessToken accessToken = getAccessToken();
        MaterialUploadImageSyncRequest request = new MaterialUploadImageSyncRequest();
        MaterialUploadImageSyncParam param1 = request.getParam();
        param1.setFolderId("0");
        param1.setUrl(s);
        param1.setMaterialName(RandomStringUtils.randomAlphanumeric(10));
        //param.setFileUri("tos-cn-i-7veqoeduo3/9e1df78157524c63abf7caa9bb1e88e0");
        param1.setNeedDistinct(true);
        MaterialUploadImageSyncResponse response = request.execute(accessToken);
        //return success(response);
        /**
         * 查询分类结果打印:
         */
        System.out.println("上传图片结果打印:" + JSON.toJSONString(response));
        if (response != null) {
            if ("10000".equalsIgnoreCase(response.getCode())) {
                //成功
                MaterialUploadImageSyncData data = response.getData();
                //return success(response.getData());
                //上传素材
                //redisService.setCacheObject(doudian_metial_url_prefix + s, data, 2L, TimeUnit.DAYS);
            } else {
                //失败
                System.out.println("msg:" + response.getMsg() + ",subMsg:" + response.getSubMsg());
                //return AjaxResult.error("msg:" + response.getMsg() + ",subMsg:" + response.getSubMsg());
            }
        } else {
            //return error("上传素材失败");
            System.out.println("上传素材失败");
        }
    }
}

    public static void main9988877445555(String[] args) {
        String configName = "7406208857338316322";
        GlobalConfig.getGlobalConfig(configName).setAppKey("7406208857338316322");
        GlobalConfig.getGlobalConfig(configName).setAppSecret("7d1a5657-6a2f-431c-b90c-3d50d98dfa67");
        AccessToken accessToken = getAccessToken();
        SmsTemplateApplyRequest request = new SmsTemplateApplyRequest();
        request.setAppKey(configName);
        SmsTemplateApplyParam param = request.getParam();
        param.setSmsAccount("7f07982f");
        param.setTemplateType("CN_OTP");
        param.setTemplateName("看课验证码1");
        param.setTemplateContent("您好！课程已通过购物消息发放，验证码为：${code} ");
        SmsTemplateApplyResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

    public static void mainkkkkkkkkkkkkkkkkkkk(String[] args) {
        String encryptPostTel = "$$6QgGUV5ayd4/8sp84ey84LF93hegb+VK9IMPnlStvGh0LSLPS0HA4o9o2GbT/qFCESfI/O5A76TJKdlzodi1zeFU02PFV0HCKKeLv730bge0Le0=*CgYIASAHKAESPgo8Wb7gQSVmNQZcVLUOIatbVv06qnjqEYTUsdX6eJgZCkiWqZeKNGTmC4AiGIF2JJ6emEk8sV5SXdK65MzIGgA=$1$$";
        String configName = "7406208857338316322";
        GlobalConfig.getGlobalConfig(configName).setAppKey("7406208857338316322");
        GlobalConfig.getGlobalConfig(configName).setAppSecret("7d1a5657-6a2f-431c-b90c-3d50d98dfa67");
        AccessToken accessToken = getAccessToken();
        OrderBatchDecryptRequest request = new OrderBatchDecryptRequest();
        request.setAppKey(configName);
        OrderBatchDecryptParam param = request.getParam();
        List<CipherInfosItem> cipherInfos = new ArrayList<>();
        CipherInfosItem item = new CipherInfosItem();
        item.setAuthId("6941228281997169815");
        item.setCipherText(encryptPostTel);
        cipherInfos.add(item);
        param.setCipherInfos(cipherInfos);
        param.setNeedVirtualPhone(0L);
        OrderBatchDecryptResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }
public static void main(String[] args) {
    String configName = "7406208857338316322";
    GlobalConfig.getGlobalConfig(configName).setAppKey("7406208857338316322");
    GlobalConfig.getGlobalConfig(configName).setAppSecret("7d1a5657-6a2f-431c-b90c-3d50d98dfa67");
    AccessToken accessToken = getAccessToken();
    SmsSendRequest request = new SmsSendRequest();
    request.setAppKey(configName);
    SmsSendParam param = request.getParam();
    param.setSmsAccount("7f07982f");
    param.setSign("问到");
    param.setTemplateId("ST_83678d26");
    param.setTemplateParam("{\"code\":\"586958\"}");
//    param.setTag("回执");
    param.setPostTel("***********");
//    param.setUserExtCode("abc");
//    param.setOutboundId("17037442535v6Nz7_3u0");
//    param.setLinkId("********");
    SmsSendResponse response = request.execute(accessToken);
    System.out.println(JSON.toJSONString(response));
}

//    public static void main09234(String[] args) {
//        AccessToken accessToken = getAccessToken();
//        SmsSignApplyRequest request = new SmsSignApplyRequest();
//        SmsSignApplyParam param = request.getParam();
//        param.setSmsAccount("7406208857338316322");
//        param.setSign("问到课堂");
//        SmsSignApplyResponse response = request.execute(accessToken);
//        System.out.println(JSON.toJSONString(response));
//    }

    public static void mainKK(String[] args) {
        AccessToken accessToken = getAccessToken();
        SmsPublicTemplateRequest request = new SmsPublicTemplateRequest();
        SmsPublicTemplateParam param = request.getParam();
//        param.setSize(10L);
//        param.setPage(0L);
//        param.setTemplateId("ST_90251");
        SmsPublicTemplateResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));

//        AccessToken accessToken = getAccessToken();
//        SmsSignSearchRequest request = new SmsSignSearchRequest();
//        SmsSignSearchParam param = request.getParam();
//        param.setSmsAccount("7406208857338316322");
//        param.setLike("问到课堂");
//        param.setSize(10L);
//        param.setPage(0L);
//        SmsSignSearchResponse response = request.execute(accessToken);
//        System.out.println(JSON.toJSONString(response));
    }

    public static void main9876555(String[] args) {
        AccessToken accessToken = getAccessToken();
        OrderOrderDetailRequest request = new OrderOrderDetailRequest();
        OrderOrderDetailParam param = request.getParam();
        param.setShopOrderId("69337389598********");
        OrderOrderDetailResponse response = request.execute(accessToken);
        //System.out.println(JSON.toJSONString(response));
        //解密测试
        // 69337389598********
        OrderOrderDetailData data = response.getData();
        List<SkuOrderListItem> skuOrderList = data.getShopOrderDetail().getSkuOrderList();

        String doudianOpenId = data.getShopOrderDetail().getDoudianOpenId();
        System.out.println(doudianOpenId);

//        OpenOpenIdSwitchRequest request22 = new OpenOpenIdSwitchRequest();
//        OpenOpenIdSwitchParam param22 = request22.getParam();
//        param.setOpenId("fgdsgwerqwfdsfd");
//        param.setOpenIdType(1);
//        OpenOpenIdSwitchResponse response = request.execute(accessToken);

        for(SkuOrderListItem item:skuOrderList){
            OrderBatchDecryptRequest request1 = new OrderBatchDecryptRequest();
            OrderBatchDecryptParam param1 = request1.getParam();
//            param.setAccountId("dy1001");
            param1.setAccountType("main_account");
            List<CipherInfosItem> cipherInfos = new ArrayList<>();
            CipherInfosItem cipherInfosItem = new CipherInfosItem();
            cipherInfosItem.setAuthId(item.getOrderId());
            System.out.println("encrypt:"+item.getEncryptPostTel());
            cipherInfosItem.setCipherText(item.getEncryptPostReceiver());
            cipherInfos.add(cipherInfosItem);
            param1.setCipherInfos(cipherInfos);


//            param.setNeedVirtualPhone(1L);
            OrderBatchDecryptResponse response1 = request1.execute(accessToken);

            //System.out.println(JSON.toJSONString(response1));
        }
    }

    public static void main55(String[] args) {
        //{"code":"10000","data":{"auditStatus":1,"byteUrl":"","folderId":"0","isNew":true,"materialId":"74080122683593198430512"},"logId":"202408281031041B36EB456E7AED5EF962","msg":"success","subCode":"","subMsg":"","success":true}
        //{"code":"10000","data":{"auditStatus":1,"byteUrl":"","folderId":"0","isNew":true,"materialId":"74080122703816133660512"},"logId":"202408281031054DE88532F484187B0D0F","msg":"success","subCode":"","subMsg":"","success":true}
        //{"code":"10000","data":{"auditStatus":1,"byteUrl":"","folderId":"0","isNew":true,"materialId":"74080122716556004380512"},"logId":"20240828103105480F0D6D4830977B2418","msg":"success","subCode":"","subMsg":"","success":true}
        //{"code":"10000","data":{"auditStatus":1,"byteUrl":"","folderId":"0","isNew":true,"materialId":"74080122732385077860512"},"logId":"2024082810310553FC9EB96B366D636F90","msg":"success","subCode":"","subMsg":"","success":true}
        //{"code":"10000","data":{"auditStatus":1,"byteUrl":"","folderId":"0","isNew":true,"materialId":"74080122758943542140512"},"logId":"20240828103106759E193FAD4A0C633B95","msg":"success","subCode":"","subMsg":"","success":true}
        List<String> materialIds = new ArrayList<>();
        materialIds.add("74080122683593198430512");
        materialIds.add("74080122703816133660512");
        materialIds.add("74080122716556004380512");
        materialIds.add("74080122732385077860512");
        materialIds.add("74080122758943542140512");

        AccessToken accessToken = getAccessToken();
        for (String materialId:materialIds){
            MaterialQueryMaterialDetailRequest request = new MaterialQueryMaterialDetailRequest();
            MaterialQueryMaterialDetailParam param = request.getParam();
            param.setMaterialId(materialId);
            MaterialQueryMaterialDetailResponse response = request.execute(accessToken);
            System.out.println(JSON.toJSONString(response));
        }
        //{"code":"10000","data":{"materialInfo":{"auditRejectDesc":"","auditStatus":3,"byteUrl":"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_d15e5ca52bc915b9a1192145284bf2ac_sx_347589_www600-600","createTime":"2024-08-28 10:31:05","deleteTime":"","folderId":"0","materialId":"74080122683593198430512","materialType":"photo","materilName":"素描本图片1.png","operateStatus":1,"originUrl":"https://1319546384.vod-qcloud.com/8f958275vodsh1319546384/faf87a021397757891284948845/9VJkhjpH5ZUA.png","photoInfo":{"format":"png","height":600,"width":600},"size":348160,"updateTime":"2024-08-28 10:31:05"}},"logId":"20240828103618997BC442A95E7C7B2ECB","msg":"success","subCode":"","subMsg":"","success":true}
        //{"code":"10000","data":{"materialInfo":{"auditRejectDesc":"","auditStatus":3,"byteUrl":"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_26fe011b79b4ccd603a8751ed7205b64_sx_396121_www600-600","createTime":"2024-08-28 10:31:05","deleteTime":"","folderId":"0","materialId":"74080122703816133660512","materialType":"photo","materilName":"素描本图片5.png","operateStatus":1,"originUrl":"https://1319546384.vod-qcloud.com/8f958275vodsh1319546384/a95352bc1397757891283747537/A90prxrHPVwA.png","photoInfo":{"format":"png","height":600,"width":600},"size":396288,"updateTime":"2024-08-28 10:31:05"}},"logId":"2024082810361817A0DFC4B79E9464F68C","msg":"success","subCode":"","subMsg":"","success":true}
        //{"code":"10000","data":{"materialInfo":{"auditRejectDesc":"","auditStatus":3,"byteUrl":"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_748ff72336efa43d1b753227fa6ca1cd_sx_577987_www600-600","createTime":"2024-08-28 10:31:05","deleteTime":"","folderId":"0","materialId":"74080122716556004380512","materialType":"photo","materilName":"素描本图片3.png","operateStatus":1,"originUrl":"https://1319546384.vod-qcloud.com/8f958275vodsh1319546384/5c59269b1397757891282747906/7quHECrUpjYA.png","photoInfo":{"format":"png","height":600,"width":600},"size":578560,"updateTime":"2024-08-28 10:31:06"}},"logId":"20240828103618B9478057933A0365028D","msg":"success","subCode":"","subMsg":"","success":true}
        //{"code":"10000","data":{"materialInfo":{"auditRejectDesc":"","auditStatus":3,"byteUrl":"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_d8ab4ea2b91be495411bfac6dd59960c_sx_775269_www600-600","createTime":"2024-08-28 10:31:06","deleteTime":"","folderId":"0","materialId":"74080122732385077860512","materialType":"photo","materilName":"素描本图片2.png","operateStatus":1,"originUrl":"https://1319546384.vod-qcloud.com/30a20baevodcq1319546384/37d4cf1c1397757891285278383/famrCcGWRHEA.png","photoInfo":{"format":"png","height":600,"width":600},"size":776192,"updateTime":"2024-08-28 10:31:07"}},"logId":"2024082810361834999D4433522B5DFA63","msg":"success","subCode":"","subMsg":"","success":true}
        //{"code":"10000","data":{"materialInfo":{"auditRejectDesc":"","auditStatus":3,"byteUrl":"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_b67c532df0c08208d674e40fe307a321_sx_539551_www600-600","createTime":"2024-08-28 10:31:06","deleteTime":"","folderId":"0","materialId":"74080122758943542140512","materialType":"photo","materilName":"素描本图片4.png","operateStatus":1,"originUrl":"https://1319546384.vod-qcloud.com/30a20baevodcq1319546384/fb3cebdc1397757891284981248/1ex2nvA7X9QA.png","photoInfo":{"format":"png","height":600,"width":600},"size":539648,"updateTime":"2024-08-28 10:31:07"}},"logId":"202408281036184B961CE732BF855EC9D2","msg":"success","subCode":"","subMsg":"","success":true}
    }


    public static void main33(String[] args) {
        Map<String,String > filemap = new HashMap<>();
        filemap.put("https://1319546384.vod-qcloud.com/8f958275vodsh1319546384/faf87a021397757891284948845/9VJkhjpH5ZUA.png","素描本图片1.png");
        filemap.put("https://1319546384.vod-qcloud.com/30a20baevodcq1319546384/37d4cf1c1397757891285278383/famrCcGWRHEA.png","素描本图片2.png");
        filemap.put("https://1319546384.vod-qcloud.com/8f958275vodsh1319546384/5c59269b1397757891282747906/7quHECrUpjYA.png","素描本图片3.png");
        filemap.put("https://1319546384.vod-qcloud.com/30a20baevodcq1319546384/fb3cebdc1397757891284981248/1ex2nvA7X9QA.png","素描本图片4.png");
        filemap.put("https://1319546384.vod-qcloud.com/8f958275vodsh1319546384/a95352bc1397757891283747537/A90prxrHPVwA.png","素描本图片5.png");

        AccessToken accessToken = getAccessToken();
        for(Map.Entry<String,String> e:filemap.entrySet()){
            MaterialUploadImageSyncRequest request = new MaterialUploadImageSyncRequest();
            MaterialUploadImageSyncParam param = request.getParam();
            param.setFolderId("0");
            param.setUrl(e.getKey());
            param.setMaterialName(e.getValue());
            //param.setFileUri("tos-cn-i-7veqoeduo3/9e1df78157524c63abf7caa9bb1e88e0");
            param.setNeedDistinct(false);
            MaterialUploadImageSyncResponse response = request.execute(accessToken);
            System.out.println(JSON.toJSONString(response));
            //{"code":"10000","data":{"auditStatus":1,"byteUrl":"","folderId":"0","isNew":true,"materialId":"74080122683593198430512"},"logId":"202408281031041B36EB456E7AED5EF962","msg":"success","subCode":"","subMsg":"","success":true}
            //{"code":"10000","data":{"auditStatus":1,"byteUrl":"","folderId":"0","isNew":true,"materialId":"74080122703816133660512"},"logId":"202408281031054DE88532F484187B0D0F","msg":"success","subCode":"","subMsg":"","success":true}
            //{"code":"10000","data":{"auditStatus":1,"byteUrl":"","folderId":"0","isNew":true,"materialId":"74080122716556004380512"},"logId":"20240828103105480F0D6D4830977B2418","msg":"success","subCode":"","subMsg":"","success":true}
            //{"code":"10000","data":{"auditStatus":1,"byteUrl":"","folderId":"0","isNew":true,"materialId":"74080122732385077860512"},"logId":"2024082810310553FC9EB96B366D636F90","msg":"success","subCode":"","subMsg":"","success":true}
            //{"code":"10000","data":{"auditStatus":1,"byteUrl":"","folderId":"0","isNew":true,"materialId":"74080122758943542140512"},"logId":"20240828103106759E193FAD4A0C633B95","msg":"success","subCode":"","subMsg":"","success":true}
        }

    }

    public static void main44(String[] args) {
        AccessToken accessToken = getAccessToken();
        ProductGetCatePropertyV2Request request = new ProductGetCatePropertyV2Request();
        ProductGetCatePropertyV2Param param = request.getParam();
        param.setCategoryLeafId(27558L);
        ProductGetCatePropertyV2Response response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

    public static void main02(String[] args) {
        AccessToken accessToken = getAccessToken();
        ProductGetCategoryPropertyValueRequest request = new ProductGetCategoryPropertyValueRequest();
        ProductGetCategoryPropertyValueParam param = request.getParam();
        param.setCategoryId(27558L);
        param.setPropertyId(2778L);
        ProductGetCategoryPropertyValueResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

    public static void main0088(String[] args) {
        AccessToken accessToken = getAccessToken();
        BrandListRequest request = new BrandListRequest();
        BrandListParam param = request.getParam();
//        param.setCategories([123]);
//        param.setOffset(0L);
//        param.setSize(10L);
//        param.setSort(0);
//        param.setStatus(2);
//        param.setFullBrandInfo(true);
        param.setCategoryId(27558L);
//        param.setQuery("wu");
//        param.setBrandIds([123]);
        BrandListResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));

    }

    public static void main9999(String[] args) {
        AccessToken accessToken = getAccessToken();
        ProductGetCatePropertyV2Request request = new ProductGetCatePropertyV2Request();
        ProductGetCatePropertyV2Param param = request.getParam();
        param.setCategoryLeafId(27558L);
        ProductGetCatePropertyV2Response response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

    public static void main_de(String[] args) {
        //getCateProperty
        AccessToken accessToken = getAccessToken();
        ProductGetCatePropertyV2Request request = new ProductGetCatePropertyV2Request();
        ProductGetCatePropertyV2Param param = request.getParam();
        param.setCategoryLeafId(27558L);
        ProductGetCatePropertyV2Response response = request.execute(accessToken);
        /**
         * 查询分类结果打印:
         */
        System.out.println("查询分类属性结果打印:" + JSON.toJSONString(response));
        if (response != null) {
            if ("10000".equalsIgnoreCase(response.getCode())) {
                //做一下排序处理
                List<DataItem> data = response.getData().getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    //重要属性
                    List<DataItem> dList = data.stream().filter(item -> item.getImportantType() == 1L).collect(Collectors.toList());
                    //非重要属性
                    List<DataItem> collect1 = data.stream().filter(item -> item.getImportantType() == 0L).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(collect1)){
                        //必填
                        List<DataItem> collect2 = collect1.stream().filter(item -> item.getRequired() == 1L).collect(Collectors.toList());
                        //select multi_select text timestamp timerange
                        //输入text、单选select、多选multi_select、时间戳timestamp、时间段timerange
                        List<DataItem> select = collect2.stream().filter(item -> "select".equals(item.getType())).collect(Collectors.toList());
                        List<DataItem> multi_select = collect2.stream().filter(item -> "multi_select".equals(item.getType())).collect(Collectors.toList());
                        List<DataItem> text = collect2.stream().filter(item -> "text".equals(item.getType())).collect(Collectors.toList());
                        List<DataItem> timestamp = collect2.stream().filter(item -> "timestamp".equals(item.getType())).collect(Collectors.toList());
                        List<DataItem> timerange = collect2.stream().filter(item -> "timerange".equals(item.getType())).collect(Collectors.toList());
                        dList.addAll(select);
                        dList.addAll(multi_select);
                        dList.addAll(text);
                        dList.addAll(timestamp);
                        dList.addAll(timerange);
                        // 非必填
                        List<DataItem> collect3 = collect1.stream().filter(item -> item.getRequired() == 0L).collect(Collectors.toList());
                        List<DataItem> select2 = collect3.stream().filter(item -> "select".equals(item.getType())).collect(Collectors.toList());
                        List<DataItem> multi_select2 = collect3.stream().filter(item -> "multi_select".equals(item.getType())).collect(Collectors.toList());
                        List<DataItem> text2 = collect3.stream().filter(item -> "text".equals(item.getType())).collect(Collectors.toList());
                        List<DataItem> timestamp2 = collect3.stream().filter(item -> "timestamp".equals(item.getType())).collect(Collectors.toList());
                        List<DataItem> timerange2 = collect3.stream().filter(item -> "timerange".equals(item.getType())).collect(Collectors.toList());
                        dList.addAll(select2);
                        dList.addAll(multi_select2);
                        dList.addAll(text2);
                        dList.addAll(timestamp2);
                        dList.addAll(timerange2);
                    }
                    //对里面的options排序
                    for (DataItem item : dList) {
                        if (CollectionUtils.isNotEmpty(item.getOptions())) {
                            List<OptionsItem> orderedOptionList = item.getOptions().stream().sorted(Comparator.comparing(OptionsItem::getSequence)).collect(Collectors.toList());
                            item.setOptions(orderedOptionList);
                        }
                    }

                    for(DataItem di:dList){
                        System.out.println(di.getPropertyName()+",required:"+di.getRequired()+",important:"+di.getImportantType());
                    }
                }


                //成功
                //return success(data);
            } else {
                //失败
                //return AjaxResult.error("msg:" + response.getMsg() + ",subMsg:" + response.getSubMsg());
            }
        } else {
            //return error("查询抖店分类属性失败");
        }
    }
    public static void main9998888(String[] args) {
        AccessToken accessToken = getAccessToken();

        ProductAddV2Param param1 = new ProductAddV2Param();

        param1.setOutProductId(587458769854L);
        param1.setProductType(0L);
        //27558L
        param1.setCategoryLeafId(27558L);
        /**
         * 查属性
         */
        List<DataItem> catProperty = getCatProperty(accessToken, param1.getCategoryLeafId());

        {
            //组装属性
            //删除非必填属性
            catProperty.removeIf(item->item.getRequired()!=1L);
            for(DataItem item1:catProperty){
                Long propertyId = item1.getPropertyId();
                String propertyName = item1.getPropertyName();
                Long diyType = item1.getDiyType();
                //System.out.println(JSON.toJSONString(item1.getPropertyId()));
            }
//                if(item1.getRequired()==1L){
//
//                }
//            }
        }
//        if(param1.getProductType()==0L){
//            return;
//        }

        //名称
        param1.setName("美术素描作业本-新品1号");
        /**
         * https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_d15e5ca52bc915b9a1192145284bf2ac_sx_347589_www600-600
         * https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_26fe011b79b4ccd603a8751ed7205b64_sx_396121_www600-600
         * https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_748ff72336efa43d1b753227fa6ca1cd_sx_577987_www600-600
         * https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_d8ab4ea2b91be495411bfac6dd59960c_sx_775269_www600-600
         * https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_b67c532df0c08208d674e40fe307a321_sx_539551_www600-600
         */
        param1.setPic("https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_d15e5ca52bc915b9a1192145284bf2ac_sx_347589_www600-600|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_26fe011b79b4ccd603a8751ed7205b64_sx_396121_www600-600|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_748ff72336efa43d1b753227fa6ca1cd_sx_577987_www600-600|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_d8ab4ea2b91be495411bfac6dd59960c_sx_775269_www600-600|https://p3-aio.ecombdimg.com/obj/ecom-shop-material/nXWZPYZh_m_b67c532df0c08208d674e40fe307a321_sx_539551_www600-600");
        param1.setDescription(param1.getPic());
        //String product_format_new ="{\\\"1687\\\":[{\\\"Value\\\":596120136,\\\"Name\\\":\\\"无品牌\\\",\\\"PropertyId\\\":1687,\\\"PropertyName\\\":\\\"品牌\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}],\\\"716\\\":[{\\\"Value\\\":9421,\\\"Name\\\":\\\"成人\\\",\\\"PropertyId\\\":716,\\\"PropertyName\\\":\\\"适用人群\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}],\\\"21\\\":[{\\\"Value\\\":0,\\\"Name\\\":\\\"江老师\\\",\\\"PropertyId\\\":21,\\\"PropertyName\\\":\\\"主讲老师\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}],\\\"2132\\\":[{\\\"Value\\\":31056,\\\"Name\\\":\\\"自动发货\\\",\\\"PropertyId\\\":2132,\\\"PropertyName\\\":\\\"发货形式\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}],\\\"271\\\":[{\\\"Value\\\":0,\\\"Name\\\":\\\"https://wap.wendao101.com/#/pages_details/details/details?course_id=28264\\\",\\\"PropertyId\\\":271,\\\"PropertyName\\\":\\\"观看路径\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}],\\\"2778\\\":[{\\\"Value\\\":32020,\\\"Name\\\":\\\"线上录播\\\",\\\"PropertyId\\\":2778,\\\"PropertyName\\\":\\\"教学形式\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}],\\\"433\\\":[{\\\"Value\\\":0,\\\"Name\\\":\\\"抖音课堂\\\",\\\"PropertyId\\\":433,\\\"PropertyName\\\":\\\"学习平台\\\",\\\"diy_type\\\":1,\\\"measure_info\\\":null}],\\\"560\\\":[{\\\"Value\\\":36476,\\\"Name\\\":\\\"随到随学\\\",\\\"PropertyId\\\":560,\\\"PropertyName\\\":\\\"开课形式\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}],\\\"671\\\":[{\\\"Value\\\":31161,\\\"Name\\\":\\\"录播课\\\",\\\"PropertyId\\\":671,\\\"PropertyName\\\":\\\"培训班类型\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}],\\\"716\\\":[{\\\"Value\\\":9421,\\\"Name\\\":\\\"成人\\\",\\\"PropertyId\\\":716,\\\"PropertyName\\\":\\\"适用人群\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}],\\\"772\\\":[{\\\"Value\\\":30864,\\\"Name\\\":\\\"素描\\\",\\\"PropertyId\\\":772,\\\"PropertyName\\\":\\\"培训类别\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}],\\\"954\\\":[{\\\"Value\\\":0,\\\"Name\\\":\\\"200\\\",\\\"PropertyId\\\":954,\\\"PropertyName\\\":\\\"总课时\\\",\\\"diy_type\\\":0,\\\"measure_info\\\":null}]}";
        //String product_format_new ="{\\\"1687\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"无品牌\\\",\\\"diy_type\\\":1}],\\\"2778\\\":[{\\\"value\\\":32020,\\\"name\\\":\\\"线上录播\\\",\\\"diy_type\\\":1}],\\\"2132\\\":[{\\\"value\\\":31056,\\\"name\\\":\\\"自动发货\\\",\\\"diy_type\\\":1}],\\\"560\\\":[{\\\"value\\\":36476,\\\"name\\\":\\\"随到随学\\\",\\\"diy_type\\\":1}],\\\"271\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"https://wap.wendao101.com/#/pages_details/details/details?course_id=28264\\\",\\\"diy_type\\\":0}],\\\"671\\\":[{\\\"value\\\":31161,\\\"name\\\":\\\"录播课\\\",\\\"diy_type\\\":1}],\\\"433\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"问到课堂\\\",\\\"diy_type\\\":1}],\\\"21\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"江老师\\\",\\\"diy_type\\\":1}],\\\"954\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"10\\\",\\\"diy_type\\\":1}]}";
        String product_format_new="{\"1687\":[{\"value\":0,\"name\":\"无品牌\",\"diy_type\":0}],\"2778\":[{\"value\":32020,\"name\":\"线上录播\",\"diy_type\":1}],\"2132\":[{\"value\":31056,\"name\":\"自动发货\",\"diy_type\":1}],\"560\":[{\"value\":36476,\"name\":\"随到随学\",\"diy_type\":1}],\"271\":[{\"value\":0,\"name\":\"https://wap.wendao101.com/#/pages_details/details/details?course_id=28264\",\"diy_type\":0}],\"671\":[{\"value\":31161,\"name\":\"录播课\",\"diy_type\":1}],\"433\":[{\"value\":0,\"name\":\"问到课堂\",\"diy_type\":1}],\"21\":[{\"value\":0,\"name\":\"江老师\",\"diy_type\":1}],\"954\":[{\"value\":0,\"name\":\"10\",\"diy_type\":1}]}";
        param1.setProductFormatNew(product_format_new);
        //param1.setProductFormatNew("{\\\"405\\\":[{\\\"value\\\":27664,\\\"name\\\":\\\"复习资料\\\",\\\"diy_type\\\":0}],\\\"449\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"佚名\\\",\\\"diy_type\\\":0}],\\\"501\\\":[{\\\"value\\\":7310,\\\"name\\\":\\\"否\\\",\\\"diy_type\\\":0}],\\\"855\\\":[{\\\"value\\\":61683,\\\"name\\\":\\\"北京出版社\\\",\\\"diy_type\\\":0}],\\\"1088\\\":[{\\\"value\\\":407,\\\"name\\\":\\\"小学五年级\\\",\\\"diy_type\\\":0}],\\\"1319\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"1\\\",\\\"diy_type\\\":0}],\\\"1601\\\":[{\\\"value\\\":13911,\\\"name\\\":\\\"通用版\\\",\\\"diy_type\\\":0}],\\\"1618\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"9787218122861\\\",\\\"diy_type\\\":0}],\\\"1831\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"小学英语看图说话写话二年级\\\",\\\"diy_type\\\":0}],\\\"2000\\\":[{\\\"value\\\":34762,\\\"name\\\":\\\"无\\\",\\\"diy_type\\\":0}],\\\"2229\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"1\\\",\\\"diy_type\\\":0}],\\\"2763\\\":[{\\\"value\\\":25193,\\\"name\\\":\\\"英语\\\",\\\"diy_type\\\":0}],\\\"3271\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"1\\\",\\\"diy_type\\\":0}],\\\"3296\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"16.80元\\\",\\\"diy_type\\\":0}]}");
        System.out.println(product_format_new);
//        if(param1.getProductType()==0L){
//            return;
//        }



        //商品详情组装
        ProductAddV2Request request = new ProductAddV2Request();
        ProductAddV2Param param = request.getParam();
        param.setOuterProductId(String.valueOf(param1.getOutProductId()));
        //0-普通，3-虚拟，6玉石闪购，7云闪购
        //是否虚拟商品
        boolean isVirtual = false;
//        if(param1.getProductType()!=3&&param1.getProductType()!=0){
//            return error("商品类型错误,只能选普通和虚拟商品!值为0和3");
//        }
//        if(param1.getProductType()==3){
//            isVirtual = true;
//        }
        param.setProductType(param1.getProductType());
        param.setCategoryLeafId(param1.getCategoryLeafId());
        param.setName(param1.getName());
        //param.setRecommendRemark("这个商品很好啊");
        //商品轮播图，多张图片用 \"|\" 分开，第一张图为主图，最多5张，至少600x600，大小不超过5M。
        param.setPic(param1.getPic());
        param.setDescription(param1.getDescription());
        //支付方式，0货到付款 1在线支付，2，货到付款+在线支付
        param.setPayType(1L);
        if(!isVirtual){
            param.setDeliveryMethod(1);
        }
        //param.setCdfCategory("1");
        //1 减库存类型：1-拍下减库存 2-付款减库存
        param.setReduceType(1L);
//        param.setAssocIds("1|2|3");
        //运费模板id，传0表示包邮，通过/freightTemplate/list接口获取
        param.setFreightId(0L);
        //param.setWeight(1000d);
        //param.setWeightUnit(1L);
        /**
         * delivery_delay_day： 承诺发货时间，单位是天,不传则默认为2天。现货发货(presell_type=0)和阶梯发货模式(presell_type=2)时必填，支持传入9999 、1、 2 （分别表示当日发、次日发、48小时发），具体支持传入的参数范围/product/getProductUpdateRule
         */
        if(!isVirtual){
            param.setDeliveryDelayDay(1L);
        }

        param.setPresellType(0L);
//        param.setPresellDelay(7L);
//        param.setPresellEndTime("2020-02-21 18:54:27");
        //param.setSupply7dayReturn(该字段将在2023年4月30日下线，请开发使用最新的after_sale_service字段传值；L);
        //客服电话号码
        param.setMobile(wendao_service_tel_number);
        param.setCommit(true);
//        param.setRemark("备注");
        param.setOutProductId(param1.getOutProductId());
        //规格字段都不填
        //param.setSpecName("颜色-尺码");
        //param.setSpecs("颜色|红色,黑色^尺码|S,M");
        param.setSpecs("默认|默认");
        //List<SpecPricesV2Item> specPricesV2 = new ArrayList<>();
        SpecPricesV2Item item = new SpecPricesV2Item();
        item.setSkuStatus(true);
        item.setSkuType(0L);
        item.setStockNum(999L);
        item.setPrice(2000L);

        SellPropertiesItem sellPropertiesItem = new SellPropertiesItem();
        sellPropertiesItem.setPropertyName("默认");
        sellPropertiesItem.setValueName("默认");
        item.setSellProperties(Collections.singletonList(sellPropertiesItem));
        //specPricesV2.add(item);
        param.setSpecPricesV2(Collections.singletonList(item));
        //param.setSpecPrices("[{\\\"spec_detail_name1\\\":\\\"默认\\\",\\\"stock_num\\\":9999,\\\"price\\\":2000,\\\"code\\\":\\\"\\\",\\\"step_stock_num\\\":0,\\\"supplier_id\\\":\\\"\\\",\\\"outer_sku_id\\\":\\\"\\\"}]");
        //param.setSpecPrices("[{\\\"spec_detail_name1\\\":\\\"红色\\\",\\\"spec_detail_name2\\\":\\\"S\\\",\\\"spec_detail_name3\\\":\\\"\\\",\\\"stock_num\\\":11,\\\"price\\\":100,\\\"code\\\":\\\"\\\",\\\"step_stock_num\\\":0,\\\"supplier_id\\\":\\\"\\\",\\\"outer_sku_id\\\":\\\"\\\",\\\"delivery_infos\\\":[{\\\"info_type\\\":\\\"weight\\\",\\\"info_value\\\":\\\"100\\\",\\\"info_unit\\\":\\\"mg\\\"}]},{\\\"spec_detail_name1\\\":\\\"红色\\\",\\\"spec_detail_name2\\\":\\\"M\\\",\\\"spec_detail_name3\\\":\\\"\\\",\\\"stock_num\\\":22,\\\"price\\\":100,\\\"code\\\":\\\"\\\",\\\"step_stock_num\\\":0,\\\"supplier_id\\\":\\\"\\\",\\\"outer_sku_id\\\":\\\"\\\",\\\"delivery_infos\\\":[{\\\"info_type\\\":\\\"weight\\\",\\\"info_value\\\":\\\"100\\\",\\\"info_unit\\\":\\\"mg\\\"}]},{\\\"spec_detail_name1\\\":\\\"黑色\\\",\\\"spec_detail_name2\\\":\\\"S\\\",\\\"spec_detail_name3\\\":\\\"\\\",\\\"stock_num\\\":44,\\\"price\\\":100,\\\"code\\\":\\\"\\\",\\\"step_stock_num\\\":0,\\\"supplier_id\\\":\\\"\\\",\\\"outer_sku_id\\\":\\\"\\\",\\\"delivery_infos\\\":[{\\\"info_type\\\":\\\"weight\\\",\\\"info_value\\\":\\\"100\\\",\\\"info_unit\\\":\\\"mg\\\"}]},{\\\"spec_detail_name1\\\":\\\"黑色\\\",\\\"spec_detail_name2\\\":\\\"M\\\",\\\"spec_detail_name3\\\":\\\"\\\",\\\"stock_num\\\":55,\\\"price\\\":100,\\\"code\\\":\\\"\\\",\\\"step_stock_num\\\":0,\\\"supplier_id\\\":\\\"\\\",\\\"outer_sku_id\\\":\\\"\\\",\\\"delivery_infos\\\":[{\\\"info_type\\\":\\\"weight\\\",\\\"info_value\\\":\\\"100\\\",\\\"info_unit\\\":\\\"mg\\\"}]}]");
        //param.setSpecPic("img_url,img_url,img_url");
        //每个用户每次下单限购件数
        //param.setMaximumPerOrder(1L);
        //每个用户累计限购件数
        //param.setLimitPerBuyer(1L);
        //每个用户每次下单至少购买的件数
        //param.setMinimumPerOrder(1L);
        param.setProductFormatNew(param1.getProductFormatNew());
        //param.setSpuId(param1.getOutProductId());
//        param.setAppointDeliveryDay(2L);
//        param.setThirdUrl("http://img.alicdn.com/xxxx");
//        param.setExtra("略");
//        param.setSrc("略");
        param.setStandardBrandId(596120136L);
//        if(isVirtual){
//            param.setNeedCheckOut(true);
//            //设置卡券信息
//        }

//        param.setCarVinCode("*****************");
//        param.setPresellConfigLevel(3L);
        //充值模式
//        if(isVirtual){
//            //param.setNeedRechargeMode(true);
//            //param.setAccountTemplateId("122112");
//        }

        //param.setPresellDeliveryType(1L);
        //param.setWhiteBackGroundPicUrl("http://aaaa");
        //param.setLongPicUrl("http://aaaa");
        //param.setAfterSaleService(Collections.singletonMap("supply_day_return_selector", "7"));
        //param.setSellChannel(Collections.singletonList(0L));
        param.setStartSaleType(0L);
        //param.setMaterialVideoId("vaaaa");
        //提取方式新字段，推荐使用。"0": 普通商品-使用物流发货, "1": 虚拟商品-无需物流与电子交易凭证, "2": 虚拟商品-使用电子交易凭证, "3": 虚拟商品-充值直连
        if(isVirtual){
            param.setPickupMethod("2");
        }else{
            param.setPickupMethod("0");
        }

        //param.setSizeInfoTemplateId(101L);
        //param.setSubstituteGoodsUrl("https://xxx.xxx.xxx");
        //param.setSaleChannelType("sameAsOffline");
//        param.setStoreId(12345L);
//        param.setMainProductId(3121213121212L);
//        param.setSaleLimitId(123L);
//        param.setNamePrefix("钛钢木质耳饰");
//        param.setReferencePrice(12300L);
        //param.setMainImageThreeToFour("img_url1|img_url2|img_url3");
//        param.setIsC2bSwitchOn(true);
//        param.setMicroAppId("abcde");
//        param.setIsAutoCharge(false);
//        param.setShortProductName("新品牛肉干");
//        param.setWithSkuType(true);
//        param.setNameSuffix("36.9度");
//        param.setUseBrandName(false);
//        param.setPriceHasTax("1");
//        param.setBizKind(2L);
        ProductAddV2Response response = request.execute(accessToken);
        /**
         * 查询分类结果打印:
         */
        System.out.println("添加商品结果打印:"+JSON.toJSONString(response));
//        if(response!=null){
//            if("10000".equalsIgnoreCase(response.getCode())){
//                //成功
//                return success(response.getData());
//            }else{
//                //失败
//                return AjaxResult.error("msg:"+response.getMsg() + ",subMsg:"+response.getSubMsg());
//            }
//        }else{
//            return error("查询抖店分类失败");
//        }
    }

    private static List<DataItem> getCatProperty(AccessToken accessToken, Long categoryLeafId) {
        ProductGetCatePropertyV2Request request = new ProductGetCatePropertyV2Request();
        ProductGetCatePropertyV2Param param = request.getParam();
        param.setCategoryLeafId(categoryLeafId);
        ProductGetCatePropertyV2Response response = request.execute(accessToken);
//        System.out.println(JSON.toJSONString(response));
        ProductGetCatePropertyV2Data data = response.getData();
        return data.getData();
    }

    public static void main788443(String[] args) {
        /**
         * 使用refresh_token刷新access_token
         * 使用场景：
         * 1、在 access_token过期时，使用 refresh_token可以获取新的acces_token 和 refresh_token；可以保证一直是有效的access_token。
         * 2、access_token有效期7天，refresh_token用于刷新access_token的刷新令牌，有效期：14 天；
         *
         * 注意点：
         * 1.、在 access_token 过期前1h之前，ISV使用 refresh_token 刷新时，会返回原来的 access_token 和 refresh_token，但是二者有效期不会变；
         * 2、在 access_token 过期前1h之内，ISV使用 refresh_token 刷新时，会返回新的 access_token 和 refresh_token，但是原来的 access_token 和 refresh_token 继续有效一个小时；
         * 3、在 access_token 过期后，ISV使用 refresh_token 刷新时，将获得新的 acces_token 和 refresh_token，同时原来的 acces_token 和 refresh_token 失效；
         *
         * 推荐使用SDK进行token刷新：https://op.jinritemai.com/docs/guide-docs/1041/1072#doc_anchor0.7581191566268553
         */
        //获取accessToken
        TokenCreateRequest request = new TokenCreateRequest();
        TokenCreateParam param = request.getParam();
        param.setCode("");
        param.setGrantType("authorization_self");
//        param.setTestShop("1");
        param.setShopId("146046627");
//        param.setAuthId("112334");
//        param.setAuthSubjectType("WuLiuShang");
        TokenCreateResponse response = request.execute(null);
        String jsonString = JSON.toJSONString(response);
        System.out.println(jsonString);
        /**
         * access_token的有效期为7天
         * {"code":"10000","data":
         * {"accessToken":"bb8f19a2-d5e7-4e4e-a0a3-7c0639030c6b","encryptOperator":"","expiresIn":604800,"operatorName":"","refreshToken":"fd41ac0b-03ed-49b7-a5f7-82ddd2b6616c","scope":"SCOPE","shopBizType":0,"shopId":184833512,"shopName":"问到课堂"},
         * "logId":"20240826165642D8A132E98C310204FD05","msg":"success","subCode":"","subMsg":"","success":true}
         */
    }

    /**
     * 获取三级类目
     * @param args
     */
    public static void main2(String[] args) {
        ShopGetShopCategoryRequest request = new ShopGetShopCategoryRequest();
        ShopGetShopCategoryParam param = request.getParam();
        param.setCid(20077L);
        String accessTokenStr = "{\"code\":\"10000\",\"data\":{\"accessToken\":\"bb8f19a2-d5e7-4e4e-a0a3-7c0639030c6b\",\"encryptOperator\":\"\",\"expiresIn\":604800,\"operatorName\":\"\",\"refreshToken\":\"fd41ac0b-03ed-49b7-a5f7-82ddd2b6616c\",\"scope\":\"SCOPE\",\"shopBizType\":0,\"shopId\":184833512,\"shopName\":\"问到课堂\"},\"logId\":\"20240826165642D8A132E98C310204FD05\",\"msg\":\"success\",\"subCode\":\"\",\"subMsg\":\"\",\"success\":true}";
        AccessToken accessToken = JSON.parseObject(accessTokenStr, AccessToken.class);
        ShopGetShopCategoryResponse response = request.execute(accessToken);

        System.out.println(JSON.toJSONString(response));
    }

    /**
     * ===================SPI使用说明===================
     * SDK会根据接口名称自动生成对应的Request和Response实体类，
     * 开发者对接时只需像Request里注册业务处理handler，SDK会自
     * 动完成签名计算和校验逻辑
     *
     * 假设需要实现的SPI为：demo/spi GET，以Spring MVC为例：
     */
    //@RequestMapping(path = "demo/spi", method = {RequestMethod.GET})
    //@ResponseBody
    public String demoSpi(HttpServletRequest httpServletRequest) {
        return DoudianOpSpi.config(DemoSpiRequest.class, bizHandler, httpServletRequest).responseJson();
    }

//    public void demoApi() {
//        GlobalConfig.initAppKey("7406208857338316322");
//        GlobalConfig.initAppSecret("7d1a5657-6a2f-431c-b90c-3d50d98dfa67");
//        AccessToken accessToken= AccessTokenBuilder.parse("xxxxxxxxxx", "123123");
//
//    }

    //商品中的图片必须是素材中心图片

    /**
     * 定义bizHandler，bizHandler用来处理接收到服务器请求之后，
     * 需要处理的业务逻辑，SDK会将服务器下发的业务参数（param_json）
     * 封装成对应的Param类实例，并放入DoudianOpSpiContext中，开发
     * 者可以通过context获取封装好的数据。SDK也会将需要返回的数据封
     * 装成Data类实例，开发者可以通过context.getData()获取该实例，
     * 并设置其中的字段，最终返回给开放平台服务器。
     *
     * 在本示例中（demo/spi），Param对象为 DemoSpiParam，Data对象
     * 为 DemoSpiData
     */
    private final DoudianOpSpiBizHandler bizHandler = new DoudianOpSpiBizHandler() {
        @Override
        public void handle(DoudianOpSpiContext context) {

            // 1. 获取入参对象
            DemoSpiParam param = context.getParam();

            // 2. 业务处理逻辑
            // ...

            // 3. 设置data数据(可选)
            DemoSpiData data = context.getData();
            // data.setXXX()

            // 4. 设置处理结果（成功还是失败，二选一）
            // a.处理成功
            context.wrapSuccess();
            // b. 处理失败
            context.wrapError(100002L, "系统错误");
        }
    };
}
