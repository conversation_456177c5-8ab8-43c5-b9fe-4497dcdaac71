package com.wendao101.teacher.aliyunlive;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.live.model.v20161101.DescribeLiveDomainConfigsRequest;
import com.aliyuncs.live.model.v20161101.DescribeLiveDomainConfigsResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.wendao101.common.core.aliyunlive.StreamUrls;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.TimeUnit;

public class LiveUrlGenerator {

    public StreamUrls generateUrls(String appName, String streamName, String uid, String rand) {
        // 推流域名和播流域名
        String pushDomain = "apush.wendao101.com";
        String playDomain = "aplay.wendao101.com";

        // 延播流名称后缀
        String delaySuffix = "-alidelay";
        String delayStreamName = streamName + delaySuffix;

        // 鉴权参数
        //String uid = "0";
        //String rand = "0";
        int validMinutes = 1440;  // 有效期1440分钟（24小时）

        // 生成过期时间戳
        long exp = System.currentTimeMillis() / 1000 + TimeUnit.MINUTES.toSeconds(validMinutes);

        // 获取鉴权密钥
        String pushKey = getAuthKeyForDomain(pushDomain);
        String playKey = getAuthKeyForDomain(playDomain);

        // 公共路径
        String basePath = "/" + appName + "/" + streamName;
        String delayBasePath = "/" + appName + "/" + delayStreamName;

        // 生成推流地址
        String authKeyPush = generateAuthKey(basePath, pushKey, exp, rand, uid);

        // 生成原画播放地址
        StreamUrls urls = new StreamUrls();
        setPlayUrls(urls, playDomain, basePath, playKey, exp, rand, uid, false);

        // 生成延播播放地址
        setPlayUrls(urls, playDomain, delayBasePath, playKey, exp, rand, uid, true);

        // 设置推流地址
        urls.setRtmpPushUrl("rtmp://" + pushDomain + basePath + "?auth_key=" + authKeyPush);
        urls.setRtsPushUrl("artc://" + pushDomain + basePath + "?auth_key=" + authKeyPush);
        urls.setSrtPushUrl(generateSrtUrl(pushDomain, basePath, authKeyPush));

        return urls;
    }

    private void setPlayUrls(StreamUrls urls, String domain, String basePath,
                             String key, long exp, String rand, String uid, boolean isDelay) {
        // 生成播放地址鉴权参数
        String authKeyNormal = generateAuthKey(basePath, key, exp, rand, uid);
        String authKeyFlv = generateAuthKey(basePath + ".flv", key, exp, rand, uid);
        String authKeyM3u8 = generateAuthKey(basePath + ".m3u8", key, exp, rand, uid);

        if (isDelay) {
            // 设置延播播放地址
            urls.setDelayRtmpPlayUrl("rtmp://" + domain + basePath + "?auth_key=" + authKeyNormal);
            urls.setDelayFlvPlayUrl("https://" + domain + basePath + ".flv?auth_key=" + authKeyFlv);
            urls.setDelayM3u8PlayUrl("https://" + domain + basePath + ".m3u8?auth_key=" + authKeyM3u8);
            urls.setDelayRtsPlayUrl("artc://" + domain + basePath + "?auth_key=" + authKeyNormal);
        } else {
            // 设置原画播放地址
            urls.setRtmpPlayUrl("rtmp://" + domain + basePath + "?auth_key=" + authKeyNormal);
            urls.setFlvPlayUrl("https://" + domain + basePath + ".flv?auth_key=" + authKeyFlv);
            urls.setM3u8PlayUrl("https://" + domain + basePath + ".m3u8?auth_key=" + authKeyM3u8);
            urls.setRtsPlayUrl("artc://" + domain + basePath + "?auth_key=" + authKeyNormal);
        }
    }

    private String generateAuthKey(String path, String secretKey, long exp, String rand, String uid) {
        String original = path + "-" + exp + "-" + rand + "-" + uid + "-" + secretKey;
        return exp + "-" + rand + "-" + uid + "-" + DigestUtils.md5Hex(original);
    }

    private String generateSrtUrl(String domain, String path, String authKey) {
        return "srt://" + domain + ":1105?streamid=#!::h=" + domain + ",r=" + path + "?auth_key=" + authKey + ",m=publish";
    }

    // 从配置或阿里云API获取鉴权密钥
    private String getAuthKeyForDomain(String domain) {
        if (StringUtils.isBlank(domain)) {
            domain = "aplay.wendao101.com";
        }
        /**
         * 需要将<>内容替换成实际使用的值,获取到鉴权Key后就可以对URL进行加密。
         * 生成推流地址时，要使用推流域名的鉴权Key。
         *
         * 生成播放地址时，要使用播流域名的鉴权Key。
         */
        DefaultProfile profile = DefaultProfile.getProfile("cn-shanghai", "LTAI5tRfCNfcpvFCRaM1ydiy", "******************************");
        IAcsClient client = new DefaultAcsClient(profile);
        DescribeLiveDomainConfigsRequest describeLiveDomainConfigsRequest = new DescribeLiveDomainConfigsRequest();
        describeLiveDomainConfigsRequest.setDomainName(domain);
        describeLiveDomainConfigsRequest.setFunctionNames("aliauth");

        DescribeLiveDomainConfigsResponse describeLiveStreamSnapshotInfoResponse = null;
        try {
            describeLiveStreamSnapshotInfoResponse = client.getAcsResponse(describeLiveDomainConfigsRequest);
        } catch (ClientException e) {
            e.printStackTrace();
        }
        //鉴权key
        String key = "";
        //String key2 = "";
        for (DescribeLiveDomainConfigsResponse.DomainConfig.FunctionArg f : describeLiveStreamSnapshotInfoResponse.getDomainConfigs().get(0).getFunctionArgs()) {
            //主KEY
            if ("auth_key1".equals(f.getArgName())) {
                key = f.getArgValue();
            }
            //副KEY
//            if ("auth_key2".equals(f.getArgName())) {
//                key2 = f.getArgValue();
//            }
            //System.out.println(JSON.toJSONString(f));
        }
        //System.out.println(key);
        //System.out.println(key2);
        return key;
    }
}
