package com.wendao101.teacher.aliyunlive;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.live.model.v20161101.DescribeLiveDomainConfigsRequest;
import com.aliyuncs.live.model.v20161101.DescribeLiveDomainConfigsResponse;
import com.aliyuncs.profile.DefaultProfile;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AliyunLiveTest2 {
    public static void main(String[] args) {
        /**
         * 播放地址
         * RTMP 格式
         * rtmp://aplay.wendao101.com/live/test?auth_key=1749175645-0-0-215c8a0f3c0e30bab5f3a36c12a436cc
         * FLV 格式
         * https://aplay.wendao101.com/live/test.flv?auth_key=1749175645-0-0-ebd36e6fc144999ebb979a9763856b7f
         * M3U8 格式
         * https://aplay.wendao101.com/live/test.m3u8?auth_key=1749175645-0-0-f059f62486148b28d1d9e0d2e148cf0d
         * RTS 格式
         * artc://aplay.wendao101.com/live/test?auth_key=1749175645-0-0-215c8a0f3c0e30bab5f3a36c12a436cc
         */
        Map<String,String> formatMap = new LinkedHashMap<>();
        formatMap.put("RTMP", "");
        formatMap.put("FLV", ".flv");
        formatMap.put("M3U8", ".m3u8");
        formatMap.put("RTS", "");

        Map<String,String> protocolMap = new LinkedHashMap<>();
        protocolMap.put("RTMP", "rtmp");
        protocolMap.put("FLV", ".https");
        protocolMap.put("M3U8", "https");
        protocolMap.put("RTS", "artc");


        String domain = "aplay.wendao101.com";
        //固定住参数
        long exp = 1749181455;
        // 修改1：将uid替换为实际用户ID（假设userId是业务中的用户ID）
        String uid = "0"; // 例如：String.valueOf(user.getUserId());
        String mainKey = getMainKey(domain);
        // 修改2：生成随机数rand（使用UUID）
        String rand = "0"; // 移除中划线

        for(Map.Entry<String,String> entry : formatMap.entrySet()){
            //按各个格式组装path,并生成auth_key
            String path = "/live/test"+entry.getValue();
            String authKey = getAuthKey(path,mainKey,exp,uid,rand);
            //按entry.getKey()结合protocolMap打印格式名称和url
            System.out.println(entry.getKey()+" 格式："+protocolMap.get(entry.getKey())+"://"+domain+path+"?auth_key="+authKey);
        }

    }
    public static void generatePushUrls(String[] args) {
        String domain = "apush.wendao101.com";
        String mainKey = getMainKey(domain);
        String path = "/live/test";
        long exp = 1749175645;
        // 修改1：将uid替换为实际用户ID（假设userId是业务中的用户ID）
        String uid = "0"; // 例如：String.valueOf(user.getUserId());

        // 修改2：生成随机数rand（使用UUID）
        String rand = "0"; // 移除中划线
        String authKey = getAuthKey(path,mainKey,exp,uid,rand);
        System.out.println(authKey);
        /**
         * 推流地址
         * RTMP 格式
         * rtmp://apush.wendao101.com/live/test?auth_key=1749175645-0-0-8656853159105f39a2ba87aefdff5413
         * RTS 格式
         * artc://apush.wendao101.com/live/test?auth_key=1749175645-0-0-8656853159105f39a2ba87aefdff5413
         * SRT 格式
         * srt://apush.wendao101.com:1105?streamid=#!::h=apush.wendao101.com,r=/live/test?auth_key=1749175645-0-0-8656853159105f39a2ba87aefdff5413,m=publish
         */
        //RTMP 格式
        String rtmpUrl = "rtmp://" + domain + path+"?auth_key="+authKey;
        //RTS 格式
        String rtsUrl = "artc://" + domain + path+"?auth_key="+authKey;
        //SRT 格式
        String srtUrl = "srt://" + domain + ":1105?streamid=#!::h=" + domain + ",r="+path+"?auth_key=" + authKey + ",m=publish";
        //按格式名称打印
        System.out.println("RTMP 格式：" + rtmpUrl);
        System.out.println("RTS 格式：" + rtsUrl);
        System.out.println("SRT 格式：" + srtUrl);

    }
    /**
     * 在此之前您可以通过鉴权URL组成了解鉴权URL结构。以下示例代码以RTMP协议为例，生成鉴权URL。
     * @param args
     */
    public static void main0(String[] args) {
        String defaultPushProtocol = "rtmp";
        String domain = "apush.wendao101.com";
        String defaultAppName = "live";
        String streamName = RandomStringUtils.randomAlphabetic(8);
        // 待加密的推/播流地址，example.aliyundoc.com为推/播流域名，liveApp为AppName，liveStream为StreamName
        // 推流和播流URL采用同样的加密方法
        // AppName或StreamName不超过256字符，支持数字、大小写字母、短划线（-）、下划线（_）、等号（=）。
        String uri = defaultPushProtocol+"://"+domain+"/"+defaultAppName+"/"+streamName;
        // 鉴权Key，推流地址使用推流域名URL鉴权Key，播流地址使用播流域名URL鉴权Key
        String mainKey = getMainKey(domain);
        System.out.println(mainKey);
        // exp值为UNIX时间戳，单位秒，最终失效时间由该值加上域名URL鉴权有效时长决定
        // 比如您在此处设置exp为：当前时间+24*3600秒,也就是24小时，那最终失效时间为：当前时间+24*3600秒+域名URL鉴权有效时长。如果设置exp为：当前时间，那最终失效时间为：当前时间+域名URL鉴权有效时长
        long exp = System.currentTimeMillis() / 1000 + 24*3600;
        // 修改1：将uid替换为实际用户ID（假设userId是业务中的用户ID）
        String uid = "778899"; // 例如：String.valueOf(user.getUserId());

        // 修改2：生成随机数rand（使用UUID）
        String rand = UUID.randomUUID().toString().replace("-", ""); // 移除中划线
        String authUri = aAuth(uri, mainKey, exp,uid,rand);
        System.out.printf("URL : %s\nAuth: %s", uri, authUri);

    }

    //第一步，获取鉴权Key
    public static String getMainKey(String domain) {
        if(StringUtils.isBlank(domain)){
            domain = "aplay.wendao101.com";
        }
        /**
         * 需要将<>内容替换成实际使用的值,获取到鉴权Key后就可以对URL进行加密。
         * 生成推流地址时，要使用推流域名的鉴权Key。
         *
         * 生成播放地址时，要使用播流域名的鉴权Key。
         */
        DefaultProfile profile = DefaultProfile.getProfile("cn-shanghai", "LTAI5tRfCNfcpvFCRaM1ydiy", "******************************");
        IAcsClient client = new DefaultAcsClient(profile);
        DescribeLiveDomainConfigsRequest describeLiveDomainConfigsRequest=new DescribeLiveDomainConfigsRequest();
        describeLiveDomainConfigsRequest.setDomainName(domain);
        describeLiveDomainConfigsRequest.setFunctionNames("aliauth");

        DescribeLiveDomainConfigsResponse describeLiveStreamSnapshotInfoResponse = null;
        try {
            describeLiveStreamSnapshotInfoResponse = client.getAcsResponse(describeLiveDomainConfigsRequest);
        } catch (ClientException e) {
            e.printStackTrace();
        }
//鉴权key
        String key="";

        String key2="";


        for(DescribeLiveDomainConfigsResponse.DomainConfig.FunctionArg f:describeLiveStreamSnapshotInfoResponse.getDomainConfigs().get(0).getFunctionArgs()){
            //主KEY
            if("auth_key1".equals(f.getArgName())){
                key=f.getArgValue();
            }
            //副KEY
            if("auth_key2".equals(f.getArgName())){
                key2=f.getArgValue();
            }
            //System.out.println(JSON.toJSONString(f));
        }

        System.out.println(key);
        System.out.println(key2);
        return key;


    }

    private static String md5Sum(String src) {
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        md5.update(StandardCharsets.UTF_8.encode(src));
        return String.format("%032x", new BigInteger(1, md5.digest()));
    }
    private static String aAuth(String uri, String key, long exp,String uid,String rand) {
        String pattern = "^(rtmp://)?([^/?]+)(/[^?]*)?(\\\\?.*)?$";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(uri);
        String scheme = "", host = "", path = "", args = "";
        if (m.find()) {
            scheme = m.group(1) == null ? "rtmp://" : m.group(1);
            host = m.group(2) == null ? "" : m.group(2);
            path = m.group(3) == null ? "/" : m.group(3);
            args = m.group(4) == null ? "" : m.group(4);
        } else {
            System.out.println("NO MATCH");
        }

//        String rand = "0";  // "0" by default, other value is ok
//        String uid = "0";   // "0" by default, other value is ok
        String sString = String.format("%s-%s-%s-%s-%s", path, exp, rand, uid, key);
        String hashValue = md5Sum(sString);
        String authKey = String.format("%s-%s-%s-%s", exp, rand, uid, hashValue);
        if (args.isEmpty()) {
            return String.format("%s%s%s%s?auth_key=%s", scheme, host, path, args, authKey);
        } else {
            return String.format("%s%s%s%s&auth_key=%s", scheme, host, path, args, authKey);
        }
    }

    private static String getAuthKey(String path, String key, long exp,String uid,String rand) {
        String sString = String.format("%s-%s-%s-%s-%s", path, exp, rand, uid, key);
        String hashValue = md5Sum(sString);
        return String.format("%s-%s-%s-%s", exp, rand, uid, hashValue);
    }
}
