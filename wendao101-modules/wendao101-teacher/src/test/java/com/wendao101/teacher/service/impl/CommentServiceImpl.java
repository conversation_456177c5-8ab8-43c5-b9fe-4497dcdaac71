package com.wendao101.teacher.service.impl;

import com.wendao101.teacher.dao.repository.CommentDao;
import com.wendao101.teacher.domain.Comment;
import com.wendao101.teacher.service.CommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CommentServiceImpl implements CommentService {
    @Autowired
    private CommentDao commentDao;
    @Override
    public void save(Comment comment) {
        commentDao.save(comment);
    }

    @Override
    public List<Comment> findAll() {
        List<Comment> comments = commentDao.findAll();
        return comments;
    }

    @Override
    public List<Comment> findByCommentContentAndCommentUser(String content, Integer userId) {
        return commentDao.findByCommentContentAndCommentUser(content,userId);
    }
}
