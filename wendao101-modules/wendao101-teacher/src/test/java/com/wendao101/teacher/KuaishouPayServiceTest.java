package com.wendao101.teacher;

import com.alibaba.fastjson2.JSON;
import com.wendao101.common.core.kuaishou.pay.CreateOrderDTO;
import com.wendao101.common.core.kuaishou.pay.CreateOrderResult;
import com.wendao101.douyin.api.feign.KuaishouPayService;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class KuaishouPayServiceTest {
    @Autowired
    private KuaishouPayService kuaishouPayService;

    /**
     *     <add key="ksAppSecert" value="9c5QO0SdLXmPKWHVtbj65Q" />
     *     <add key="ksAppId" value="ks685954519696767360" />
     *
     *         <add key="ksAppSecert" value="9c5QO0SdLXmPKWHVtbj65Q" />
     *     <add key="ksAppId" value="ks685954519696767360" />
     *
     *     {"attach":"ks_pay","detail":"测试商品","expire_time":600,"goods_detail_url":"/page/index/anima","multi_copies_goods_info":"[{\"copies\":1}]","notify_url":"https://www.wendao101.com/ksPayNotify","open_id":"f18d6db7dd8bf64c14bf57244173ea64","out_order_no":"123456789","sign":"5cfae0117f658b5758cbda1c100116a4","subject":"测试商品","total_amount":1000,"type":1298}
     */
    @org.junit.Test
    public void testCreateOrder(){
        /**
         * 张宏亮 11-22 18:14:38
         * 快手appid:ks699183842184529589
         *
         * 张宏亮 11-22 18:16:49
         * jkqH4SscXW6wkBuaQWACGQ
         */
        String appId = "ks685954519696767360";
        String appSecret = "9c5QO0SdLXmPKWHVtbj65Q";
        String accessToken = "ChFvYXV0aC5hY2Nlc3NUb2tlbhIwIn69XcErU5aZwL-TPm9AhPR7UmR5iksZGVmMvOHK5ffRKax-H1muo3WU9k_X_t31GhINVY3cQVhC4YDnv5YNyfZ0IKwiII3LHQXTibnZKsH7Vk0cLfPm8eqQTdwRkIwv7g-aMQy5KAUwAQ";
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setOpen_id("f18d6db7dd8bf64c14bf57244173ea64");
        createOrderDTO.setOut_order_no("123456789");
        createOrderDTO.setTotal_amount(1000);
        createOrderDTO.setSubject("测试商品");
        createOrderDTO.setType(1298);
        createOrderDTO.setDetail("测试商品");


        createOrderDTO.setExpire_time(600);
        // createOrderDTO.setGoods_detail_url("/page/index/anima");
        createOrderDTO.setNotify_url("https://www.wendao101.com/ksPayNotify");
        //createOrderDTO.setAttach("ks_pay");



        //createOrderDTO.setSign(createOrderDTO.createSign(appId,appSecret,));
        System.out.println(JSON.toJSONString(createOrderDTO));
        CreateOrderResult createOrderResult = kuaishouPayService.createOrder(appId, accessToken, createOrderDTO);
        System.out.println(JSON.toJSONString(createOrderResult));
    }
}
