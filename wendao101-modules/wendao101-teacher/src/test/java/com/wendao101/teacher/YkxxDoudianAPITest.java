package com.wendao101.teacher;

import com.alibaba.fastjson.JSON;
import com.doudian.open.api.product_publishPreCheck.ProductPublishPreCheckRequest;
import com.doudian.open.api.product_publishPreCheck.ProductPublishPreCheckResponse;
import com.doudian.open.api.product_publishPreCheck.param.ProductPublishPreCheckParam;
import com.doudian.open.api.sms_send.SmsSendRequest;
import com.doudian.open.api.sms_send.SmsSendResponse;
import com.doudian.open.api.sms_send.param.SmsSendParam;
import com.doudian.open.api.sms_sign_apply.SmsSignApplyRequest;
import com.doudian.open.api.sms_sign_apply.SmsSignApplyResponse;
import com.doudian.open.api.sms_sign_apply.param.SmsSignApplyParam;
import com.doudian.open.api.sms_template_apply.SmsTemplateApplyRequest;
import com.doudian.open.api.sms_template_apply.SmsTemplateApplyResponse;
import com.doudian.open.api.sms_template_apply.param.SmsTemplateApplyParam;
import com.doudian.open.api.token_create.TokenCreateRequest;
import com.doudian.open.api.token_create.TokenCreateResponse;
import com.doudian.open.api.token_create.param.TokenCreateParam;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.GlobalConfig;

import java.util.Collections;

public class YkxxDoudianAPITest {

    public static final String access_token_str = "{\"code\":\"10000\",\"data\":{\"accessToken\":\"8q06112yvdjj-1kfukxx7ui2at0000smaxbz\",\"encryptOperator\":\"\",\"expiresIn\":604800,\"operatorName\":\"\",\"refreshToken\":\"2frq122yvdjj-1kfukxx7ui2at0000smaxbz\",\"scope\":\"SCOPE\",\"shopBizType\":0,\"shopId\":*********,\"shopName\":\"杨工教育店铺\"},\"logId\":\"2024110210235912143159ACE9FA69A0D9\",\"msg\":\"success\",\"subCode\":\"\",\"subMsg\":\"\",\"success\":true}";
    public static void main(String[] args) {
        //getAccessTokenFromApi();
        //sendSms();
        //getAccessTokenFromApi();
        //createSign();
        sendSms();
        //https://z.douyin.com/xEcuol6
//        String link = "https://z.douyin.com/xEcuol6";
//        String linkLast = link.substring(link.lastIndexOf("/") + 1);
//        System.out.println(linkLast);
    }

    public static void sendSms(){
        GlobalConfig.getGlobalConfig("7428763670991832613").setAppKey("7428763670991832613");
        GlobalConfig.getGlobalConfig("7428763670991832613").setAppSecret("cbc16f16-7018-4661-96c1-48d9d8faafb4");
        AccessToken accessToken = getAccessToken();
        SmsSendRequest request = new SmsSendRequest();
        request.setAppKey("7428763670991832613");
        SmsSendParam param = request.getParam();
        param.setSmsAccount("804090b6");
        param.setSign("杨工教育店铺");
        param.setTemplateId("ST_8040b358");
        param.setTemplateParam("{\"productName\":\"" + "基础课程" + "\",\"learnUrl\":\"" + "z.douyin.com/xEcu7oi" + "\"}");
        param.setPostTel("***********");
        SmsSendResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

    private static void checkCategory() {
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppKey("7424461012612924978");
        GlobalConfig.getGlobalConfig("7424461012612924978").setAppSecret("9c382f4e-d934-4d66-bed8-ae94f7494327");
        AccessToken accessToken = getAccessToken();

        ProductPublishPreCheckRequest request = new ProductPublishPreCheckRequest();
        request.setAppKey("7424461012612924978");
        ProductPublishPreCheckParam param = request.getParam();
        param.setCheckTypes(Collections.singletonList("shop_use_category_access"));
        param.setCategoryId(23250L);
        ProductPublishPreCheckResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

    private static void createTemplate() {
        GlobalConfig.getGlobalConfig("7428763670991832613").setAppKey("7428763670991832613");
        GlobalConfig.getGlobalConfig("7428763670991832613").setAppSecret("cbc16f16-7018-4661-96c1-48d9d8faafb4");
        AccessToken accessToken = getAccessToken();
        SmsTemplateApplyRequest request = new SmsTemplateApplyRequest();
        request.setAppKey("7428763670991832613");
        SmsTemplateApplyParam param = request.getParam();
        param.setSmsAccount("804090b6");
        param.setTemplateType("CN_NTC");
        param.setTemplateName("购买课程商品后短信看课");
        param.setTemplateContent("您已购买${productName}，点击链接: ${learnUrl}，老师带你学习！");
        SmsTemplateApplyResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }


    public static void createSign() {
        GlobalConfig.getGlobalConfig("7428763670991832613").setAppKey("7428763670991832613");
        GlobalConfig.getGlobalConfig("7428763670991832613").setAppSecret("cbc16f16-7018-4661-96c1-48d9d8faafb4");
        AccessToken accessToken = getAccessToken();
        SmsSignApplyRequest request = new SmsSignApplyRequest();
        request.setAppKey("7428763670991832613");
        SmsSignApplyParam param = request.getParam();
        param.setSmsAccount("804090b6");
        param.setSign("杨工教育店铺");
        SmsSignApplyResponse response = request.execute(accessToken);
        System.out.println(JSON.toJSONString(response));
    }

    private static AccessToken getAccessToken () {
        return JSON.parseObject(access_token_str, AccessToken.class);
    }

    private static void getAccessTokenFromApi() {
        GlobalConfig.getGlobalConfig("7428763670991832613").setAppKey("7428763670991832613");
        GlobalConfig.getGlobalConfig("7428763670991832613").setAppSecret("cbc16f16-7018-4661-96c1-48d9d8faafb4");
        //获取accessToken
        TokenCreateRequest request = new TokenCreateRequest();
        request.setAppKey("7428763670991832613");
        TokenCreateParam param = request.getParam();
        param.setCode("");
        param.setGrantType("authorization_self");
//        param.setTestShop("1");
        param.setShopId("*********");
//        param.setAuthId("112334");
//        param.setAuthSubjectType("WuLiuShang");
        TokenCreateResponse response = request.execute(null);
        String jsonString = JSON.toJSONString(response);
        System.out.println(jsonString);
        /**
         * access_token的有效期为7天
         * {"code":"10000","data":
         * {"accessToken":"bb8f19a2-d5e7-4e4e-a0a3-7c0639030c6b","encryptOperator":"","expiresIn":604800,"operatorName":"","refreshToken":"fd41ac0b-03ed-49b7-a5f7-82ddd2b6616c","scope":"SCOPE","shopBizType":0,"shopId":184833512,"shopName":"问到课堂"},
         * "logId":"20240826165642D8A132E98C310204FD05","msg":"success","subCode":"","subMsg":"","success":true}
         */
    }
}
