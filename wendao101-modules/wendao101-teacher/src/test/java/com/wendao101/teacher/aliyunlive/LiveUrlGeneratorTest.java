package com.wendao101.teacher.aliyunlive;

import com.wendao101.common.core.aliyunlive.StreamUrls;

import java.util.UUID;

public class LiveUrlGeneratorTest {
    public static void main(String[] args) {
        LiveUrlGenerator generator = new LiveUrlGenerator();
        // 修改1：将uid替换为实际用户ID（假设userId是业务中的用户ID）
        String uid = "778899"; // 例如：String.valueOf(user.getUserId());

        // 修改2：生成随机数rand（使用UUID）
        String rand = UUID.randomUUID().toString().replace("-", ""); // 移除中划线
        StreamUrls urls = generator.generateUrls("wendaoLive", "myLive",uid,rand);

        // 推流地址
        System.out.println("推流地址:");
        System.out.println("推流地址 (RTMP): " + urls.getRtmpPushUrl());
        System.out.println("推流地址 (RTS): " + urls.getRtsPushUrl());
        System.out.println("推流地址 (SRT): " + urls.getSrtPushUrl());

        // 原画播放地址
        System.out.println("\n原画播放地址:");
        System.out.println("播放地址 (RTMP): " + urls.getRtmpPlayUrl());
        System.out.println("播放地址 (FLV): " + urls.getFlvPlayUrl());
        System.out.println("播放地址 (HLS): " + urls.getM3u8PlayUrl());
        System.out.println("播放地址 (RTS): " + urls.getRtsPlayUrl());

        // 延播播放地址
        System.out.println("\n延播播放地址:");
        System.out.println("RTMP: " + urls.getDelayRtmpPlayUrl());
        System.out.println("FLV: " + urls.getDelayFlvPlayUrl());
        System.out.println("HLS: " + urls.getDelayM3u8PlayUrl());
        System.out.println("RTS: " + urls.getDelayRtsPlayUrl());

    }
}
