package com.wendao101.teacher.aliyunlive;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.live.model.v20161101.DescribeLiveDomainConfigsRequest;
import com.aliyuncs.live.model.v20161101.DescribeLiveDomainConfigsResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.wendao101.common.core.aliyunlive.StreamAddressResult;
import org.apache.commons.codec.digest.DigestUtils;

import java.security.MessageDigest;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

public class AliyunLiveStreamGenerator {

    /**
     *
     * @param appId 应用id
     * @param appKey 应用秘钥
     * @param roomId 房间id
     * @param anchorUserId 主播id
     * @param audienceUserId 连麦用户id
     * @param playDomain 播放域名
     * @param rtcTimestamp 连麦地址过期时间
     * @param cdnTimestamp 播放地址过期时间
     * @param playUserId  普通观众用户id
     * @return
     */
//    public static StreamAddressResult generateStreamAddresses(
//            String appId,
//            String appKey,
//            String roomId,
//            String anchorUserId,
//            String audienceUserId,
//            String playDomain,
//            long rtcTimestamp,
//            long cdnTimestamp, String playUserId) {
//
//        StreamAddressResult result = new StreamAddressResult();
//
//        // 1. 生成主播端连麦地址
//        String anchorToken = getRtcAuth(appId, appKey, roomId, anchorUserId, rtcTimestamp);
//        result.setAnchorPushUrl(
//                String.format("artc://live.aliyun.com/push/%s?sdkAppId=%s&userId=%s&timestamp=%d&token=%s",
//                        roomId, appId, anchorUserId, rtcTimestamp, anchorToken));
//        result.setAnchorPullUrl(
//                String.format("artc://live.aliyun.com/play/%s?sdkAppId=%s&userId=%s&timestamp=%d&token=%s",
//                        roomId, appId, anchorUserId, rtcTimestamp, anchorToken));
//
//        // 2. 生成观众端连麦地址
//        String audienceToken = getRtcAuth(appId, appKey, roomId, audienceUserId, rtcTimestamp);
//        result.setAudiencePushUrl(
//                String.format("artc://live.aliyun.com/push/%s?sdkAppId=%s&userId=%s&timestamp=%d&token=%s",
//                        roomId, appId, audienceUserId, rtcTimestamp, audienceToken));
//        result.setAudiencePullUrl(
//                String.format("artc://live.aliyun.com/play/%s?sdkAppId=%s&userId=%s&timestamp=%d&token=%s",
//                        roomId, appId, audienceUserId, rtcTimestamp, audienceToken));
//
//        // 3. 生成CDN播放地址
//        String authKey = getAuthKeyForDomain(playDomain);
//        String basePath = "/live/" + appId + "_" + roomId + "_" + anchorUserId + "_camera";
//
//        // 修改1：将uid替换为实际用户ID（假设userId是业务中的用户ID）
//        //String uid = playUserId; // 例如：String.valueOf(user.getUserId());
//
//        // 修改2：生成随机数rand（使用UUID）
//        String rand = UUID.randomUUID().toString().replace("-", ""); // 移除中划线
//
//        result.setRtmpUrl(generateCdnUrl("rtmp", playDomain, basePath, authKey, cdnTimestamp, rand, playUserId));
//        result.setFlvUrl(generateCdnUrl("https", playDomain, basePath + ".flv", authKey, cdnTimestamp, rand, playUserId));
//        result.setHlsUrl(generateCdnUrl("https", playDomain, basePath + ".m3u8", authKey, cdnTimestamp, rand, playUserId));
//        result.setRtsUrl(generateCdnUrl("artc", playDomain, basePath, authKey, cdnTimestamp, rand, playUserId));
//
//        return result;
//    }

    private static String generateCdnUrl(String protocol, String domain, String path, String authKey, long timestamp, String rand, String uid) {
        String auth = generateAuthKey(path, authKey, timestamp, rand, uid);
        return String.format("%s://%s%s?auth_key=%s", protocol, domain, path, auth);
    }

    // 以下是原文件中的核心方法保持不变
    private static String generateAuthKey(String path, String secretKey, long exp, String rand, String uid) {
        String original = path + "-" + exp + "-" + rand + "-" + uid + "-" + secretKey;
        return exp + "-" + rand + "-" + uid + "-" + DigestUtils.md5Hex(original);
    }

    public static String getRtcAuth(String appId, String appKey, String channelId, String userId, long timestamp) {
        String rtcAuthStr = String.format("%s%s%s%s%d", appId, appKey, channelId, userId, timestamp);
        return getSHA256(rtcAuthStr);
    }

    private static String getSHA256(String str) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(str.getBytes("UTF-8"));
            return byte2Hex(md.digest());
        } catch (Exception e) {
            throw new RuntimeException("SHA-256 calculation error", e);
        }
    }

    private static String byte2Hex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) sb.append('0');
            sb.append(hex);
        }
        return sb.toString();
    }

    private static String getAuthKeyForDomain(String domain) {
        try {
            DefaultProfile profile = DefaultProfile.getProfile("cn-shanghai",
                    "LTAI5tRfCNfcpvFCRaM1ydiy",
                    "******************************");

            IAcsClient client = new DefaultAcsClient(profile);
            DescribeLiveDomainConfigsRequest request = new DescribeLiveDomainConfigsRequest();
            request.setDomainName(domain);
            request.setFunctionNames("aliauth");

            DescribeLiveDomainConfigsResponse response = client.getAcsResponse(request);

            for (DescribeLiveDomainConfigsResponse.DomainConfig.FunctionArg arg :
                    response.getDomainConfigs().get(0).getFunctionArgs()) {
                if ("auth_key1".equals(arg.getArgName())) {
                    return arg.getArgValue();
                }
            }
            return "";
        } catch (ClientException e) {
            throw new RuntimeException("Failed to get auth key for domain: " + domain, e);
        }
    }

//    public static void main(String[] args) {
//        // 配置参数（与原始文件相同）
//        String appId = "ce9599c5-a7c7-4b1f-b596-92368ea78a4f";
//        String appKey = "9aa010a36cbe0954035dad3583bd1ade";
//        String roomId = "568457";
//        String anchorUserId = "123";
//        String audienceUserId = "456";
//        String playDomain = "rts1-aplay.wendao101.com";
//
//
//        int validMinutes = 1440;  // 有效期1440分钟（24小时）
//
//        // 生成过期时间戳
//        // long exp = System.currentTimeMillis() / 1000 + TimeUnit.MINUTES.toSeconds(validMinutes);
//        long rtcTimestamp = System.currentTimeMillis() / 1000 + TimeUnit.MINUTES.toSeconds(validMinutes); // 连麦地址时间戳
//        long cdnTimestamp = System.currentTimeMillis() / 1000 + TimeUnit.MINUTES.toSeconds(validMinutes); // CDN地址时间戳
//
//        // 修改1：将uid替换为实际用户ID（假设userId是业务中的用户ID）
//        String uid = "778899"; // 例如：String.valueOf(user.getUserId());
//
//        // 生成地址
//        StreamAddressResult result = generateStreamAddresses(
//                appId, appKey, roomId, anchorUserId, audienceUserId,
//                playDomain, rtcTimestamp, cdnTimestamp, uid
//        );
//
//        // 打印结果（使用新的扁平化结构）
//        System.out.println("主播端地址:");
//        System.out.println("推流地址: " + result.getAnchorPushUrl());
//        System.out.println("拉流地址: " + result.getAnchorPullUrl());
//
//        System.out.println("\n观众端地址:");
//        System.out.println("推流地址: " + result.getAudiencePushUrl());
//        System.out.println("拉流地址: " + result.getAudiencePullUrl());
//
//        System.out.println("\nCDN播放地址:");
//        System.out.println("RTMP: " + result.getRtmpUrl());
//        System.out.println("FLV: " + result.getFlvUrl());
//        System.out.println("HLS: " + result.getHlsUrl());
//        System.out.println("RTS: " + result.getRtsUrl());
//    }
}
