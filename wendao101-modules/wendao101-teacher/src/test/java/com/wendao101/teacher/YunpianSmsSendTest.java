package com.wendao101.teacher;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.http.entity.StringEntity;

public class YunpianSmsSendTest {
    // 云片API密钥
    private static final String APIKEY = "102222afce402a62dd7b44276e736fff";
    // 云片短信发送接口地址
    private static final String API_URL = "https://sms.yunpian.com/v2/sms/single_send.json";
    
    public static void main(String[] args) {
        try {
            // 设置请求参数
            Map<String, String> params = new HashMap<>();
            params.put("apikey", APIKEY);
            params.put("mobile", "接收短信的手机号");
            params.put("text", "【您的签名】您的验证码是1234");
            
            // 发送短信
            String result = singleSend(params);
            System.out.println("发送结果：" + result);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 发送单条短信
     * @param params 请求参数
     * @return 响应结果
     */
    private static String singleSend(Map<String, String> params) throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(API_URL);
        
        // 设置请求头
        httpPost.setHeader("Accept", "application/json;charset=utf-8");
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        
        // 构建请求参数
        StringBuilder paramBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (paramBuilder.length() > 0) {
                paramBuilder.append("&");
            }
            paramBuilder.append(entry.getKey()).append("=").append(entry.getValue());
        }
        
        // 设置请求体
        StringEntity entity = new StringEntity(paramBuilder.toString(), "UTF-8");
        httpPost.setEntity(entity);
        
        // 发送请求并获取响应
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            return EntityUtils.toString(response.getEntity(), "UTF-8");
        } finally {
            httpClient.close();
        }
    }
}
