package com.wendao101.teacher;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.*;

import java.util.ArrayList;
import java.util.List;

public class Transcode1 {
    public static void main(String[] args) {
        List<String> list = new ArrayList<>();
        //list.add("5576678022388447797");
        list.add("5576678022389045341");
        list.add("5576678022388417166");
        list.add("5576678022387679162");
        list.add("5576678022389577633");
        list.add("5576678022389880081");
        list.add("5576678022389660931");
        list.add("5576678022388051057");
        list.add("5576678022388688918");
        list.add("5576678022388627674");
        list.add("5576678022388629248");
        list.add("5576678022389044698");
        list.add("5576678022388272616");
        list.add("5576678022389031165");
        list.add("5576678022388627687");
        list.add("5576678022386936398");
        list.add("5576678022388628954");
        list.add("5576678022388296454");
        list.add("5576678022389419481");
        list.add("5576678022388296486");
        list.add("5576678022388645991");
        list.add("5576678022387347208");
        list.add("5576678022389434908");
        list.add("5576678022387907069");
        list.add("5576678022388419784");
        list.add("5576678022389442673");
        list.add("5576678022389125606");
        list.add("5576678022389582204");
        list.add("5576678022389657695");
        list.add("5576678022388631738");
        list.add("5576678022390232687");
        list.add("5576678022390225666");
        list.add("5576678022389422361");
        list.add("5576678022387977655");
        list.add("5576678022389484008");
        list.add("5576678022389044696");
        list.add("5576678022389201888");
        list.add("5576678022390000367");
        list.add("5576678022389985409");
        list.add("5576678022390258497");
        list.add("5576678022390224636");
        list.add("5576678022388630742");
        list.add("5576678022389448117");
        list.add("5576678022388690674");
        list.add("5576678022389044760");
        list.add("5576678022389198487");
        list.add("5576678022389438845");
        list.add("5576678022388690722");
        list.add("5576678022389037296");
        list.add("5576678022389209802");
        list.add("5576678022388813701");
        list.add("5576678022388627877");
        list.add("5576678022388866149");
        list.add("5576678022389198519");
        list.add("5576678022389558527");
        list.add("5576678022389273513");
        list.add("5576678022390000447");
        list.add("5576678022387351368");
        list.add("5576678022388642919");
        list.add("5576678022387347250");
        list.add("5576678022388096627");
        list.add("5576678022389477620");
        list.add("5576678022390232265");
        list.add("5576678022389990878");
        list.add("5576678022390227708");
        list.add("5576678022388652185");
        list.add("5576678022390689583");
        list.add("5576678022389024971");
        list.add("5576678022389212730");
        list.add("5576678022389540437");
        list.add("5576678022390227046");
        list.add("5576678022388410240");
        list.add("5576678022388362769");
        list.add("5576678022389212746");
        list.add("5576678022389265499");
        list.add("5576678022389047832");
        list.add("5576678022389445648");
        list.add("5576678022389799631");
        list.add("5576678022389039434");
        list.add("5576678022389027159");
        list.add("5576678022390228566");
        list.add("5576678022388787509");
        list.add("5576678022389798596");
        list.add("5576678022390233577");
        list.add("5576678022390405154");
        list.add("5576678022389441226");
        list.add("5576678022389659111");
        list.add("5576678022390690639");
        list.add("5576678022389074077");
        list.add("5576678022390002020");
        list.add("5576678022389583668");
        list.add("5576678022388633752");
        list.add("5576678022389041040");
        list.add("5576678022389027191");
        list.add("5576678022388815014");
        list.add("5576678022388411156");
        list.add("5576678022388750880");
        list.add("5576678022388421440");
        list.add("5576678022388646726");
        list.add("5576678022390220275");
        list.add("5576678022389039498");
        list.add("5576678022388748412");
        list.add("5576678022389419665");
        list.add("5576678022389878024");
        list.add("5576678022389074157");
        list.add("5576678022389571226");
        list.add("5576678022388322231");
        list.add("5576678022389128218");
        list.add("5576678022389416042");
        list.add("5576678022390690361");
        list.add("5576678022388689321");
        list.add("5576678022389929909");
        list.add("5576678022390406657");
        list.add("5576678022390217265");
        list.add("5576678022390095767");
        list.add("5576678022390439860");
        list.add("5576678022389928015");
        list.add("5576678022388689337");
        list.add("5576678022388820621");
        list.add("5576678022390492120");
        list.add("5576678022390687518");
        list.add("5576678022389040312");
        list.add("5576678022390218389");
        list.add("5576678022390690930");
        list.add("5576678022390217153");
        list.add("5576678022389417404");
        list.add("5576678022390207081");
        list.add("5576678022388356189");
        list.add("5576678022389445944");
        list.add("5576678022389875652");
        list.add("5576678022389476470");
        list.add("5576678022389134014");
        list.add("5576678022389069339");
        list.add("5576678022389583785");
        list.add("5576678022388322135");
        list.add("5576678022389033355");
        list.add("5576678022390412690");
        list.add("5576678022389445960");
        list.add("5576678022390898442");
        list.add("5576678022388818000");
        list.add("5576678022389988125");
        list.add("5576678022390217137");
        list.add("5576678022390413898");
        list.add("5576678022389038293");
        for(String url : list){
            main1(url);
        }
    }

    public static void main1(String fileId) {
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential("AKIDeFj6Vny8HLXf7cBjHmXEvgLtKcmsqdps", "73pQs38B8xiLrbK83Sr9KKJAqW5ylmp9");
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("vod.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VodClient client = new VodClient(cred, "", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            ProcessMediaRequest req = new ProcessMediaRequest();
            req.setFileId(fileId);
            req.setSubAppId(1500025759L);
            MediaProcessTaskInput mediaProcessTaskInput1 = new MediaProcessTaskInput();

            TranscodeTaskInput[] transcodeTaskInputs1 = new TranscodeTaskInput[1];
            TranscodeTaskInput transcodeTaskInput1 = new TranscodeTaskInput();
            transcodeTaskInput1.setDefinition(100030L);
            transcodeTaskInputs1[0] = transcodeTaskInput1;

            mediaProcessTaskInput1.setTranscodeTaskSet(transcodeTaskInputs1);

            req.setMediaProcessTask(mediaProcessTaskInput1);

            // 返回的resp是一个ProcessMediaResponse的实例，与请求对象对应
            ProcessMediaResponse resp = client.ProcessMedia(req);
            // 输出json格式的字符串回包
            System.out.println(resp);
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
    }
}
