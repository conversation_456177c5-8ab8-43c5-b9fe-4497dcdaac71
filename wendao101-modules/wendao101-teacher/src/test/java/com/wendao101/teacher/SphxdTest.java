package com.wendao101.teacher;

import com.alibaba.fastjson.JSON;
import com.wendao101.common.redis.service.RedisService;
import me.chanjar.weixin.channel.api.WxChannelService;
import me.chanjar.weixin.channel.bean.category.CategoryDetailResult;
import me.chanjar.weixin.common.error.WxErrorException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Set;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SphxdTest {
    @Autowired
    private WxChannelService channelService;
    @Test
    public void testChannelService() throws WxErrorException {
        CategoryDetailResult categoryDetail = channelService.getCategoryService().getCategoryDetail("529048");
        System.out.println(JSON.toJSONString(categoryDetail));
    }
}
