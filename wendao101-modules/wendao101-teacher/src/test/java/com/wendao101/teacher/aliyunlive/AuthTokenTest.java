package com.wendao101.teacher.aliyunlive;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.live.model.v20161101.DescribeLiveDomainConfigsRequest;
import com.aliyuncs.live.model.v20161101.DescribeLiveDomainConfigsResponse;
import com.aliyuncs.profile.DefaultProfile;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.TimeUnit;

public class AuthTokenTest {
    public static void main(String[] args) {
        String uri = "/push/1234";
        long exp = 1749278979;

        // 获取鉴权密钥
        String pushKey = getAuthKeyForDomain("live.aliyun.com");

        String result = generateAuthKey(uri, pushKey, exp, "123456", "123456");
    }

    private static String generateAuthKey(String path, String secretKey, long exp, String rand, String uid) {
        String original = path + "-" + exp + "-" + rand + "-" + uid + "-" + secretKey;
        return exp + "-" + rand + "-" + uid + "-" + DigestUtils.md5Hex(original);
    }

    private static String getAuthKeyForDomain(String domain) {
        if (StringUtils.isBlank(domain)) {
            domain = "aplay.wendao101.com";
        }
        /**
         * 需要将<>内容替换成实际使用的值,获取到鉴权Key后就可以对URL进行加密。
         * 生成推流地址时，要使用推流域名的鉴权Key。
         *
         * 生成播放地址时，要使用播流域名的鉴权Key。
         */
        DefaultProfile profile = DefaultProfile.getProfile("cn-shanghai", "LTAI5tRfCNfcpvFCRaM1ydiy", "******************************");
        IAcsClient client = new DefaultAcsClient(profile);
        DescribeLiveDomainConfigsRequest describeLiveDomainConfigsRequest = new DescribeLiveDomainConfigsRequest();
        describeLiveDomainConfigsRequest.setDomainName(domain);
        describeLiveDomainConfigsRequest.setFunctionNames("aliauth");

        DescribeLiveDomainConfigsResponse describeLiveStreamSnapshotInfoResponse = null;
        try {
            describeLiveStreamSnapshotInfoResponse = client.getAcsResponse(describeLiveDomainConfigsRequest);
        } catch (ClientException e) {
            e.printStackTrace();
        }
        //鉴权key
        String key = "";
        //String key2 = "";
        for (DescribeLiveDomainConfigsResponse.DomainConfig.FunctionArg f : describeLiveStreamSnapshotInfoResponse.getDomainConfigs().get(0).getFunctionArgs()) {
            //主KEY
            if ("auth_key1".equals(f.getArgName())) {
                key = f.getArgValue();
            }
            //副KEY
//            if ("auth_key2".equals(f.getArgName())) {
//                key2 = f.getArgValue();
//            }
            //System.out.println(JSON.toJSONString(f));
        }
        //System.out.println(key);
        //System.out.println(key2);
        return key;
    }
}
