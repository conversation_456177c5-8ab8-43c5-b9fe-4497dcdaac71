package com.wendao101.teacher;

import com.alibaba.fastjson.JSON;
import com.doudian.open.api.product_addV2.param.*;
import com.wendao101.teacher.dto.ProductAddV2ParamDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class TestSpec1 {
    public static void main(String[] args) {
        ProductAddV2ParamDTO param1 = new ProductAddV2ParamDTO();
        SpecInfo specInfo1 = new SpecInfo();

        List<SpecValuesItem> specValues1 = new ArrayList<>();
        {
            SpecValuesItem specValuesItem1 = new SpecValuesItem();
            specValuesItem1.setPropertyName("颜色");
            List<ValuesItem_4> values11 = new ArrayList<>();
            ValuesItem_4 valuesItem1 = new ValuesItem_4();
            valuesItem1.setValueName("白");
            values11.add(valuesItem1);
            ValuesItem_4 valuesItem2 = new ValuesItem_4();
            valuesItem2.setValueName("红");
            values11.add(valuesItem2);
            ValuesItem_4 valuesItem3 = new ValuesItem_4();
            valuesItem3.setValueName("绿");
            values11.add(valuesItem3);
            specValuesItem1.setValues(values11);
            specValues1.add(specValuesItem1);
        }
        // 移除重复的颜色规格块，避免重复添加
        {
            SpecValuesItem specValuesItem1 = new SpecValuesItem();
            specValuesItem1.setPropertyName("尺码");
            List<ValuesItem_4> values11 = new ArrayList<>();
            ValuesItem_4 valuesItem1 = new ValuesItem_4();
            valuesItem1.setValueName("大");
            values11.add(valuesItem1);
            ValuesItem_4 valuesItem2 = new ValuesItem_4();
            valuesItem2.setValueName("中");
            values11.add(valuesItem2);
            ValuesItem_4 valuesItem3 = new ValuesItem_4();
            valuesItem3.setValueName("小");
            values11.add(valuesItem3);
            specValuesItem1.setValues(values11);
            specValues1.add(specValuesItem1);
        }
        {
            SpecValuesItem specValuesItem1 = new SpecValuesItem();
            specValuesItem1.setPropertyName("布料");
            List<ValuesItem_4> values11 = new ArrayList<>();
            ValuesItem_4 valuesItem1 = new ValuesItem_4();
            valuesItem1.setValueName("棉");
            values11.add(valuesItem1);
            ValuesItem_4 valuesItem2 = new ValuesItem_4();
            valuesItem2.setValueName("麻");
            values11.add(valuesItem2);
            specValuesItem1.setValues(values11);
            specValues1.add(specValuesItem1);
        }
        specInfo1.setSpecValues(specValues1);
        param1.setSpecInfo(specInfo1);
        SpecInfo specInfo = param1.getSpecInfo();
        //获取真正的规格属性
        List<SpecValuesItem> specValues = specInfo.getSpecValues();
        if (CollectionUtils.isEmpty(specValues)) {
            //return error("没有提交规格");
        }
        if (specValues.size() == 1 && CollectionUtils.isNotEmpty(specValues.get(0).getValues()) && specValues.get(0).getValues().size() == 1) {
            System.out.println("单规格且规格值也是一个的商品!");
        }
        //Map<Integer, Integer> linkedHashMap = new LinkedHashMap<>();
        int specProductNum = 1;
        //这里单规格和多规格,单规格值也有可能有多个值
        for (SpecValuesItem specValue : specValues) {
            //System.out.println(specValues.get(k).getPropertyName());
            //System.out.println(specValues.get(k).getValues());
            if (CollectionUtils.isEmpty(specValue.getValues())) {
                //error("规格:[" + specValues.get(k).getPropertyName() + "]的规格值为空!");
            }
//            for (ValuesItem_4 vItem : specValues.get(k).getValues()) {
//                //打印规格值
//                System.out.println(vItem.getValueName());
//            }
            //计算规格商品数量,如果第一个规格值为2个,第二个规格值有3个,则为1*2*3=6个
            specProductNum = specProductNum * specValue.getValues().size();
            //Integer level = k+1;
            //第一层计为0层
            //linkedHashMap.put(k, specValues.get(k).getValues().size());
        }
        // 移除重复的颜色规格（在示例中有两个相同的颜色规格）
        Map<String, SpecValuesItem> propertyMap = new LinkedHashMap<>();
        for (SpecValuesItem item : specValues) {
            propertyMap.put(item.getPropertyName(), item);
        }
        List<SpecValuesItem> uniqueSpecValues = new ArrayList<>(propertyMap.values());

        // 重新计算规格商品数量
        specProductNum = 1;
        for (SpecValuesItem item : uniqueSpecValues) {
            specProductNum *= item.getValues().size();
        }
        System.out.println("总规格数量: " + specProductNum);

        // 构建spec_prices_v2
        List<SpecPricesV2Item> totalSpecPricesItem = new ArrayList<>(specProductNum);

        // 生成所有规格组合
        generateSpecCombinations(totalSpecPricesItem, uniqueSpecValues, new ArrayList<>(), 0);

        // 打印生成的规格组合
        for (SpecPricesV2Item item : totalSpecPricesItem) {
            //这里面有引用,要换成json再转回来
            String sellPropertiesJson = JSON.toJSONString(item.getSellProperties());
            List<SellPropertiesItem> sellItemList = JSON.parseArray(sellPropertiesJson, SellPropertiesItem.class);
            item.setSellProperties(sellItemList);
        }

        System.out.println(JSON.toJSONString(totalSpecPricesItem));
    }
    
    /**
     * 递归生成所有规格组合
     * @param result 结果列表
     * @param specValues 规格值列表
     * @param currentCombination 当前组合
     * @param currentIndex 当前处理的规格索引
     */
    private static void generateSpecCombinations(List<SpecPricesV2Item> result, List<SpecValuesItem> specValues, 
                                               List<SellPropertiesItem> currentCombination, int currentIndex) {
        // 如果已经处理完所有规格，添加当前组合到结果中
        if (currentIndex >= specValues.size()) {
            SpecPricesV2Item item = new SpecPricesV2Item();
            // 创建新的列表避免引用问题
            List<SellPropertiesItem> properties = new ArrayList<>(currentCombination);
            item.setSellProperties(properties);
            result.add(item);
            return;
        }
        
        // 获取当前规格
        SpecValuesItem currentSpec = specValues.get(currentIndex);
        List<ValuesItem_4> values = currentSpec.getValues();
        
        // 遍历当前规格的所有值
        for (ValuesItem_4 value : values) {
            // 创建新的规格属性
            SellPropertiesItem property = new SellPropertiesItem();
            property.setPropertyName(currentSpec.getPropertyName());
            property.setValueName(value.getValueName());
            
            // 添加到当前组合
            currentCombination.add(property);
            
            // 递归处理下一个规格
            generateSpecCombinations(result, specValues, currentCombination, currentIndex + 1);
            
            // 回溯，移除当前添加的属性
            currentCombination.remove(currentCombination.size() - 1);
        }
    }
}
