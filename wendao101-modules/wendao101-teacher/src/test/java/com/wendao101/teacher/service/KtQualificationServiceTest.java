package com.wendao101.teacher.service;

import com.alibaba.fastjson.JSON;
import com.wendao101.common.core.ktdto.Qualifications;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.douyin.api.feign.KtQualificationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class KtQualificationServiceTest {
    @Autowired
    private KtQualificationService ktQualificationService;

    @Test
    public void testAdd(){
        Qualifications qualifications = new Qualifications();
        qualifications.setEmpid(9999999);
        qualifications.setZzid("123");
        qualifications.setObjectid("123");
        AjaxResult result = ktQualificationService.add(qualifications);

        System.out.println(JSON.toJSONString(result));
    }

}
