package com.wendao101.teacher.aliyunlive;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.live.model.v20161101.DescribeLiveDomainConfigsRequest;
import com.aliyuncs.live.model.v20161101.DescribeLiveDomainConfigsResponse;
import com.aliyuncs.profile.DefaultProfile;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class LianMaiTest {
//    public static void main(String[] args) {
//        //播放域名
//        String cdnPlayDomain = "rts1-aplay.wendao101.com";
//
//        String defaultLiveMicAppId = "ce9599c5-a7c7-4b1f-b596-92368ea78a4f";
//        String defaultLiveMicAppKey = "9aa010a36cbe0954035dad3583bd1ade";
//        String roomId = "568457";//channelId
//        String anchorUserId = "123";
//        long timestamp = 1749279720;
//        //主播端:
//        String rtcAuth = getRtcAuth(defaultLiveMicAppId, defaultLiveMicAppKey, roomId, anchorUserId, timestamp);
//        //System.out.println(rtcAuth);
//        //主播端地址:
//        /**
//         * 主播端
//         * 连麦推流地址
//         * artc://live.aliyun.com/push/568457?sdkAppId=ce9599c5-a7c7-4b1f-b596-92368ea78a4f&userId=123&timestamp=1749279720&token=fd762e1c28320f943dcbe6897b2a8fc21be660a758f7b19cc95625f5afb24ede
//         * 连麦拉流地址
//         * artc://live.aliyun.com/play/568457?sdkAppId=ce9599c5-a7c7-4b1f-b596-92368ea78a4f&userId=123&timestamp=1749279720&token=fd762e1c28320f943dcbe6897b2a8fc21be660a758f7b19cc95625f5afb24ede
//         */
//        String anchorPushUrl = String.format("artc://live.aliyun.com/push/%s?sdkAppId=%s&userId=%s&timestamp=%d&token=%s",
//                roomId, defaultLiveMicAppId, anchorUserId, timestamp, rtcAuth);
//        String anchorPullUrl= String.format("artc://live.aliyun.com/play/%s?sdkAppId=%s&userId=%s&timestamp=%d&token=%s",
//                roomId, defaultLiveMicAppId, anchorUserId, timestamp, rtcAuth);
//        System.out.println("主播端:");
//        System.out.println("连麦推流地址:");
//        System.out.println(anchorPushUrl);
//        System.out.println("连麦拉流地址:");
//        System.out.println(anchorPullUrl);
//
//
//        //观众端:
//        String audienceUserId = "456";
//        String rtcAuthAudience = getRtcAuth(defaultLiveMicAppId, defaultLiveMicAppKey, roomId, audienceUserId, timestamp);
//        //System.out.println(rtcAuthAudience);
//
//        //观众端地址:
//        /**
//         * 观众端
//         * 连麦推流地址
//         * artc://live.aliyun.com/push/568457?sdkAppId=ce9599c5-a7c7-4b1f-b596-92368ea78a4f&userId=456&timestamp=1749279720&token=ca24421c78ee6445f1a97d7a671aca9add495fa8cc4e929ec505d566f59f6465
//         * 连麦拉流地址
//         * artc://live.aliyun.com/play/568457?sdkAppId=ce9599c5-a7c7-4b1f-b596-92368ea78a4f&userId=456&timestamp=1749279720&token=ca24421c78ee6445f1a97d7a671aca9add495fa8cc4e929ec505d566f59f6465
//         */
//        String audienceLiveMicPushUrl = String.format("artc://live.aliyun.com/push/%s?sdkAppId=%s&userId=%s&timestamp=%d&token=%s",
//                roomId, defaultLiveMicAppId, audienceUserId, timestamp, rtcAuthAudience);
//        String audienceLiveMicPullUrl = String.format("artc://live.aliyun.com/play/%s?sdkAppId=%s&userId=%s&timestamp=%d&token=%s",
//                roomId, defaultLiveMicAppId, audienceUserId, timestamp, rtcAuthAudience);
//
//        System.out.println("观众端:");
//        System.out.println("连麦推流地址:");
//        System.out.println(audienceLiveMicPushUrl);
//        System.out.println("连麦拉流地址:");
//        System.out.println(audienceLiveMicPullUrl);
//
//
//        timestamp = 1749193320;
//        //cdn播放地址:
//        String authKeyForDomain = getAuthKeyForDomain(cdnPlayDomain);
//        //RTMP 格式
//        StreamUrls streamUrls = new StreamUrls();
//        ///live/ce9599c5-a7c7-4b1f-b596-92368ea78a4f_568457_123_camera
//        String basePath = "/live/" + defaultLiveMicAppId + "_" + roomId + "_" + anchorUserId + "_camera";
//        setPlayUrls(streamUrls,cdnPlayDomain, basePath, authKeyForDomain, timestamp, "0", "0");
//
//        // 原画播放地址
//        System.out.println("\nCDN播流地址:");
//        System.out.println("播放地址 (RTMP): " + streamUrls.getRtmpPlayUrl());
//        System.out.println("播放地址 (FLV): " + streamUrls.getFlvPlayUrl());
//        System.out.println("播放地址 (HLS): " + streamUrls.getM3u8PlayUrl());
//        System.out.println("播放地址 (RTS): " + streamUrls.getRtsPlayUrl());
//    }

//    private static void setPlayUrls(StreamUrls urls, String domain, String basePath,
//                             String key, long exp, String rand, String uid) {
//        // 生成播放地址鉴权参数
//        String authKeyNormal = generateAuthKey(basePath, key, exp, rand, uid);
//        String authKeyFlv = generateAuthKey(basePath + ".flv", key, exp, rand, uid);
//        String authKeyM3u8 = generateAuthKey(basePath + ".m3u8", key, exp, rand, uid);
//
////        if (isDelay) {
////            // 设置延播播放地址
////            urls.setDelayRtmpPlayUrl("rtmp://" + domain + basePath + "?auth_key=" + authKeyNormal);
////            urls.setDelayFlvPlayUrl("https://" + domain + basePath + ".flv?auth_key=" + authKeyFlv);
////            urls.setDelayM3u8PlayUrl("https://" + domain + basePath + ".m3u8?auth_key=" + authKeyM3u8);
////            urls.setDelayRtsPlayUrl("artc://" + domain + basePath + "?auth_key=" + authKeyNormal);
////        } else {
//            // 设置原画播放地址
//            urls.setRtmpPlayUrl("rtmp://" + domain + basePath + "?auth_key=" + authKeyNormal);
//            urls.setFlvPlayUrl("https://" + domain + basePath + ".flv?auth_key=" + authKeyFlv);
//            urls.setM3u8PlayUrl("https://" + domain + basePath + ".m3u8?auth_key=" + authKeyM3u8);
//            urls.setRtsPlayUrl("artc://" + domain + basePath + "?auth_key=" + authKeyNormal);
//        //}
//    }

    private static String generateAuthKey(String path, String secretKey, long exp, String rand, String uid) {
        String original = path + "-" + exp + "-" + rand + "-" + uid + "-" + secretKey;
        return exp + "-" + rand + "-" + uid + "-" + DigestUtils.md5Hex(original);
    }

    public static String getRtcAuth(String liveMicAppId, String liveMicAppKey,String channelId, String userId, long timestamp) {

        String rtcAuthStr = String.format("%s%s%s%s%d", liveMicAppId, liveMicAppKey, channelId, userId, timestamp);
        String rtcAuth = getSHA256(rtcAuthStr);
        //log.info("getRtcAuth. rtcAuthStr:{}, rtcAuth:{}", rtcAuthStr, rtcAuth);
        return rtcAuth;
    }

    private static String getSHA256(String str) {
        MessageDigest messageDigest;
        String encodestr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes("UTF-8"));
            encodestr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return encodestr;
    }

    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        String temp = null;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    private static String getAuthKeyForDomain(String domain) {
        if (StringUtils.isBlank(domain)) {
            domain = "aplay.wendao101.com";
        }
        /**
         * 需要将<>内容替换成实际使用的值,获取到鉴权Key后就可以对URL进行加密。
         * 生成推流地址时，要使用推流域名的鉴权Key。
         *
         * 生成播放地址时，要使用播流域名的鉴权Key。
         */
        DefaultProfile profile = DefaultProfile.getProfile("cn-shanghai", "LTAI5tRfCNfcpvFCRaM1ydiy", "******************************");
        IAcsClient client = new DefaultAcsClient(profile);
        DescribeLiveDomainConfigsRequest describeLiveDomainConfigsRequest = new DescribeLiveDomainConfigsRequest();
        describeLiveDomainConfigsRequest.setDomainName(domain);
        describeLiveDomainConfigsRequest.setFunctionNames("aliauth");

        DescribeLiveDomainConfigsResponse describeLiveStreamSnapshotInfoResponse = null;
        try {
            describeLiveStreamSnapshotInfoResponse = client.getAcsResponse(describeLiveDomainConfigsRequest);
        } catch (ClientException e) {
            e.printStackTrace();
        }
        //鉴权key
        String key = "";
        //String key2 = "";
        for (DescribeLiveDomainConfigsResponse.DomainConfig.FunctionArg f : describeLiveStreamSnapshotInfoResponse.getDomainConfigs().get(0).getFunctionArgs()) {
            //主KEY
            if ("auth_key1".equals(f.getArgName())) {
                key = f.getArgValue();
            }
            //副KEY
//            if ("auth_key2".equals(f.getArgName())) {
//                key2 = f.getArgValue();
//            }
            //System.out.println(JSON.toJSONString(f));
        }
        //System.out.println(key);
        //System.out.println(key2);
        return key;
    }
}
