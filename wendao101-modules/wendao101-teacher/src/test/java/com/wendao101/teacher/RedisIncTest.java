package com.wendao101.teacher;

import com.wendao101.common.redis.service.RedisService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class RedisIncTest {
    @Autowired
    RedisService redisService;
    @Test
    public void testInc() {
        //创建10个线程每个线程循环100次
//        for (int i = 0; i < 10; i++) {
//            new Thread(() -> {
//                // 循环100次
//                for (int j = 0; j < 100; j++) {
//                    long incr = redisService.incr("20231122", 12346789L);
//                    System.out.println(incr);
//                }
//            }).start();
//        }
        // 循环100次
        for (int i = 0; i < 100; i++) {
            long incr = redisService.incr("20231122", 17834566L);
            System.out.println(incr);
        }
    }
}
