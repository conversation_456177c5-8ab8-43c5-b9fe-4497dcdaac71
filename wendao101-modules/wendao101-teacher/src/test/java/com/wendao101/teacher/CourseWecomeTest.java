package com.wendao101.teacher;

import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.teacher.domain.Course;
import com.wendao101.teacher.service.ICourseService;
import com.wendao101.teacher.vo.AddWecomeOrTel;
import org.apache.commons.lang3.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class CourseWecomeTest {
    @Autowired
    private ICourseService courseService;
    @org.junit.Test
    public void test(){
        AddWecomeOrTel addWecomeOrTel = new AddWecomeOrTel();
        addWecomeOrTel.setTeacherId(3L);
        addWecomeOrTel.setCourseId(174L);


        addWecomeOrTel.setIncreaseTeacherWecom(1);
        addWecomeOrTel.setTeacherWecomQrcodeUrl("http://xxxx.xxx.com/url.html");
        addWecomeOrTel.setIncreaseTeacherWxphone(1);
        addWecomeOrTel.setTeacherWxphoneContent(null);
        Course course = courseService.selectCourseByTeacherIdAndId(addWecomeOrTel.getCourseId(), addWecomeOrTel.getTeacherId());
        if(course==null){
            System.out.println("课程不存在");
        }
        //删除逻辑
        //如果addWecomeOrTel中的teacherWecomQrcodeUrl为空则设置increaseTeacherWecom为0
        if(StringUtils.isBlank(addWecomeOrTel.getTeacherWecomQrcodeUrl())){
            addWecomeOrTel.setIncreaseTeacherWecom(0);
            addWecomeOrTel.setTeacherWecomQrcodeUrl(null);
        }else{
            addWecomeOrTel.setIncreaseTeacherWecom(1);
        }
        //如果addWecomeOrTel中的teacherWxphoneContent为空则设置increaseTeacherWxphone为0
        if(StringUtils.isBlank(addWecomeOrTel.getTeacherWxphoneContent())){
            addWecomeOrTel.setIncreaseTeacherWxphone(0);
            addWecomeOrTel.setTeacherWxphoneContent(null);
        }else{
            addWecomeOrTel.setIncreaseTeacherWxphone(1);
        }
        course.setIncreaseTeacherWecom(addWecomeOrTel.getIncreaseTeacherWecom());
        course.setIncreaseTeacherWxphone(addWecomeOrTel.getIncreaseTeacherWxphone());
        course.setTeacherWecomQrcodeUrl(addWecomeOrTel.getTeacherWecomQrcodeUrl());
        course.setTeacherWxphoneContent(addWecomeOrTel.getTeacherWxphoneContent());
        courseService.updateCourseSetWecomQrcodeUrlNullWxphoneContentNull(course);
    }
}
