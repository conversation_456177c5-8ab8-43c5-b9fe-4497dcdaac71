package com.wendao101.teacher;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
public class JavaTest {
    public static void main(String[] args) {
        String originalJson = "{\"cats\": [{\"id\": \"381003\"}, {\"id\": \"378005\"}, {\"id\": \"530058\"}], \"skus\": [{\"attrs\": [{\"key\": \"适用人群\", \"value\": \"全龄段\"}, {\"key\": \"发货形式\", \"value\": \"自动发货\"}, {\"key\": \"学习平台\", \"value\": \"小程序/公众号\"}, {\"key\": \"开课形式\", \"value\": \"随到随学\"}, {\"key\": \"课程有效期\", \"value\": \"永久有效\"}, {\"key\": \"总课时\", \"value\": \"100\"}, {\"key\": \"学习资料\", \"value\": \"电子资料\"}, {\"key\": \"主要教学形式\", \"value\": \"线上视频课\"}], \"stockNum\": 999, \"salePrice\": 59900, \"skuDeliverInfo\": {\"stockType\": 0}}], \"attrs\": [{\"key\": \"适用人群\", \"value\": \"全龄段\"}, {\"key\": \"发货形式\", \"value\": \"自动发货\"}, {\"key\": \"学习平台\", \"value\": \"小程序/公众号\"}, {\"key\": \"开课形式\", \"value\": \"随到随学\"}, {\"key\": \"课程有效期\", \"value\": \"永久有效\"}, {\"key\": \"总课时\", \"value\": \"100\"}, {\"key\": \"学习资料\", \"value\": \"电子资料\"}, {\"key\": \"主要教学形式\", \"value\": \"线上视频课\"}], \"title\": \"古筝基本功（初中级）精讲课｜央音师资体系\", \"brandId\": \"2100000000\", \"listing\": 1, \"descInfo\": {\"desc\": \"面向人群：\\n古筝考级2级以上学员/艺考生/教师提升人群\\n\\n课程详情：初级1至27，28至60中级，61至72演奏秘籍，73至89全曲讲解，90至100先导课\", \"imgs\": [\"https://mmecimage.cn/p/wx72e0b2963317efe0/HJMy76ZvZ7-LmW9n-zGoUm8ZOMdt6335WIuButQVpTaA\", \"https://mmecimage.cn/p/wx72e0b2963317efe0/HK2bpn3xIvobc2EgwF9zY9R9RkwwpNuFFZ_n2k9HZ4Km\", \"https://mmecimage.cn/p/wx72e0b2963317efe0/HGdGVbSg_6L4XK1A-z3i6-jFBnxfImwI-_g2QXHqWU4e\", \"https://mmecimage.cn/p/wx72e0b2963317efe0/HDNuIy7h2bkOw7KM9AC-8TQw374sAEX0giS9ElQssPre\", \"https://mmecimage.cn/p/wx72e0b2963317efe0/HFqiFao5vqdmjgRT3zIPCJRRJ7GJWMv8Yx7NegXmosyZ\"]}, \"headImgs\": [\"https://mmecimage.cn/p/wx72e0b2963317efe0/HC0j5yt878lC4GBV_TOrNQTSGv3WVXEL4kT6v6ndVRUV\", \"https://mmecimage.cn/p/wx72e0b2963317efe0/HNQwwFQX9b4pKoxibp-amPrBS1-FLBRF7S7UHrc0W2Ja\", \"https://mmecimage.cn/p/wx72e0b2963317efe0/HOHBhzTn5C4L46ITkbpp5_gIBotWBUWAUmaJ1KcGV74a\"], \"limitInfo\": {\"periodType\": 0}, \"extraService\": {\"sevenDayReturn\": 0, \"freightInsurance\": 1}, \"outProductId\": \"wx72e0b2963317efe0_32070\", \"deliverMethod\": 1}";

        // 1. 解析JSON字符串为JSONObject
        JSONObject jsonObject = JSON.parseObject(originalJson);

        // 2. 获取skus数组
        JSONArray skus = jsonObject.getJSONArray("skus");

        // 3. 遍历skus数组，修改salePrice
        if (skus != null && !skus.isEmpty()) {
            for (int i = 0; i < skus.size(); i++) {
                JSONObject sku = skus.getJSONObject(i);
                if (sku.containsKey("salePrice")) {
                    // 修改salePrice值为800
                    sku.put("salePrice", 800);
                    System.out.println("已修改第 " + (i + 1) + " 个sku的salePrice为800");
                }
            }
        }

        // 4. 将修改后的JSONObject转换回字符串
        String modifiedJson = jsonObject.toJSONString();

        // 5. 打印结果
        System.out.println("修改后的JSON字符串:");
        System.out.println(modifiedJson);
    }
}
