package com.wendao101.teacher;

import com.wendao101.common.core.utils.StringUtils;

public class TestSplit {
    public static void main(String[] args) {
        //必须指定直播流id
        String pushUrl = "webrtc://livepush.wendao101.com/live/1jdolgimu?txSecret=3dc719d96458fa899b630dfe7ab2849c&txTime=66C59D6D";
        //String pushUrl = wendaoLiveWap.getPushUrl();
        if(StringUtils.isNotBlank(pushUrl)&&pushUrl.contains("/live/")&&pushUrl.contains("?")){
            String[] split = pushUrl.split("/live/");
            String[] split1 = split[1].split("\\?");
            String streamId = split1[0];
            if(StringUtils.isNotBlank(streamId)){
                System.out.println("streamId:"+streamId);
                //liveRoomInfoDTO.setStreamId(streamId);
            }
        }
    }
}
