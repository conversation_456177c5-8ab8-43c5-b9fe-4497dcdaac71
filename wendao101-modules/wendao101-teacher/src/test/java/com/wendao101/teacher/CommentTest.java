package com.wendao101.teacher;

import com.alibaba.fastjson.JSON;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import com.wendao101.teacher.domain.Comment;
import com.wendao101.teacher.service.CommentService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.regex.Pattern;

@RunWith(SpringRunner.class)
@SpringBootTest
public class CommentTest {

    @Autowired
    private CommentService commentService;

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 插入文档
     */
    @Test
    public void testInsertDoc() {
        Comment comment = new Comment();
        comment.setCommentContent("我是评论的内容");
        comment.setCommentUser(123456);
        comment.setCommentBlog("1301794986045005824 ");
        comment.setCommentGood(0);
        comment.setCreatedTime("2020-09-21 15:30:30");
        mongoTemplate.insert(comment);
    }

    /**
     * 查询文档 查看所有文档
     */
    @Test
    public void testQueryDoc() {

        List<Comment> comments = mongoTemplate.findAll(Comment.class);
        for (Comment comment : comments) {
            System.out.println(comment);
        }

    }

    /**
     * 查询文档 查看所有文档
     */
    @Test
    public void testQueryDoc2() {
        List<Comment> comments = commentService.findAll();
        for (Comment comment : comments) {
            System.out.println(comment);
        }
    }

    @Test
    public void testQueryById() {
        Comment comment = mongoTemplate.findById("64cca8bce306d77d7035e582", Comment.class);
        System.out.println(comment);
    }

    @Test
    public void testInsertDoc22() {

        Comment comment = new Comment();
        comment.setCommentContent("我是评论的内容2");
        comment.setCommentUser(222222);
        comment.setCommentBlog("1301794986045005825 ");
        comment.setCommentGood(1);
        comment.setCreatedTime("2020-09-21 15:30:31");

        mongoTemplate.insert(comment);

    }

    @Test
    public void testInsertDoc3() {

        Comment comment = new Comment();
        comment.setCommentContent("我是评论的内容3");
        comment.setCommentUser(333333);
        comment.setCommentBlog("1301794986045005826 ");
        comment.setCommentGood(2);
        comment.setCreatedTime("2020-09-21 15:30:32");

        mongoTemplate.insert(comment);

    }

    /**
     * 查找指定记录
     */
    @Test
    public void testQueryDoc4() {

        Query query = new Query(Criteria.where("commentGood").is(2));
        List<Comment> comments = mongoTemplate.find(query, Comment.class);
        for (Comment comment : comments) {
            System.out.println(comment);
        }
    }

    /**
     * and查询
     */
    @Test
    public void testQueryDoc5() {

        Query query = new Query(Criteria
                .where("commentGood").is(2)
                .and("commentUser").is(333333));
        List<Comment> comments = mongoTemplate.find(query, Comment.class);
        for (Comment comment : comments) {
            System.out.println(comment);
        }
    }

    /**
     * or查询
     */
    @Test
    public void testQueryDoc6() {
        Criteria criteria = new Criteria();

        Query query = new Query(criteria.orOperator(
                Criteria.where("commentGood").is(2),
                Criteria.where("commentUser").is(222222)));
        List<Comment> comments = mongoTemplate.find(query, Comment.class);
        for (Comment comment : comments) {
            System.out.println(comment);
        }
    }

    /**
     * 条件查询
     */
    @Test
    public void testQueryDoc7() {
        Query query = new Query(Criteria.where("commentGood").gt(1));
        List<Comment> comments = mongoTemplate.find(query, Comment.class);
        for (Comment comment : comments) {
            System.out.println(comment);
        }
    }

    /**
     * 模糊查询
     */
    @Test
    public void testQueryDoc8() {

        Pattern pattern = Pattern.compile(".*内容3.*");
        Query query = new Query(Criteria.where("commentContent").regex(pattern));
        List<Comment> comments = mongoTemplate.find(query, Comment.class);
        for (Comment comment : comments) {
            System.out.println(comment);
        }
    }

    /**
     * 修改文档
     */
    @Test
    public void testUpdateDoc() {

        Update update = new Update();
        update.set("commentGood", 888);
        update.set("commentContent", "我是评论的内容");

        Query query = new Query(Criteria.where("commentGood").is(2));
        UpdateResult updateResult = mongoTemplate.upsert(query, update, Comment.class);
        if (updateResult.getModifiedCount() > 0) {
            System.out.println("更新成功");
        }

    }

    /**
     * 删除文档
     */
    @Test
    public void testDeleteDoc() {

        Query query = new Query();
        query.addCriteria(Criteria.where("commentUser").is(333333));
        DeleteResult deleteResult = mongoTemplate.remove(query, Comment.class);
        if (deleteResult.getDeletedCount() > 0) {
            System.out.println("删除成功");
        }
    }


    /**
     * 基于MongoRepository开发CRUD,按照Spring Data规范就可以了。
     */
    @Test
    public void testFind() {
        List<Comment> list = commentService.findByCommentContentAndCommentUser("我是评论的内容2", 222222);
        System.out.println(JSON.toJSONString(list));
    }



}
