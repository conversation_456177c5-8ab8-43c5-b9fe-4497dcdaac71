package com.wendao101.teacher.service;

import com.alibaba.fastjson2.JSON;
import com.wendao101.teacher.domain.CourseDirectory;
import com.wendao101.teacher.domain.CourseDirectoryDy;
import org.springframework.beans.BeanUtils;

public class BeanCopyTest {

    public static void main(String[] args) {
        CourseDirectory directory = new CourseDirectory();
        directory.setId(123L);


        CourseDirectoryDy directoryDy = new CourseDirectoryDy();
        directoryDy.setId(456L);
        directoryDy.setChapterId(123L);

        BeanUtils.copyProperties(directory,directoryDy);

        System.out.println(JSON.toJSONString(directoryDy));

    }
}
