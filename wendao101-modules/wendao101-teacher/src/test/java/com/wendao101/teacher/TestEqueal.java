package com.wendao101.teacher;

import com.alibaba.fastjson2.JSON;
import com.wendao101.teacher.domain.DoudianCourse;
import com.wendao101.teacher.dto.DouDianExistDTO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestEqueal {
//    public static void main(String[] args) {
//        TestDTO testDTO = new TestDTO();
//        testDTO.setId(3L);
//
//        System.out.println(testDTO.getId()==3L);
//    }

    public static void main(String[] args) {
        Map<Long, DouDianExistDTO> map = new HashMap<>();
        List<DoudianCourse> doudianCourses = new ArrayList<>();
        {
            DoudianCourse d1 = new DoudianCourse();
            d1.setOutProductId(24206L);
            d1.setProductType(3L);
            doudianCourses.add(d1);
        }
        {
            DoudianCourse d1 = new DoudianCourse();
            d1.setOutProductId(9000024206L);
            d1.setProductType(3L);
            doudianCourses.add(d1);
        }
        {
            DoudianCourse d1 = new DoudianCourse();
            d1.setOutProductId(8000024206L);
            d1.setProductType(0L);
            doudianCourses.add(d1);
        }
        for (DoudianCourse doudianCourse : doudianCourses) {
            String outProductId = String.valueOf(doudianCourse.getOutProductId());

            if (outProductId.startsWith("90000")) {
                outProductId = outProductId.replace("90000", "");
            }
            if (outProductId.startsWith("80000")) {
                outProductId = outProductId.replace("80000", "");
            }
            //System.out.println(outProductId);
            Long outProductId1 = Long.valueOf(outProductId);
            DouDianExistDTO douDianExistDTO = map.get(outProductId1);
            //System.out.println(douDianExistDTO);
            if (douDianExistDTO == null) {
                douDianExistDTO = new DouDianExistDTO();
                map.put(outProductId1, douDianExistDTO);
            }
            //douDianExistDTO.setVirtualGoods(doudianCourse.getProductType() == 3);
            //System.out.println("ProductType:"+doudianCourse.getProductType());
            //System.out.println(doudianCourse.getProductType() == 0);
            if(!douDianExistDTO.isRealGoods()){
                douDianExistDTO.setRealGoods(doudianCourse.getProductType() == 0);
            }
            if(!douDianExistDTO.isVirtualGoods()){
                douDianExistDTO.setVirtualGoods(doudianCourse.getProductType() == 3);
            }
            douDianExistDTO.setCourseId(outProductId1);
            System.out.println(douDianExistDTO);
        }

        System.out.println(JSON.toJSONString(map));
    }
}
