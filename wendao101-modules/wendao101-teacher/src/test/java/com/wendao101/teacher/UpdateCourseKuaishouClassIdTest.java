package com.wendao101.teacher;

import cn.hutool.core.io.FileUtil;

import java.util.*;

public class UpdateCourseKuaishouClassIdTest {
    public static void main(String[] args) {
        String dyTextFilePath = "C:\\Users\\<USER>\\Desktop\\dy_class_course.txt";
        String ksTextFilePath = "C:\\Users\\<USER>\\Desktop\\ks_class_course.txt";
        List<String> dyStrings = FileUtil.readLines(dyTextFilePath, "UTF-8");
        List<String> ksStrings = FileUtil.readLines(ksTextFilePath, "UTF-8");
        //iterator循环,如果含null则删除
        ksStrings.removeIf(item -> item.contains("null"));
        Map<String, Integer> map = new HashMap<>();
        for (String i : ksStrings) {
            //System.out.println(i);
            String[] split = i.split("\t");
            String mutiClass = split[0];
            if (mutiClass.contains("，")) {
                String[] split1 = mutiClass.split("，");
                for (String sp : split1) {
                    map.put(sp, Integer.parseInt(split[1]));
                }
            } else {
                map.put(mutiClass, Integer.parseInt(split[1]));
            }
        }

        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            System.out.println(entry.getKey() + ":" + entry.getValue());
        }
        //开始处理数据
        for (String dyClassString : dyStrings) {
            //System.out.println(dyClassString);
            String[] split = dyClassString.split("\t");
            Integer ksClassId = map.get(split[0]);
            if (ksClassId == null) {
                System.out.println("分类查找失败:" + split[0]);
            }
            Long courseId = Long.parseLong(split[1]);

            String stemp = "update course set kuaishou_class_id=%d,audit_status=1,ks_audit_status=0 where course_id_number=%d and is_delete=0;";
            System.out.println(String.format(stemp, ksClassId, courseId));
            String stemp1 = "update course_dy set kuaishou_class_id=%d,audit_status=1,ks_audit_status=0 where course_id_number=%d and is_delete=0;";
            System.out.println(String.format(stemp1, ksClassId, courseId));
        }
    }
}
