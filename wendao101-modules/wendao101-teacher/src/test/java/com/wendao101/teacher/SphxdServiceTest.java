package com.wendao101.teacher;

import com.alibaba.fastjson.JSON;
import com.wendao101.teacher.domain.TTeacher;
import com.wendao101.teacher.service.ITTeacherService;
import com.wendao101.teacher.service.WendaoWxChannelMessageService;
import me.chanjar.weixin.channel.api.BaseWxChannelMessageService;
import me.chanjar.weixin.channel.api.WxChannelService;
import me.chanjar.weixin.channel.message.WxChannelMessage;
import me.chanjar.weixin.channel.util.JsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SphxdServiceTest {
    @Autowired
    private WxChannelService channelService;

    @Autowired
    private ITTeacherService teacherService;

    @Autowired
    private WendaoWxChannelMessageService wendaoWxChannelMessageService;

    @Test
    public void testRoute() {
        String cipherText = "{\"ToUserName\":\"gh_e47fbbbcc2e3\",\"FromUserName\":\"oG9tU4yDS2cOp79Pv4uplVD73Cn8\",\"CreateTime\":1699983546,\"MsgType\":\"event\",\"Event\":\"channels_ec_order_deliver\",\"order_info\":{\"order_id\":3715726925221725696,\"finish_delivery\":1}}";
        WxChannelMessage message = JsonUtils.decode(cipherText, WxChannelMessage.class);
//        if(message.getEvent()!=null&&message.getEvent().equals("channels_ec_order_deliver")){
//
//        }
        //message.get

        //System.out.println(JSON.toJSONString(message));
        Object object = wendaoWxChannelMessageService.route(message, cipherText, channelService.getConfig().getAppid(), channelService);
        System.out.println(JSON.toJSONString(object));
    }

    @Test
    public void testRoute1() {
        TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(254125444L);
        System.out.println(tTeacher.getAppNameType());
    }
}
