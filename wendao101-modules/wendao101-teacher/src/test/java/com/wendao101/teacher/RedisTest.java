package com.wendao101.teacher;

import com.alibaba.fastjson.JSON;
import com.wendao101.common.redis.service.RedisService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Set;

@RunWith(SpringRunner.class)
@SpringBootTest
public class RedisTest {
    @Autowired
    RedisService redisService;
    @Test
    public void testRedisSet() {
        redisService.sSet("aaa","bbbb");
        redisService.sSet("aaa","2222");
        redisService.sSet("aaa","2222");

        Set<Object> aaa = redisService.getCacheSet("aaa");
        System.out.println(JSON.toJSONString(aaa));
    }
}
