package com.wendao101.job;

import com.tencentcloudapi.common.AbstractModel;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.*;
import com.wendao101.common.core.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class RecoverVideoTest {

    public static final Long subAppId = 1319546384L;
    /**
     * Expedited：极速模式。
     * Standard：标准模式。
     * Bulk：批量模式。
     */
    //默认
    public static final String restoreTier = "Expedited";
    //public static final String restoreTier = "Standard";
    //public static final String restoreTier = "Bulk";
    public static void main(String[] args) {
        List<String> list = new ArrayList<>();
        list.add("1397757890828598192");
        for (String fileId:list){
            process(fileId);
        }
    }
    public static void process(String fileId){
        try{
            Credential cred = new Credential("AKIDeFj6Vny8HLXf7cBjHmXEvgLtKcmsqdps", "73pQs38B8xiLrbK83Sr9KKJAqW5ylmp9");
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("vod.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            VodClient client = new VodClient(cred, "", clientProfile);
            DescribeMediaInfosRequest req = new DescribeMediaInfosRequest();
            String[] fileIds1 = {fileId};
            req.setFileIds(fileIds1);

            req.setSubAppId(subAppId);
            DescribeMediaInfosResponse resp = client.DescribeMediaInfos(req);
            System.out.println(AbstractModel.toJsonString(resp));
            if(resp.getMediaInfoSet()!=null&&resp.getMediaInfoSet().length>0){
                MediaInfo mediaInfo = resp.getMediaInfoSet()[0];
                MediaBasicInfo basicInfo = mediaInfo.getBasicInfo();
                String storageClass = basicInfo.getStorageClass();
                if(StringUtils.equals("ARCHIVE",storageClass)){
                    fetch(fileId);
                }
                if(StringUtils.equals("DEEP_ARCHIVE",storageClass)){
                    fetch1(fileId);
                }
            }
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
    }

    private static void fetch(String fileId) {
        System.out.println("正在取回ARCHIVE！fileId:"+fileId);
        try{
            Credential cred = new Credential("AKIDeFj6Vny8HLXf7cBjHmXEvgLtKcmsqdps", "73pQs38B8xiLrbK83Sr9KKJAqW5ylmp9");
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("vod.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            VodClient client = new VodClient(cred, "", clientProfile);
            ModifyMediaStorageClassRequest req = new ModifyMediaStorageClassRequest();
            String[] fileIds1 = {fileId};
            req.setFileIds(fileIds1);
            req.setStorageClass("STANDARD");
            req.setSubAppId(subAppId);
            req.setRestoreTier(restoreTier);
            ModifyMediaStorageClassResponse resp = client.ModifyMediaStorageClass(req);
            System.out.println(AbstractModel.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
    }

    private static void fetch1(String fileId) {
        System.out.println("正在取回DEEP_ARCHIVE！fileId:"+fileId);
        try{
            Credential cred = new Credential("AKIDeFj6Vny8HLXf7cBjHmXEvgLtKcmsqdps", "73pQs38B8xiLrbK83Sr9KKJAqW5ylmp9");
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("vod.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            VodClient client = new VodClient(cred, "", clientProfile);
            ModifyMediaStorageClassRequest req = new ModifyMediaStorageClassRequest();
            String[] fileIds1 = {fileId};
            req.setFileIds(fileIds1);
            req.setStorageClass("STANDARD");
            req.setSubAppId(subAppId);
            if("Bulk".equals(restoreTier)){
                req.setRestoreTier(restoreTier);
            }else{
                req.setRestoreTier("Standard");
            }
            ModifyMediaStorageClassResponse resp = client.ModifyMediaStorageClass(req);
            System.out.println(AbstractModel.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
    }
}
