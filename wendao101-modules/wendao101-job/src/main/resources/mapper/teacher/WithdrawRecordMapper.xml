<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.WithdrawRecordMapper">
    
    <resultMap type="WithdrawRecord" id="WithdrawRecordResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="promoterId"    column="promoter_id"    />
        <result property="withdrawOrderNumber"    column="withdraw_order_number"    />
        <result property="withdrawName"    column="withdraw_name"    />
        <result property="withdrawNameImg"    column="withdraw_name_img"    />
        <result property="fundsTransitPrice"    column="funds_transit_price"    />
        <result property="mayWithdrawPrice"    column="may_withdraw_price"    />
        <result property="withdrawPrice"    column="withdraw_price"    />
        <result property="accountPhone"    column="account_phone"    />
        <result property="accountPrice"    column="account_price"    />
        <result property="servicePriceRatio"    column="service_price_ratio"    />
        <result property="servicePrice"    column="service_price"    />
        <result property="incomePlatform"    column="income_platform"    />
        <result property="accountType"    column="account_type"    />
        <result property="withdrawApplyTime"    column="withdraw_apply_time"    />
        <result property="withdrawAccountTime"    column="withdraw_account_time"    />
        <result property="processingTime"    column="processing_time"    />
        <result property="tripartiteProcessingTime"    column="tripartite_processing_time"    />
        <result property="paymentSequenceNumber"    column="payment_sequence_number"    />
        <result property="zfbOrderId"    column="zfb_order_id"    />
        <result property="withdrawStatus"    column="withdraw_status"    />
        <result property="withdrawAuditStatus"    column="withdraw_audit_status"    />
        <result property="entityType"    column="entity_type"    />
        <result property="remarkMessage"    column="remark_message"    />
        <result property="evidence"    column="evidence"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWithdrawRecordVo">
        select id, teacher_id, promoter_id, withdraw_order_number, withdraw_name, withdraw_name_img, funds_transit_price, may_withdraw_price, withdraw_price, account_phone, account_price, service_price_ratio, service_price, income_platform, account_type, withdraw_apply_time, withdraw_account_time, processing_time, tripartite_processing_time, payment_sequence_number, zfb_order_id, withdraw_status, withdraw_audit_status, entity_type, remark_message, evidence, is_delete, create_time, update_time
        from `wendao101-order`.withdraw_record
    </sql>

    <select id="selectWithdrawRecordList" parameterType="WithdrawRecord" resultMap="WithdrawRecordResult">
        <include refid="selectWithdrawRecordVo"/>
        <where>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="promoterId != null "> and promoter_id = #{promoterId}</if>
            <if test="withdrawOrderNumber != null  and withdrawOrderNumber != ''"> and withdraw_order_number = #{withdrawOrderNumber}</if>
            <if test="withdrawName != null  and withdrawName != ''"> and withdraw_name like concat('%', #{withdrawName}, '%')</if>
            <if test="accountPhone != null  and accountPhone != ''"> and account_phone = #{accountPhone}</if>
            <if test="incomePlatform != null "> and income_platform = #{incomePlatform}</if>
            <if test="accountType != null "> and account_type = #{accountType}</if>
            <if test="withdrawStatus != null "> and withdraw_status = #{withdrawStatus}</if>
            <if test="withdrawAuditStatus != null "> and withdraw_audit_status = #{withdrawAuditStatus}</if>
            <if test="entityType != null "> and entity_type = #{entityType}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWithdrawRecordById" parameterType="Long" resultMap="WithdrawRecordResult">
        <include refid="selectWithdrawRecordVo"/>
        where id = #{id}
    </select>
    <select id="selectTotalWithdrawRecordList" resultType="com.wendao101.job.dto.WithDrawTotalDTO">
        select income_platform as incomePlatform,sum(withdraw_price) as withdrawTotal, sum(service_price) as serviceFeeTotal, sum(account_price) as sendMoneyTotal
        from `wendao101-order`.withdraw_record where teacher_id = #{teacherId} and withdraw_status=0 and withdraw_audit_status=0
       <if test="beginTime != null">
           <![CDATA[ and processing_time>=#{beginTime} ]]>
       </if>
        <if test="endTime != null">
            <![CDATA[ and processing_time<=#{endTime} ]]>
        </if>
         GROUP BY income_platform
    </select>
    <select id="selectPromoterTotalWithdrawRecordInPromoterList"
            resultType="com.wendao101.job.dto.WithDrawTotalDTO">
        select income_platform as incomePlatform,sum(withdraw_price) as withdrawTotal, sum(service_price) as serviceFeeTotal, sum(account_price) as sendMoneyTotal
        from `wendao101-order`.withdraw_record where withdraw_status=0 and withdraw_audit_status=0
        <if test="beginTime != null">
            <![CDATA[ and processing_time>=#{beginTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and processing_time<=#{endTime} ]]>
        </if>
        and promoter_id in
        <foreach item="promoterId" collection="list" open="(" separator="," close=")">
            #{promoterId}
        </foreach>
     GROUP BY income_platform
    </select>

    <!-- 计算老师净抽佣 -->
    <select id="calculateTeacherNetCommission" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(
            withdraw_price *
            CASE income_platform
                WHEN 0 THEN service_price_ratio  -- 抖音小程序: 原值保持不变
                WHEN 1 THEN (service_price_ratio - 1)  -- 微信小程序: 原值减1
                WHEN 2 THEN (service_price_ratio - 2)  -- 快手小程序: 原值减2
                WHEN 3 THEN (service_price_ratio - 3)  -- 视频号: 原值减3
                WHEN 5 THEN (service_price_ratio - 1)  -- h5端: 原值减1
                WHEN 7 THEN (service_price_ratio - 3)  -- 小红书: 原值减3
                WHEN 8 THEN (service_price_ratio - 1)  -- 知识店铺: 原值减1
                WHEN 9 THEN (service_price_ratio - 1)  -- pc端: 原值减1
                WHEN 11 THEN (service_price_ratio - 2) -- 抖店: 原值减2
                ELSE service_price_ratio
            END / 100
        ), 0) as netCommission
        FROM `wendao101-order`.withdraw_record
        WHERE teacher_id = #{teacherId}
        AND withdraw_status = 0
        AND withdraw_audit_status = 0
        <if test="beginTime != null">
            <![CDATA[ AND processing_time >= #{beginTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ AND processing_time <= #{endTime} ]]>
        </if>
    </select>

    <!-- 计算推广员净抽佣 -->
    <select id="calculatePromoterNetCommission" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(
            withdraw_price *
            CASE income_platform
                WHEN 0 THEN service_price_ratio  -- 抖音小程序: 原值保持不变
                WHEN 1 THEN (service_price_ratio - 1)  -- 微信小程序: 原值减1
                WHEN 2 THEN (service_price_ratio - 2)  -- 快手小程序: 原值减2
                WHEN 3 THEN (service_price_ratio - 3)  -- 视频号: 原值减3
                WHEN 5 THEN (service_price_ratio - 1)  -- h5端: 原值减1
                WHEN 7 THEN (service_price_ratio - 3)  -- 小红书: 原值减3
                WHEN 8 THEN (service_price_ratio - 1)  -- 知识店铺: 原值减1
                WHEN 9 THEN (service_price_ratio - 1)  -- pc端: 原值减1
                WHEN 11 THEN (service_price_ratio - 2) -- 抖店: 原值减2
                ELSE service_price_ratio
            END / 100
        ), 0) as netCommission
        FROM `wendao101-order`.withdraw_record
        WHERE withdraw_status = 0
        AND withdraw_audit_status = 0
        <if test="beginTime != null">
            <![CDATA[ AND processing_time >= #{beginTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ AND processing_time <= #{endTime} ]]>
        </if>
        AND promoter_id IN
        <foreach item="promoterId" collection="list" open="(" separator="," close=")">
            #{promoterId}
        </foreach>
    </select>
    <select id="selectBackupWithdrawRecordList" resultMap="WithdrawRecordResult">
        select id,
               teacher_id,
               promoter_id,
               withdraw_order_number,
               withdraw_name,
               withdraw_name_img,
               funds_transit_price,
               may_withdraw_price,
               withdraw_price,
               account_phone,
               account_price,
               service_price_ratio,
               service_price,
               income_platform,
               account_type,
               withdraw_apply_time,
               withdraw_account_time,
               processing_time,
               tripartite_processing_time,
               payment_sequence_number,
               zfb_order_id,
               withdraw_status,
               withdraw_audit_status,
               entity_type,
               remark_message,
               evidence,
               is_delete,
               create_time,
               update_time
        from `wendao101-order`.withdraw_record_backup where is_delete=0
    </select>
    <select id="getServicePriceRatioAndEntityType"
            resultType="com.wendao101.job.dto.WithdrawRecordAndReteEntityDTO">
        select id,a.rate_type   rateType,
            a.rate        servicePriceRatio,
            a.entity_type entityType
             ,a.dy_rate dyRate
             ,a.ks_rate ksRate
             ,a.wx_rate wxRate
             ,a.sph_rate sphRate
             ,a.zsdp_rate zsdpRate
             ,a.xhs_rate xhsRate
             ,a.h5_rate h5Rate
             ,a.pc_rate pcRate
             ,a.dd_rate ddRate
            ,a.create_time createTime
        FROM ry.enter_information a where a.shop_id = #{teacherId} LIMIT 1
    </select>

    <select id="getServicePriceRatioAndEntityTypeBackUp"
            resultType="com.wendao101.job.dto.WithdrawRecordAndReteEntityDTO">
        select id,a.rate_type   rateType,
            a.rate        servicePriceRatio,
            a.entity_type entityType
             ,a.dy_rate dyRate
             ,a.ks_rate ksRate
             ,a.wx_rate wxRate
             ,a.sph_rate sphRate
             ,a.zsdp_rate zsdpRate
             ,a.xhs_rate xhsRate
             ,a.h5_rate h5Rate
             ,a.pc_rate pcRate
             ,a.dd_rate ddRate
             ,a.create_time createTime
        FROM ry.enter_information_backup a where a.shop_id = #{teacherId} and is_delete=0 order by a.create_time desc limit 1
    </select>
    <select id="queryTotalMoneyByPlatform" resultType="java.math.BigDecimal">
        <![CDATA[select sum(income_price) from `wendao101-order`.fund_income where teacher_id=#{teacherId} and is_delete=0 and income_type!=3 and income_type!=4 and income_platform=#{incomePlatform} and order_time < #{oldChangeNetTime} ]]>
    </select>
    <select id="selectPromoterTotalWithdrawRecordForPromoterList"
            resultType="com.wendao101.job.dto.WithDrawTotalDTO">
        select income_platform as incomePlatform,promoter_id as promoterId,sum(withdraw_price) as withdrawTotal, sum(service_price) as serviceFeeTotal, sum(account_price) as sendMoneyTotal
        from `wendao101-order`.withdraw_record where withdraw_status=0 and withdraw_audit_status=0
        <if test="beginTime != null">
            <![CDATA[ and processing_time>=#{beginTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and processing_time<=#{endTime} ]]>
        </if>
        and promoter_id in
        <foreach item="promoterId" collection="list" open="(" separator="," close=")">
            #{promoterId}
        </foreach>
        GROUP BY income_platform,promoter_id
    </select>

    <insert id="insertWithdrawRecord" parameterType="WithdrawRecord" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.withdraw_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="promoterId != null">promoter_id,</if>
            <if test="withdrawOrderNumber != null">withdraw_order_number,</if>
            <if test="withdrawName != null">withdraw_name,</if>
            <if test="withdrawNameImg != null">withdraw_name_img,</if>
            <if test="fundsTransitPrice != null">funds_transit_price,</if>
            <if test="mayWithdrawPrice != null">may_withdraw_price,</if>
            <if test="withdrawPrice != null">withdraw_price,</if>
            <if test="accountPhone != null">account_phone,</if>
            <if test="accountPrice != null">account_price,</if>
            <if test="servicePriceRatio != null">service_price_ratio,</if>
            <if test="servicePrice != null">service_price,</if>
            <if test="incomePlatform != null">income_platform,</if>
            <if test="accountType != null">account_type,</if>
            <if test="withdrawApplyTime != null">withdraw_apply_time,</if>
            <if test="withdrawAccountTime != null">withdraw_account_time,</if>
            <if test="processingTime != null">processing_time,</if>
            <if test="tripartiteProcessingTime != null">tripartite_processing_time,</if>
            <if test="paymentSequenceNumber != null">payment_sequence_number,</if>
            <if test="zfbOrderId != null">zfb_order_id,</if>
            <if test="withdrawStatus != null">withdraw_status,</if>
            <if test="withdrawAuditStatus != null">withdraw_audit_status,</if>
            <if test="entityType != null">entity_type,</if>
            <if test="remarkMessage != null">remark_message,</if>
            <if test="evidence != null">evidence,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="promoterId != null">#{promoterId},</if>
            <if test="withdrawOrderNumber != null">#{withdrawOrderNumber},</if>
            <if test="withdrawName != null">#{withdrawName},</if>
            <if test="withdrawNameImg != null">#{withdrawNameImg},</if>
            <if test="fundsTransitPrice != null">#{fundsTransitPrice},</if>
            <if test="mayWithdrawPrice != null">#{mayWithdrawPrice},</if>
            <if test="withdrawPrice != null">#{withdrawPrice},</if>
            <if test="accountPhone != null">#{accountPhone},</if>
            <if test="accountPrice != null">#{accountPrice},</if>
            <if test="servicePriceRatio != null">#{servicePriceRatio},</if>
            <if test="servicePrice != null">#{servicePrice},</if>
            <if test="incomePlatform != null">#{incomePlatform},</if>
            <if test="accountType != null">#{accountType},</if>
            <if test="withdrawApplyTime != null">#{withdrawApplyTime},</if>
            <if test="withdrawAccountTime != null">#{withdrawAccountTime},</if>
            <if test="processingTime != null">#{processingTime},</if>
            <if test="tripartiteProcessingTime != null">#{tripartiteProcessingTime},</if>
            <if test="paymentSequenceNumber != null">#{paymentSequenceNumber},</if>
            <if test="zfbOrderId != null">#{zfbOrderId},</if>
            <if test="withdrawStatus != null">#{withdrawStatus},</if>
            <if test="withdrawAuditStatus != null">#{withdrawAuditStatus},</if>
            <if test="entityType != null">#{entityType},</if>
            <if test="remarkMessage != null">#{remarkMessage},</if>
            <if test="evidence != null">#{evidence},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWithdrawRecord" parameterType="WithdrawRecord">
        update `wendao101-order`.withdraw_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="promoterId != null">promoter_id = #{promoterId},</if>
            <if test="withdrawOrderNumber != null">withdraw_order_number = #{withdrawOrderNumber},</if>
            <if test="withdrawName != null">withdraw_name = #{withdrawName},</if>
            <if test="withdrawNameImg != null">withdraw_name_img = #{withdrawNameImg},</if>
            <if test="fundsTransitPrice != null">funds_transit_price = #{fundsTransitPrice},</if>
            <if test="mayWithdrawPrice != null">may_withdraw_price = #{mayWithdrawPrice},</if>
            <if test="withdrawPrice != null">withdraw_price = #{withdrawPrice},</if>
            <if test="accountPhone != null">account_phone = #{accountPhone},</if>
            <if test="accountPrice != null">account_price = #{accountPrice},</if>
            <if test="servicePriceRatio != null">service_price_ratio = #{servicePriceRatio},</if>
            <if test="servicePrice != null">service_price = #{servicePrice},</if>
            <if test="incomePlatform != null">income_platform = #{incomePlatform},</if>
            <if test="accountType != null">account_type = #{accountType},</if>
            <if test="withdrawApplyTime != null">withdraw_apply_time = #{withdrawApplyTime},</if>
            <if test="withdrawAccountTime != null">withdraw_account_time = #{withdrawAccountTime},</if>
            <if test="processingTime != null">processing_time = #{processingTime},</if>
            <if test="tripartiteProcessingTime != null">tripartite_processing_time = #{tripartiteProcessingTime},</if>
            <if test="paymentSequenceNumber != null">payment_sequence_number = #{paymentSequenceNumber},</if>
            <if test="zfbOrderId != null">zfb_order_id = #{zfbOrderId},</if>
            <if test="withdrawStatus != null">withdraw_status = #{withdrawStatus},</if>
            <if test="withdrawAuditStatus != null">withdraw_audit_status = #{withdrawAuditStatus},</if>
            <if test="entityType != null">entity_type = #{entityType},</if>
            <if test="remarkMessage != null">remark_message = #{remarkMessage},</if>
            <if test="evidence != null">evidence = #{evidence},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateWithdrawRecordBackup">
        update `wendao101-order`.withdraw_record_backup
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="promoterId != null">promoter_id = #{promoterId},</if>
            <if test="withdrawOrderNumber != null">withdraw_order_number = #{withdrawOrderNumber},</if>
            <if test="withdrawName != null">withdraw_name = #{withdrawName},</if>
            <if test="withdrawNameImg != null">withdraw_name_img = #{withdrawNameImg},</if>
            <if test="fundsTransitPrice != null">funds_transit_price = #{fundsTransitPrice},</if>
            <if test="mayWithdrawPrice != null">may_withdraw_price = #{mayWithdrawPrice},</if>
            <if test="withdrawPrice != null">withdraw_price = #{withdrawPrice},</if>
            <if test="accountPhone != null">account_phone = #{accountPhone},</if>
            <if test="accountPrice != null">account_price = #{accountPrice},</if>
            <if test="servicePriceRatio != null">service_price_ratio = #{servicePriceRatio},</if>
            <if test="servicePrice != null">service_price = #{servicePrice},</if>
            <if test="incomePlatform != null">income_platform = #{incomePlatform},</if>
            <if test="accountType != null">account_type = #{accountType},</if>
            <if test="withdrawApplyTime != null">withdraw_apply_time = #{withdrawApplyTime},</if>
            <if test="withdrawAccountTime != null">withdraw_account_time = #{withdrawAccountTime},</if>
            <if test="processingTime != null">processing_time = #{processingTime},</if>
            <if test="tripartiteProcessingTime != null">tripartite_processing_time = #{tripartiteProcessingTime},</if>
            <if test="paymentSequenceNumber != null">payment_sequence_number = #{paymentSequenceNumber},</if>
            <if test="zfbOrderId != null">zfb_order_id = #{zfbOrderId},</if>
            <if test="withdrawStatus != null">withdraw_status = #{withdrawStatus},</if>
            <if test="withdrawAuditStatus != null">withdraw_audit_status = #{withdrawAuditStatus},</if>
            <if test="entityType != null">entity_type = #{entityType},</if>
            <if test="remarkMessage != null">remark_message = #{remarkMessage},</if>
            <if test="evidence != null">evidence = #{evidence},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWithdrawRecordById" parameterType="Long">
        delete from `wendao101-order`.withdraw_record where id = #{id}
    </delete>

    <delete id="deleteWithdrawRecordByIds" parameterType="String">
        delete from `wendao101-order`.withdraw_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="deleteServicePriceRatioAndEntityTypeBackUp">
        update `ry`.enter_information_backup set is_delete=1 where id = #{id}
    </update>
</mapper> 