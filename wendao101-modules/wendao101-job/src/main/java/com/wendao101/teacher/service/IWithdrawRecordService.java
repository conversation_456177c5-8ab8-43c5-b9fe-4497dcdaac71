package com.wendao101.teacher.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.wendao101.job.dto.WithDrawTotalDTO;
import com.wendao101.job.dto.WithdrawRecordAndReteEntityDTO;
import com.wendao101.teacher.domain.WithdrawRecord;

/**
 * 提现记录Service接口
 * 
 * <AUTHOR>
 */
public interface IWithdrawRecordService {
    /**
     * 查询提现记录
     * 
     * @param id 提现记录主键
     * @return 提现记录
     */
    public WithdrawRecord selectWithdrawRecordById(Long id);

    /**
     * 查询提现记录列表
     * 
     * @param withdrawRecord 提现记录
     * @return 提现记录集合
     */
    public List<WithdrawRecord> selectWithdrawRecordList(WithdrawRecord withdrawRecord);

    /**
     * 新增提现记录
     * 
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    public int insertWithdrawRecord(WithdrawRecord withdrawRecord);

    /**
     * 修改提现记录
     * 
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    public int updateWithdrawRecord(WithdrawRecord withdrawRecord);

    /**
     * 批量删除提现记录
     * 
     * @param ids 需要删除的提现记录主键集合
     * @return 结果
     */
    public int deleteWithdrawRecordByIds(Long[] ids);

    /**
     * 删除提现记录信息
     * 
     * @param id 提现记录主键
     * @return 结果
     */
    public int deleteWithdrawRecordById(Long id);

    List<WithDrawTotalDTO> selectTotalWithdrawRecordList(Long teacherId, Date beginTime, Date endTime);

    List<WithDrawTotalDTO> selectPromoterTotalWithdrawRecordInPromoterList(List<Long> promoterIdList, Date beginTime, Date endTime);

    List<WithdrawRecord> selectBackupWithdrawRecordList();

    int updateWithdrawRecordBackup(WithdrawRecord withdrawRecord);

    WithdrawRecordAndReteEntityDTO getServicePriceRatioAndEntityType(Long teacherId);

    WithdrawRecordAndReteEntityDTO getServicePriceRatioAndEntityTypeBackUp(Long teacherId);

    BigDecimal queryTotalMoneyByPlatform(Long teacherId, Integer incomePlatform, Date oldChangeNetTime);

    int deleteServicePriceRatioAndEntityTypeBackUp(Long id);

    List<WithDrawTotalDTO> selectPromoterTotalWithdrawRecordForPromoterList(List<Long> promoterIdList, Date beginTime, Date endTime);

    /**
     * 计算老师净抽佣
     * @param teacherId 老师ID
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 净抽佣金额
     */
    BigDecimal calculateTeacherNetCommission(Long teacherId, Date beginTime, Date endTime);

    /**
     * 计算推广员净抽佣
     * @param promoterIdList 推广员ID列表
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 净抽佣金额
     */
    BigDecimal calculatePromoterNetCommission(List<Long> promoterIdList, Date beginTime, Date endTime);
}