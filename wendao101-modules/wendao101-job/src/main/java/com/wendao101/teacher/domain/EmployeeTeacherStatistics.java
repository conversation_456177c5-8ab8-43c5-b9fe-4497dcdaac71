package com.wendao101.teacher.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 教师统计对象 teacher_statistics
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EmployeeTeacherStatistics extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 统计日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date statisticsDate;

    /** 老师id */
    private Long teacherId;

    /** 老师手机号 */
    private String mobile;

    /** 店铺名称 */
    private String shopName;

    /** 店铺所属APP，1问到好课，2问到课堂 */
    private Integer appNameType;

    /** 交易金额 */
    private BigDecimal dealAmount;

    /** 已提现金额 */
    private BigDecimal withdrawnAmount;

    /** 在途资金 */
    private BigDecimal moneyInTransit;

    /** 可提现金额 */
    private BigDecimal withdrawableAmount;

    /** 服务费(废弃) */
    private BigDecimal serviceFee;

    /** 服务费率(废弃) */
    private BigDecimal serviceFeeRate;

    private String timeQueryStr;

    /** 已提现金额收取的毛抽佣费 */
    private BigDecimal withdrawnAmountFee;
    /** 未提现金额将来要抽的毛抽佣费用 */
    private BigDecimal notWithdrawnFee;

    /**
     * 已提现金额收取的毛抽佣费+未提现金额将来要抽的毛抽佣费用
     */
    private BigDecimal totalWithdrawnFee;

    /** 员工id */
    private Long employeeId;
    /** 员工昵称姓名 */
    private String nickName;
    
    /** 直接主管id */
    private Long leaderId;
    
    /** 直接主管昵称姓名 */
    private String leaderNickName;
    
    /** 上级主管id */
    private Long parentLeaderId;
    
    /** 上级主管昵称姓名 */
    private String parentLeaderNickName;
    
    /** 订单数量 */
    private Integer orderNum;

    /**
     * 已提现净抽佣
     */
    private BigDecimal withdrawnNetCommission;
    /**
     * 未提现净抽佣
     */
    private BigDecimal notWithdrawnNetCommission;

    /**
     * 已提现净抽佣+未提现净抽佣
     */
    private BigDecimal totalWithdrawnNetCommission;

} 