package com.wendao101.teacher.mapper;

import com.wendao101.job.dto.WithDrawTotalDTO;
import com.wendao101.job.dto.WithdrawRecordAndReteEntityDTO;
import com.wendao101.teacher.domain.WithdrawRecord;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 提现记录Mapper接口
 * 
 * <AUTHOR>
 */
public interface WithdrawRecordMapper {
    /**
     * 查询提现记录
     *
     * @param id 提现记录主键
     * @return 提现记录
     */
    public WithdrawRecord selectWithdrawRecordById(Long id);

    /**
     * 查询提现记录列表
     *
     * @param withdrawRecord 提现记录
     * @return 提现记录集合
     */
    public List<WithdrawRecord> selectWithdrawRecordList(WithdrawRecord withdrawRecord);

    /**
     * 新增提现记录
     *
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    public int insertWithdrawRecord(WithdrawRecord withdrawRecord);

    /**
     * 修改提现记录
     *
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    public int updateWithdrawRecord(WithdrawRecord withdrawRecord);

    /**
     * 删除提现记录
     *
     * @param id 提现记录主键
     * @return 结果
     */
    public int deleteWithdrawRecordById(Long id);

    /**
     * 批量删除提现记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWithdrawRecordByIds(Long[] ids);

    List<WithDrawTotalDTO> selectTotalWithdrawRecordList(@Param("teacherId") Long teacherId, @Param("beginTime")Date beginTime, @Param("endTime")Date endTime);

    List<WithDrawTotalDTO> selectPromoterTotalWithdrawRecordInPromoterList(@Param("list")List<Long> promoterIdList,@Param("beginTime")Date beginTime, @Param("endTime")Date endTime);

    List<WithdrawRecord> selectBackupWithdrawRecordList();

    int updateWithdrawRecordBackup(WithdrawRecord withdrawRecord);

    WithdrawRecordAndReteEntityDTO getServicePriceRatioAndEntityType(@Param("teacherId")Long teacherId);

    WithdrawRecordAndReteEntityDTO getServicePriceRatioAndEntityTypeBackUp(@Param("teacherId")Long teacherId);

    BigDecimal queryTotalMoneyByPlatform(@Param("teacherId")Long teacherId, @Param("incomePlatform")Integer incomePlatform, @Param("oldChangeNetTime")Date oldChangeNetTime);

    int deleteServicePriceRatioAndEntityTypeBackUp(@Param("id")Long id);

    List<WithDrawTotalDTO> selectPromoterTotalWithdrawRecordForPromoterList(@Param("list")List<Long> promoterIdList, @Param("beginTime")Date beginTime, @Param("endTime")Date endTime);

    /**
     * 计算老师净抽佣
     * @param teacherId 老师ID
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 净抽佣金额
     */
    BigDecimal calculateTeacherNetCommission(@Param("teacherId")Long teacherId, @Param("beginTime")Date beginTime, @Param("endTime")Date endTime);

    /**
     * 计算推广员净抽佣
     * @param promoterIdList 推广员ID列表
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 净抽佣金额
     */
    BigDecimal calculatePromoterNetCommission(@Param("list")List<Long> promoterIdList, @Param("beginTime")Date beginTime, @Param("endTime")Date endTime);
}