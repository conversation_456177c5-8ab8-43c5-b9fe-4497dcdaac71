package com.wendao101.job.task;

import com.alibaba.fastjson2.JSON;
import com.wendao101.common.dto.TeacherStatisticsTaskDTO;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.job.dto.TeacherMoneyDTO;
import com.wendao101.job.dto.WithDrawTotalDTO;
import com.wendao101.job.dto.WithdrawRecordAndReteEntityDTO;
import com.wendao101.teacher.domain.*;
import com.wendao101.teacher.mapper.EmployeeStatisticsMapper;
import com.wendao101.teacher.mapper.EmployeeTeacherStatisticsMapper;
import com.wendao101.teacher.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 员工绩效计算任务
 */
@Component("employeePerformanceTask")
public class EmployeePerformanceTask {
    public static final String EMPLOYEE_TEACHER_STATISTICS_TIME_QUERY = "employeeTeacherStatisticsTimeQuery";
    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IFundIncomeService fundIncomeService;

    @Autowired
    private IEnterInformationService enterInformationService;
    @Autowired
    private ICourseOrderService courseOrderService;
    @Autowired
    private EmployeeTeacherStatisticsMapper employeeTeacherStatisticsMapper;

    @Autowired
    private ITTeacherService tTeacherService;

    @Autowired
    private IWithdrawRecordService withdrawRecordService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ITeacherStatisticsTimeQueryService teacherStatisticsTimeQueryService;

    @Autowired
    private EmployeeStatisticsMapper employeeStatisticsMapper;
    @Autowired
    private IEmployeeSalesStatisticsDaysService employeeStatisticsDaysService;
    @Autowired
    private IEmployeeSalesStatisticsService employeeSalesStatisticsService;


    /**
     * 每10秒一下
     * 如果redis中没有数据则直接返回
     */
    public void updateEmployeeTeacherMoney() {
        TeacherStatisticsTimeQuery t = redisService.getCacheObject(EMPLOYEE_TEACHER_STATISTICS_TIME_QUERY);
        if (t != null) {
            return;
        }
        //查询最新任务执行begin
        TeacherStatisticsTimeQuery teacherStatisticsTimeQuery = teacherStatisticsTimeQueryService.selectNewEmployeeTeacherStatisticsTimeQuery();
        if (teacherStatisticsTimeQuery == null) {
            return;
        }
        teacherStatisticsTimeQuery.setTaskStatus(1);
        teacherStatisticsTimeQueryService.updateEmployeeTeacherStatisticsTimeQuery(teacherStatisticsTimeQuery);
        redisService.setCacheObject(EMPLOYEE_TEACHER_STATISTICS_TIME_QUERY, teacherStatisticsTimeQuery);
        //查询最新任务执行end
        //处理查询数据
        TeacherStatisticsTaskDTO queryDTO = JSON.parseObject(teacherStatisticsTimeQuery.getQueryString(), TeacherStatisticsTaskDTO.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeRange = null;
        if (queryDTO.getBeginTime() == null && queryDTO.getEndTime() == null) {
            teacherStatisticsTimeQuery.setTaskStatus(3);
            teacherStatisticsTimeQueryService.updateEmployeeTeacherStatisticsTimeQuery(teacherStatisticsTimeQuery);
            redisService.deleteObject(EMPLOYEE_TEACHER_STATISTICS_TIME_QUERY);
            return;
        }
        if (queryDTO.getBeginTime() != null) {
            timeRange = sdf.format(teacherStatisticsTimeQuery.getBeginTime()) + "~";
        }
        if (queryDTO.getEndTime() != null) {
            timeRange = timeRange + sdf.format(teacherStatisticsTimeQuery.getEndTime());
        }
        if (queryDTO.getAppNameType() != null) {
            timeRange = timeRange + "~" + queryDTO.getAppNameType();
        }
//        if (StringUtils.isNotBlank(queryDTO.getTeacherInfo())) {
//            timeRange = timeRange + "~" + queryDTO.getTeacherInfo();
//        }
        if (StringUtils.isNotBlank(timeRange)) {
            int row = employeeTeacherStatisticsMapper.deleteEmployeeTeacherStatisticsBigerThanId(0L, timeRange);
        }
        List<SysUser> sysUserList = sysUserService.selectSwbUserList();
        for (SysUser sysUser : sysUserList) {
            String nickName = sysUser.getNickName();
            List<EnterInformation> enterInfos = enterInformationService.selectEnterInformationListByNickName(nickName);
            for (EnterInformation enterInfo : enterInfos) {
                Long teacherId = enterInfo.getShopId();
                TTeacher teacher = tTeacherService.selectTTeacherByTeacherId(teacherId);
                queryTeacherOrderInfo(teacher, sysUser, queryDTO.getBeginTime(), queryDTO.getEndTime(), timeRange);
            }
        }
        {
            //处理汇总数据
            insertEmployeeStatistics(timeRange);

        }
        //更新任务状态
        teacherStatisticsTimeQuery.setTaskStatus(2);
        teacherStatisticsTimeQueryService.updateEmployeeTeacherStatisticsTimeQuery(teacherStatisticsTimeQuery);
        redisService.deleteObject(EMPLOYEE_TEACHER_STATISTICS_TIME_QUERY);
    }

    @Deprecated
    public void calculate() {
        {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 时间取值 上个月1号0点到本月1号0点
            Calendar calendar = Calendar.getInstance();

            // 本月第一天 00:00:00
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date endDate = calendar.getTime();

            // 上个月第一天 00:00:00
            calendar.add(Calendar.MONTH, -1); // 回退一个月
            calendar.set(Calendar.DAY_OF_MONTH, 1); // 设为上月第一天
            Date startDate = calendar.getTime();

            TeacherStatisticsTaskDTO queryDTO = new TeacherStatisticsTaskDTO();
            queryDTO.setBeginTime(startDate);
            queryDTO.setEndTime(endDate);
            String timeRangeStr = sdf.format(startDate) + "~" + sdf.format(endDate);

            employeeTeacherStatisticsMapper.deleteEmployeeTeacherStatisticsBigerThanId(0L, timeRangeStr);

            List<SysUser> sysUserList = sysUserService.selectSwbUserList();
            for (SysUser sysUser : sysUserList) {
                String nickName = sysUser.getNickName();
                List<EnterInformation> enterInfos = enterInformationService.selectEnterInformationListByNickName(nickName);
                for (EnterInformation enterInfo : enterInfos) {
                    Long teacherId = enterInfo.getShopId();
                    TTeacher teacher = tTeacherService.selectTTeacherByTeacherId(teacherId);
                    queryTeacherOrderInfo(teacher, sysUser, queryDTO.getBeginTime(), queryDTO.getEndTime(), timeRangeStr);
                }
            }

            {
                //处理汇总数据
                insertEmployeeStatistics(timeRangeStr);

            }

            System.out.println("开始时间: " + sdf.format(startDate));
            System.out.println("结束时间: " + sdf.format(endDate));
        }

        {
            // 今天的0点和昨天的0点
            // 1. 获取当前时间的Calendar实例
            Calendar calendar = Calendar.getInstance();

            // 2. 获取今天的0点（00:00:00.000）
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date todayZero = calendar.getTime();

            // 3. 获取昨天的0点（日期减1天）
            calendar.add(Calendar.DATE, -1);
            Date yesterdayZero = calendar.getTime();

            // 4. 获取前天的0点（再减1天）
            calendar.add(Calendar.DATE, -1);  // 在前一天基础上再减1天
            Date dayBeforeYesterdayZero = calendar.getTime();
            // 创建日期格式化器
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            {
                TeacherStatisticsTaskDTO queryDTO = new TeacherStatisticsTaskDTO();
                queryDTO.setBeginTime(dayBeforeYesterdayZero);
                queryDTO.setEndTime(yesterdayZero);
                String timeRangeStr = sdf.format(dayBeforeYesterdayZero) + "~" + sdf.format(yesterdayZero);

                employeeTeacherStatisticsMapper.deleteEmployeeTeacherStatisticsBigerThanId(0L, timeRangeStr);

                List<SysUser> sysUserList = sysUserService.selectSwbUserList();
                for (SysUser sysUser : sysUserList) {
                    String nickName = sysUser.getNickName();
                    List<EnterInformation> enterInfos = enterInformationService.selectEnterInformationListByNickName(nickName);
                    for (EnterInformation enterInfo : enterInfos) {
                        Long teacherId = enterInfo.getShopId();
                        TTeacher teacher = tTeacherService.selectTTeacherByTeacherId(teacherId);
                        queryTeacherOrderInfo(teacher, sysUser, queryDTO.getBeginTime(), queryDTO.getEndTime(), timeRangeStr);
                    }
                }
                insertEmployeeStatistics(timeRangeStr);
            }

            {
                TeacherStatisticsTaskDTO queryDTO = new TeacherStatisticsTaskDTO();
                queryDTO.setBeginTime(yesterdayZero);
                queryDTO.setEndTime(todayZero);
                String timeRangeStr = sdf.format(yesterdayZero) + "~" + sdf.format(todayZero);
                employeeTeacherStatisticsMapper.deleteEmployeeTeacherStatisticsBigerThanId(0L, timeRangeStr);
                List<SysUser> sysUserList = sysUserService.selectSwbUserList();
                for (SysUser sysUser : sysUserList) {
                    String nickName = sysUser.getNickName();
                    List<EnterInformation> enterInfos = enterInformationService.selectEnterInformationListByNickName(nickName);
                    for (EnterInformation enterInfo : enterInfos) {
                        Long teacherId = enterInfo.getShopId();
                        TTeacher teacher = tTeacherService.selectTTeacherByTeacherId(teacherId);
                        queryTeacherOrderInfo(teacher, sysUser, queryDTO.getBeginTime(), queryDTO.getEndTime(), timeRangeStr);
                    }
                }
                insertEmployeeStatistics(timeRangeStr);
            }

            // 5. 输出结果
            System.out.println("今天的0点: " + sdf.format(todayZero));
            System.out.println("昨天的0点: " + sdf.format(yesterdayZero));
            System.out.println("前天的0点: " + sdf.format(dayBeforeYesterdayZero));
        }
    }

    private void insertEmployeeStatistics(String timeRangeStr) {
        if(StringUtils.isBlank(timeRangeStr)){
            employeeStatisticsMapper.deleteEmployeeStatisticsBigerThanIdAndQueryStringIsNull(0L);
        }else{
            employeeStatisticsMapper.deleteEmployeeStatisticsBigerThanId(0L,timeRangeStr);
        }
        List<EmployeeStatistics> employeeStatistics = null;
        if (StringUtils.isBlank(timeRangeStr)) {
            employeeStatistics = employeeStatisticsMapper.selectEmployeeStatisticsListGroupByEmpIdTimeNull();
        } else {
            employeeStatistics = employeeStatisticsMapper.selectEmployeeStatisticsListGroupByEmpId(timeRangeStr);
        }
        if (CollectionUtils.isEmpty(employeeStatistics)) {
            return;
        }
        for (EmployeeStatistics item : employeeStatistics) {
            item.setTimeQueryStr(StringUtils.isBlank(timeRangeStr) ? null : timeRangeStr);
            employeeStatisticsMapper.insertEmployeeStatistics(item);
        }
    }

    /**
     * 默认数据汇总任务
     */
    public void updateTeacherMoneyAtOne() {
        int row = employeeTeacherStatisticsMapper.deleteEmployeeTeacherStatisticsBigerThanIdAndQueryStringIsNull(0L);
        List<SysUser> sysUserList = sysUserService.selectSwbUserList();
        for (SysUser sysUser : sysUserList) {
            String nickName = sysUser.getNickName();
            List<EnterInformation> enterInfos = enterInformationService.selectEnterInformationListByNickName(nickName);
            for (EnterInformation enterInfo : enterInfos) {
                Long teacherId = enterInfo.getShopId();
                TTeacher teacher = tTeacherService.selectTTeacherByTeacherId(teacherId);
                queryTeacherOrderInfo(teacher, sysUser, null, null, null);
            }
        }
        insertEmployeeStatistics(null);
    }

    private void queryTeacherOrderInfo(TTeacher teacher, SysUser sysUser, Date beginTime, Date endTime, String timeRangStr) {
        EmployeeTeacherStatistics teacherStatistics = new EmployeeTeacherStatistics();
        if (StringUtils.isNotBlank(timeRangStr)) {
            teacherStatistics.setTimeQueryStr(timeRangStr);
        }
        teacherStatistics.setLeaderId(sysUser.getLeaderId());
        teacherStatistics.setLeaderNickName(sysUser.getLeaderNickName());
        teacherStatistics.setEmployeeId(sysUser.getUserId());
        teacherStatistics.setNickName(sysUser.getNickName());
        BigDecimal totalWithdrawnPrice = BigDecimal.ZERO;
        BigDecimal totalServiceFee = BigDecimal.ZERO;
        Long teacherId = teacher.getTeacherId();
        int orderNum = courseOrderService.selectOrderNumberByTeacherId(teacherId, beginTime, endTime);
        teacherStatistics.setOrderNum(orderNum);
        //查询老师的毛的抽佣合计
        List<WithDrawTotalDTO> withDrawTotalDTOList = withdrawRecordService.selectTotalWithdrawRecordList(teacherId, beginTime, endTime);
        if (CollectionUtils.isNotEmpty(withDrawTotalDTOList)) {
            for (WithDrawTotalDTO dto : withDrawTotalDTOList) {
                totalWithdrawnPrice = totalWithdrawnPrice.add(dto.getWithdrawTotal() == null ? BigDecimal.ZERO : dto.getWithdrawTotal());
                totalServiceFee = totalServiceFee.add(dto.getServiceFeeTotal() == null ? BigDecimal.ZERO : dto.getServiceFeeTotal());
            }
        }
        //查询老师的净的抽佣合计
        BigDecimal teacherNetCommission = withdrawRecordService.calculateTeacherNetCommission(teacherId, beginTime, endTime);
        //查该老师的推广员毛的抽佣合计
        List<Long> promoterIdList = employeeTeacherStatisticsMapper.selectPromoterIdsByTeacherId(teacherId);
        List<WithDrawTotalDTO> promoterWithDrawTotalDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(promoterIdList)) {
            //已修改
            promoterWithDrawTotalDTOList = withdrawRecordService.selectPromoterTotalWithdrawRecordForPromoterList(promoterIdList, beginTime, endTime);
            if (CollectionUtils.isNotEmpty(promoterWithDrawTotalDTOList)) {
                for (WithDrawTotalDTO dto : promoterWithDrawTotalDTOList) {
                    totalWithdrawnPrice = totalWithdrawnPrice.add(dto.getWithdrawTotal() == null ? BigDecimal.ZERO : dto.getWithdrawTotal());
                    totalServiceFee = totalServiceFee.add(dto.getServiceFeeTotal() == null ? BigDecimal.ZERO : dto.getServiceFeeTotal());
                }
            }
        }
        //查该老师的推广员净的抽佣合计
        BigDecimal promoterNetCommission = withdrawRecordService.calculatePromoterNetCommission(promoterIdList, beginTime, endTime);
        teacherStatistics.setTeacherId(teacherId);
        teacherStatistics.setAppNameType(teacher.getAppNameType());
        teacherStatistics.setMobile(teacher.getMobile());
        teacherStatistics.setShopName(teacher.getShopName());
        //已提现金额
        teacherStatistics.setWithdrawnAmount(totalWithdrawnPrice);
        //总服务费
        teacherStatistics.setWithdrawnAmountFee(totalServiceFee);
        //总交易金额,应该为扣除推广员的钱
        //BigDecimal dealAmount = fundIncomeService.selectSumFundIncomeAllByTeacherId(teacher.getTeacherId());
        BigDecimal dealAmount = courseOrderService.selectSumOrderPriceByTeacherId(teacherId, beginTime, endTime);
        teacherStatistics.setDealAmount(dealAmount == null ? BigDecimal.ZERO : dealAmount);
        //老师在途资金
        BigDecimal moneyInTransit = fundIncomeService.selectSumFundIncomeByTeacherId(teacherId, beginTime, endTime);
        if (moneyInTransit == null) {
            moneyInTransit = BigDecimal.ZERO;
        }
        //推广员在途资金
        if (CollectionUtils.isNotEmpty(promoterIdList)) {
            BigDecimal promoterMoneyInTransit = fundIncomeService.selectSumFundIncomeForPromoterIds(promoterIdList, beginTime, endTime);
            if (promoterMoneyInTransit != null) {
                moneyInTransit = moneyInTransit.add(promoterMoneyInTransit);
            }
        }
        teacherStatistics.setMoneyInTransit(moneyInTransit);
        //可提现金额,入账金额-已提现金额
        //老师入账金额
        BigDecimal moneyInAccount = fundIncomeService.selectSumFundIncomeForInAccountByTeacherId(teacherId, beginTime, endTime);
        if (moneyInAccount == null) {
            moneyInAccount = BigDecimal.ZERO;
        }
        //推广员入账金额
        if (CollectionUtils.isNotEmpty(promoterIdList)) {
            BigDecimal promoterMoneyInAccount = fundIncomeService.selectSumFundIncomeForInAccountByPromoterIds(promoterIdList, beginTime, endTime);
            if (promoterMoneyInAccount != null) {
                moneyInAccount = moneyInAccount.add(promoterMoneyInAccount);
            }
        }
        teacherStatistics.setWithdrawableAmount(moneyInAccount.subtract(teacherStatistics.getWithdrawnAmount()));
        //teacherStatistics.setServiceFee(totalServiceFee);

        //未提现金额将来要抽的费用(新)
        // BigDecimal notWithdrawnFee = BigDecimal.ZERO;
        //查询各个平台的费率
        WithdrawRecordAndReteEntityDTO teacherRate = withdrawRecordService.getServicePriceRatioAndEntityType(teacherId);
        Map<Integer, BigDecimal> teacherRateMap = generateRateMap(teacherRate);
        WithdrawRecordAndReteEntityDTO promoterRate = getPromoterRate();
        Map<Integer, BigDecimal> promoterRateMap = generateRateMap(promoterRate);
        //提现平台，0抖音 1微信 2快手 3视频号 8知识店铺 9pc端 5h5端(wap端) 7小红书,11抖店
        //先是老师
        List<TeacherMoneyDTO> teacherMoneyDTOList = fundIncomeService.queryMoneyByPlatform(teacherId, beginTime, endTime);
        BigDecimal teacherRestMoneyFee = processRestMoneyFee(withDrawTotalDTOList, teacherMoneyDTOList, teacherRateMap);
        //这个是毛抽佣费
        teacherRestMoneyFee = teacherRestMoneyFee == null ? BigDecimal.ZERO : teacherRestMoneyFee;
        //计算老师未提现净抽佣费
        Map<Integer, BigDecimal> teacherNetRateMap = generateNetRateMap(teacherRate);
        BigDecimal teacherRestNetMoneyFee = processRestMoneyFee(withDrawTotalDTOList, teacherMoneyDTOList, teacherNetRateMap);
        teacherRestNetMoneyFee = teacherRestNetMoneyFee == null ? BigDecimal.ZERO : teacherRestNetMoneyFee;

        //再是推广员
        List<TeacherMoneyDTO> promoterMoneyDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(promoterIdList)) {
            promoterMoneyDTOList = fundIncomeService.queryPromoterMoneyByPlatform(promoterIdList, beginTime, endTime);
        }
        BigDecimal promoterRestMoneyFee = processRestMoneyFee(promoterWithDrawTotalDTOList, promoterMoneyDTOList, promoterRateMap);
        promoterRestMoneyFee = promoterRestMoneyFee == null ? BigDecimal.ZERO : promoterRestMoneyFee;
        teacherStatistics.setNotWithdrawnFee(teacherRestMoneyFee.add(promoterRestMoneyFee));
        //计算推广员未提现净抽佣
        Map<Integer, BigDecimal> promoterNetRateMap = generateNetRateMap(promoterRate);
        BigDecimal promoterRestNetMoneyFee = processRestMoneyFee(promoterWithDrawTotalDTOList, promoterMoneyDTOList, promoterNetRateMap);
        promoterRestNetMoneyFee = promoterRestNetMoneyFee == null ? BigDecimal.ZERO : promoterRestNetMoneyFee;
        teacherStatistics.setNotWithdrawnNetCommission(teacherRestNetMoneyFee.add(promoterRestNetMoneyFee));

        // 设置已提现净抽佣（老师净抽佣 + 推广员净抽佣）
        BigDecimal totalWithdrawnNetCommission = (teacherNetCommission == null ? BigDecimal.ZERO : teacherNetCommission)
                .add(promoterNetCommission == null ? BigDecimal.ZERO : promoterNetCommission);
        teacherStatistics.setWithdrawnNetCommission(totalWithdrawnNetCommission);

        teacherStatistics.setTotalWithdrawnFee(teacherStatistics.getWithdrawnAmountFee().add(teacherStatistics.getNotWithdrawnFee()));
        teacherStatistics.setTotalWithdrawnNetCommission(teacherStatistics.getWithdrawnNetCommission().add(teacherStatistics.getNotWithdrawnNetCommission()));
        employeeTeacherStatisticsMapper.insertEmployeeTeacherStatistics(teacherStatistics);
    }

    /**
     * 处理推广员金额
     *
     * @param withdrawMoneyList 各个平台已提现金额
     * @param totalMoneyList    各个平台金额
     * @param rateMap           平台费率
     */
    private BigDecimal processRestMoneyFee(List<WithDrawTotalDTO> withdrawMoneyList, List<TeacherMoneyDTO> totalMoneyList, Map<Integer, BigDecimal> rateMap) {
        //按平台转map withdrawMoneyList
        Map<Integer, WithDrawTotalDTO> withdrawPlatformMoneyMap = new HashMap<>();
        for (WithDrawTotalDTO dto : withdrawMoneyList) {
            withdrawPlatformMoneyMap.put(dto.getIncomePlatform(), dto);
        }
        //按平台转map totalMoneyList
        Map<Integer, TeacherMoneyDTO> totalMoneyPlatformMap = new HashMap<>();
        for (TeacherMoneyDTO dto : totalMoneyList) {
            totalMoneyPlatformMap.put(dto.getIncomePlatform(), dto);
        }
        BigDecimal totalPlatformRestServiceFee = BigDecimal.ZERO;
        for (Map.Entry<Integer, BigDecimal> entry : rateMap.entrySet()) {
            BigDecimal platformRestMoney = BigDecimal.ZERO;
            Integer incomePlatform = entry.getKey();
            BigDecimal rate = entry.getValue();
            TeacherMoneyDTO teacherMoneyDTO = totalMoneyPlatformMap.get(incomePlatform);
            if (teacherMoneyDTO == null) {
                continue;
            }
            WithDrawTotalDTO withDrawTotalDTO = withdrawPlatformMoneyMap.get(incomePlatform);
            if (withDrawTotalDTO == null) {
                platformRestMoney = teacherMoneyDTO.getTotalMoney() == null ? BigDecimal.ZERO : teacherMoneyDTO.getTotalMoney();
            } else {
                platformRestMoney = teacherMoneyDTO.getTotalMoney().subtract(withDrawTotalDTO.getWithdrawTotal() == null ? BigDecimal.ZERO : withDrawTotalDTO.getWithdrawTotal());
            }
            BigDecimal platformRestServiceFee = platformRestMoney.compareTo(BigDecimal.ZERO) > 0 ? platformRestMoney.multiply(rate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            totalPlatformRestServiceFee = totalPlatformRestServiceFee.add(platformRestServiceFee);
        }
        return totalPlatformRestServiceFee;
    }

    private static Map<Integer, BigDecimal> generateRateMap(WithdrawRecordAndReteEntityDTO rateDTO) {
        Map<Integer, BigDecimal> rateMap = new HashMap<>();
        rateMap.put(0, rateDTO.getDyRate() == null ? new BigDecimal(10) : rateDTO.getDyRate());
        rateMap.put(1, rateDTO.getWxRate() == null ? new BigDecimal(10) : rateDTO.getWxRate());
        rateMap.put(2, rateDTO.getKsRate() == null ? new BigDecimal(10) : rateDTO.getKsRate());
        rateMap.put(3, rateDTO.getSphRate() == null ? new BigDecimal(11) : rateDTO.getSphRate());
        rateMap.put(8, rateDTO.getZsdpRate() == null ? new BigDecimal(10) : rateDTO.getZsdpRate());
        rateMap.put(9, rateDTO.getPcRate() == null ? new BigDecimal(10) : rateDTO.getPcRate());
        rateMap.put(5, rateDTO.getH5Rate() == null ? new BigDecimal(10) : rateDTO.getH5Rate());
        rateMap.put(7, rateDTO.getXhsRate() == null ? new BigDecimal(11) : rateDTO.getXhsRate());
        rateMap.put(11, rateDTO.getDdRate() == null ? new BigDecimal(11) : rateDTO.getDdRate());
        return rateMap;
    }

    /**
     * 生成净费率映射，根据平台扣减相应费率
     * 抖音小程序(platform=0): 原值保持不变
     * 微信小程序(platform=1): 原值减1
     * 快手小程序(platform=2): 原值减2
     * 视频号(platform=3): 原值减3
     * 知识店铺(platform=8): 原值减1
     * pc端(platform=9): 原值减1
     * h5端(platform=5): 原值减1
     * 小红书(platform=7): 原值减3
     * 抖店(platform=11): 原值减2
     */
    private static Map<Integer, BigDecimal> generateNetRateMap(WithdrawRecordAndReteEntityDTO rateDTO) {
        Map<Integer, BigDecimal> netRateMap = new HashMap<>();
        // 抖音小程序: 原值保持不变
        netRateMap.put(0, rateDTO.getDyRate() == null ? new BigDecimal(10) : rateDTO.getDyRate());
        // 微信小程序: 原值减1
        BigDecimal wxRate = rateDTO.getWxRate() == null ? new BigDecimal(10) : rateDTO.getWxRate();
        netRateMap.put(1, wxRate.subtract(BigDecimal.ONE));
        // 快手小程序: 原值减2
        BigDecimal ksRate = rateDTO.getKsRate() == null ? new BigDecimal(10) : rateDTO.getKsRate();
        netRateMap.put(2, ksRate.subtract(new BigDecimal(2)));
        // 视频号: 原值减3
        BigDecimal sphRate = rateDTO.getSphRate() == null ? new BigDecimal(11) : rateDTO.getSphRate();
        netRateMap.put(3, sphRate.subtract(new BigDecimal(3)));
        // 知识店铺: 原值减1
        BigDecimal zsdpRate = rateDTO.getZsdpRate() == null ? new BigDecimal(10) : rateDTO.getZsdpRate();
        netRateMap.put(8, zsdpRate.subtract(BigDecimal.ONE));
        // pc端: 原值减1
        BigDecimal pcRate = rateDTO.getPcRate() == null ? new BigDecimal(10) : rateDTO.getPcRate();
        netRateMap.put(9, pcRate.subtract(BigDecimal.ONE));
        // h5端: 原值减1
        BigDecimal h5Rate = rateDTO.getH5Rate() == null ? new BigDecimal(10) : rateDTO.getH5Rate();
        netRateMap.put(5, h5Rate.subtract(BigDecimal.ONE));
        // 小红书: 原值减3
        BigDecimal xhsRate = rateDTO.getXhsRate() == null ? new BigDecimal(11) : rateDTO.getXhsRate();
        netRateMap.put(7, xhsRate.subtract(new BigDecimal(3)));
        // 抖店: 原值减2
        BigDecimal ddRate = rateDTO.getDdRate() == null ? new BigDecimal(11) : rateDTO.getDdRate();
        netRateMap.put(11, ddRate.subtract(new BigDecimal(2)));
        return netRateMap;
    }

    private static WithdrawRecordAndReteEntityDTO getPromoterRate() {
        WithdrawRecordAndReteEntityDTO promoterRate = new WithdrawRecordAndReteEntityDTO();
        promoterRate.setDyRate(new BigDecimal(10));
        promoterRate.setWxRate(new BigDecimal(10));
        promoterRate.setKsRate(new BigDecimal(10));
        promoterRate.setSphRate(new BigDecimal(11));
        promoterRate.setZsdpRate(new BigDecimal(10));
        promoterRate.setPcRate(new BigDecimal(10));
        promoterRate.setH5Rate(new BigDecimal(10));
        promoterRate.setXhsRate(new BigDecimal(11));
        promoterRate.setDdRate(new BigDecimal(11));
        return promoterRate;
    }

    public void allJobs() {
        employeeSalesStatisticsService.deleteEmployeeSalesStatisticsIdBigThenZero();
        employeeStatisticsDaysService.deleteEmployeeSalesStatisticsDaysByIdIdBigThenZero();
        calculateEmployeeYesterday();
        calculateEmployee7Days();
        calculateEmployeeMonth();
        calculateEmployeeThisYear();
        calculateEmployeeAllTime();
    }

    public void calculateEmployeeYesterday() {
        //创建两个时间,第一个时间为昨天0点0分0秒，第二个时间为今天0点0分0秒,用Calendar
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        //设置时分秒
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        //设置毫秒
        calendar.set(Calendar.MILLISECOND, 0);
        Date beginTime = calendar.getTime();
        Date endTime = DateUtils.addDays(beginTime, 1);

        processOrderData(beginTime, endTime);
        //数据汇总
        processSumData1(beginTime, endTime);

        //上一天
        calendar.add(Calendar.DATE, -1);
        Date beginTime1 = calendar.getTime();
        Date endTime1 = DateUtils.addDays(beginTime1, 1);
        processOrderData(beginTime1, endTime1);
        processSumData1(beginTime1, endTime1);
        {
            String statType = "daily";
            //先插入前一天的
            processDetailData(beginTime1, endTime1,statType);
            //再插入后一天的,保证id大的为最新的
            processDetailData(beginTime, endTime,statType);
            //计算教上一周期增长率
            calculateRateGrowth(statType);
        }
        //再上一天
        calendar.add(Calendar.DATE, -1);
        Date beginTime2 = calendar.getTime();
        Date endTime2 = DateUtils.addDays(beginTime2, 1);
        processOrderData(beginTime2, endTime2);
        processSumData1(beginTime2, endTime2);
        //再上一天
        calendar.add(Calendar.DATE, -1);
        Date beginTime3 = calendar.getTime();
        Date endTime3 = DateUtils.addDays(beginTime3, 1);
        processOrderData(beginTime3, endTime3);
        processSumData1(beginTime3, endTime3);
        //再上一天
        calendar.add(Calendar.DATE, -1);
        Date beginTime4 = calendar.getTime();
        Date endTime4 = DateUtils.addDays(beginTime4, 1);
        processOrderData(beginTime4, endTime4);
        processSumData1(beginTime4, endTime4);
        //再上一天
        calendar.add(Calendar.DATE, -1);
        Date beginTime5 = calendar.getTime();
        Date endTime5 = DateUtils.addDays(beginTime5, 1);
        processOrderData(beginTime5, endTime5);
        processSumData1(beginTime5, endTime5);
        //再上一天
        calendar.add(Calendar.DATE, -1);
        Date beginTime6 = calendar.getTime();
        Date endTime6 = DateUtils.addDays(beginTime6, 1);
        processOrderData(beginTime6, endTime6);
        processSumData1(beginTime6, endTime6);

    }
    public void calculateEmployee7Days() {
        //创建两个时间,第一个时间为7天前0点0分0秒，第二个时间为今天0点0分0秒,用Calendar
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -7);
        //设置时分秒
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        //设置毫秒
        calendar.set(Calendar.MILLISECOND, 0);
        Date beginTime = calendar.getTime();
        Date endTime = DateUtils.addDays(beginTime, 7);
        processOrderData(beginTime, endTime);
        //上一个周期,也就是上一个7天
        calendar.add(Calendar.DATE, -7);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date beginTime1 = calendar.getTime();
        Date endTime1 = DateUtils.addDays(beginTime1, 7);
        processOrderData(beginTime1, endTime1);

        {
            //先插入上一个7天,再插入最新7天,保证最新的id最大
            String statType = "weekly";
            processDetailData(beginTime1, endTime1,statType);
            processDetailData(beginTime, endTime,statType);
            //计算教上一周期增长率
            calculateRateGrowth(statType);
        }

    }
    public void calculateEmployeeMonth() {
        //创建两个时间,第一个时间为本月1号0点0分0秒,第二时间为今天0点0分0秒,用Calendar
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        //设置时分秒
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        //设置毫秒
        calendar.set(Calendar.MILLISECOND, 0);
        Date beginTime = calendar.getTime();

        Date endTime = getTodyZero();
        processOrderData(beginTime, endTime);
        //上个月到今天的日子
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date beginTime1 = calendar.getTime();
        Date endTime1 = new Date(beginTime1.getTime()+(endTime.getTime()-beginTime.getTime()));
        processOrderData(beginTime1, endTime1);

        {
            //先插入上一个月的,再插入本月,保证最新的id最大
            String statType = "monthly";
            processDetailData(beginTime1, endTime1,statType);
            processDetailData(beginTime, endTime,statType);
            //计算教上一周期增长率
            calculateRateGrowth(statType);
        }

    }
    public void calculateEmployeeThisYear() {
        //创建一个时间,第一个时间为本年1月1号0点0分0秒
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date beginTime = calendar.getTime();
        Date endTime = getTodyZero();
        processOrderData(beginTime, endTime);

        //创建去年1月1号0点0分0秒
        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR)-1);
        Date beginTime1 = calendar.getTime();
        Date endTime1 = new Date(beginTime1.getTime()+(endTime.getTime()-beginTime.getTime()));
        processOrderData(beginTime1, endTime1);
        {
            String statType = "yearly";
            processDetailData(beginTime1, endTime1,statType);
            processDetailData(beginTime, endTime,statType);
            calculateRateGrowth(statType);
        }
    }
    public void calculateEmployeeAllTime() {
        //创建一个时间,第一个时间为2020年1月1号0点0分0秒
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, 2020);
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date beginTime = calendar.getTime();
        Date endTime = getTodyZero();
        processOrderData(beginTime, endTime);
        {
            String statType = "allTime";
            processDetailData(beginTime, endTime,statType);
            calculateRateGrowth(statType);
        }
    }


    private static Date getTodyZero() {
        Calendar calendarToday = Calendar.getInstance();
        //设置时分秒
        calendarToday.set(Calendar.HOUR_OF_DAY, 0);
        calendarToday.set(Calendar.MINUTE, 0);
        calendarToday.set(Calendar.SECOND, 0);
        //设置毫秒
        calendarToday.set(Calendar.MILLISECOND, 0);
        return calendarToday.getTime();
    }

    private void processOrderData(Date beginTime, Date endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeRangeStr = sdf.format(beginTime) + "~" + sdf.format(endTime);
        employeeTeacherStatisticsMapper.deleteEmployeeTeacherStatisticsBigerThanId(0L, timeRangeStr);
        List<SysUser> sysUserList = sysUserService.selectSwbUserList();
        for (SysUser sysUser : sysUserList) {
            String nickName = sysUser.getNickName();
            List<EnterInformation> enterInfos = enterInformationService.selectEnterInformationListByNickName(nickName);
            for (EnterInformation enterInfo : enterInfos) {
                Long teacherId = enterInfo.getShopId();
                TTeacher teacher = tTeacherService.selectTTeacherByTeacherId(teacherId);
                queryTeacherOrderInfo(teacher, sysUser, beginTime, endTime, timeRangeStr);
            }
        }
    }

    private void calculateRateGrowth(String statType) {
        List<SysUser> sysUserList = sysUserService.selectSwbUserList();
        for (SysUser sysUser : sysUserList) {
            List<EmployeeSalesStatistics> listData1 = employeeSalesStatisticsService.selectTop2Data(sysUser.getUserId(), statType);
            if (CollectionUtils.isNotEmpty(listData1)) {
                EmployeeSalesStatistics updateItem = listData1.get(0);
                if (listData1.size() == 1) {
                    updateItem.setGrowthRate(new BigDecimal(100));
                    employeeSalesStatisticsService.updateEmployeeSalesStatistics(updateItem);
                }
                if (listData1.size() == 2) {
                    EmployeeSalesStatistics compareItem = listData1.get(1);
                    if (compareItem.getTotalSales() == null || compareItem.getTotalSales().compareTo(BigDecimal.ZERO) <= 0) {
                        updateItem.setGrowthRate(new BigDecimal(100));
                    } else {
                        updateItem.setGrowthRate(updateItem.getTotalSales().divide(compareItem.getTotalSales(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).subtract(new BigDecimal(100)));
                    }
                    compareItem.setDeptId(-1L);
                    employeeSalesStatisticsService.updateEmployeeSalesStatistics(compareItem);
                    employeeSalesStatisticsService.updateEmployeeSalesStatistics(updateItem);
                }
            }
        }
    }

    private void processDetailData(Date beginTime, Date endTime,String statType) {
        //汇总详细数据
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeRangeStr = sdf.format(beginTime) + "~" + sdf.format(endTime);
        List<EmployeeSalesStatistics> employeeSalesStatisticsList = employeeStatisticsMapper.selectEmployeeSaleStatisticsListGroupByEmpIdForDay(timeRangeStr);
        for (EmployeeSalesStatistics item : employeeSalesStatisticsList) {
            //item.setStatType("daily");
            item.setStatType(statType);
            employeeSalesStatisticsService.insertEmployeeSalesStatistics(item);
        }
    }

    private void processSumData1(Date beginTime, Date endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeRangeStr = sdf.format(beginTime) + "~" + sdf.format(endTime);
        List<EmployeeSalesStatisticsDays> employeeSalesStatisticsDaysList = employeeStatisticsMapper.selectEmployeeSaleStatisticsListGroupByEmpId(timeRangeStr);
        for (EmployeeSalesStatisticsDays item : employeeSalesStatisticsDaysList) {
            item.setStatDate(beginTime);
            employeeStatisticsDaysService.insertEmployeeSalesStatisticsDays(item);
        }
    }
}
