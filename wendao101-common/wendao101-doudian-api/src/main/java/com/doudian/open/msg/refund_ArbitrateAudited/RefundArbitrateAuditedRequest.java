package com.doudian.open.msg.refund_ArbitrateAudited;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_ArbitrateAudited.param.*;

//auto generated, do not edit

public class RefundArbitrateAuditedRequest extends DoudianOpMsgRequest<RefundArbitrateAuditedParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}