package com.doudian.open.msg.trade_contractPhoneWithhold;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.trade_contractPhoneWithhold.param.*;

//auto generated, do not edit

public class TradeContractPhoneWithholdRequest extends DoudianOpMsgRequest<TradeContractPhoneWithholdParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}