package com.doudian.open.msg.refund_ArbitrateCancelled;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_ArbitrateCancelled.param.*;

//auto generated, do not edit

public class RefundArbitrateCancelledRequest extends DoudianOpMsgRequest<RefundArbitrateCancelledParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}