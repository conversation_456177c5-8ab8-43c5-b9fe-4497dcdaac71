package com.doudian.open.msg.material_auditResultForBSCP;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.material_auditResultForBSCP.param.*;

//auto generated, do not edit

public class MaterialAuditResultForBSCPRequest extends DoudianOpMsgRequest<MaterialAuditResultForBSCPParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}