package com.doudian.open.core;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class GlobalConfig extends DoudianOpConfig {

    private static final Map<String, GlobalConfig> GLOBAL_CONFIGS = new ConcurrentHashMap<>();
    public static final String DEFAULT_CONFIG_NAME = "defaultConfig";
    static {
        initDefaultAppKeyAndAppSecret("7406208857338316322", "7d1a5657-6a2f-431c-b90c-3d50d98dfa67");
    }

    private GlobalConfig() {
        super();
    }

    private static boolean disableSdkLog = false;  // 添加这行

    public static synchronized GlobalConfig getGlobalConfig(String configName) {
        return GLOBAL_CONFIGS.computeIfAbsent(configName, k -> new GlobalConfig());
    }

    public static GlobalConfig getDefaultGlobalConfig() {
        return getGlobalConfig(DEFAULT_CONFIG_NAME);
    }

    public void initHttpClientReadTimeout(Integer timeout) {
        setHttpClientReadTimeout(timeout);
    }

    public void initHttpClientConnectTimeout(Integer timeout) {
        setHttpClientConnectTimeout(timeout);
    }

    public void initMaterialGatewayHttpClientReadTimeout(Integer timeout) {
        setMaterialGatewayHttpReadTimeout(timeout);
    }

    public void initMaterialGatewayHttpClientConnectTimeout(Integer timeout) {
        setMaterialGatewayHttpConnectTimeout(timeout);
    }

    public static void disableSdkLog() {
        GlobalConfig.disableSdkLog = true;
    }

    public static boolean isDisableSdkLog() {
        return GlobalConfig.disableSdkLog;
    }

    public void addAppKeyAndAppSecret(String appKey, String appSecret) {
        AddAppKeyAndSecret(appKey, appSecret);
    }

    // 静态方法用于操作默认配置
    public static void initDefaultHttpClientReadTimeout(Integer timeout) {
        getDefaultGlobalConfig().initHttpClientReadTimeout(timeout);
    }

    public static void initDefaultHttpClientConnectTimeout(Integer timeout) {
        getDefaultGlobalConfig().initHttpClientConnectTimeout(timeout);
    }

    public static void initDefaultMaterialGatewayHttpClientReadTimeout(Integer timeout) {
        getDefaultGlobalConfig().initMaterialGatewayHttpClientReadTimeout(timeout);
    }

    public static void initDefaultMaterialGatewayHttpClientConnectTimeout(Integer timeout) {
        getDefaultGlobalConfig().initMaterialGatewayHttpClientConnectTimeout(timeout);
    }

//    public static void disableDefaultSdkLog() {
//        getDefaultGlobalConfig().disableSdkLog();
//    }

//    public static boolean isDefaultSdkLogDisabled() {
//        return getDefaultGlobalConfig().isDisableSdkLog();
//    }
//
//    public static void addDefaultAppKeyAndAppSecret(String appKey, String appSecret) {
//        getDefaultGlobalConfig().addAppKeyAndAppSecret(appKey, appSecret);
//    }

    public static void initDefaultAppKeyAndAppSecret(String appKey, String appSecret) {
        GlobalConfig defaultConfig = getDefaultGlobalConfig();
        defaultConfig.setAppKey(appKey);
        defaultConfig.setAppSecret(appSecret);
    }

}
