package com.doudian.open.msg.refund_RefundSuccess;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_RefundSuccess.param.*;

//auto generated, do not edit

public class RefundRefundSuccessRequest extends DoudianOpMsgRequest<RefundRefundSuccessParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}