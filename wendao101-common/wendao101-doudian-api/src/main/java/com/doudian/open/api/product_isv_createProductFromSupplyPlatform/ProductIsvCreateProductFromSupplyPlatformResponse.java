package com.doudian.open.api.product_isv_createProductFromSupplyPlatform;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.product_isv_createProductFromSupplyPlatform.data.*;

//auto generated, do not edit

public class ProductIsvCreateProductFromSupplyPlatformResponse extends DoudianOpResponse<ProductIsvCreateProductFromSupplyPlatformData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}