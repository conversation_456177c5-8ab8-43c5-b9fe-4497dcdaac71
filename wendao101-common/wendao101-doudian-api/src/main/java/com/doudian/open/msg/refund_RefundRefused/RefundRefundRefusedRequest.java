package com.doudian.open.msg.refund_RefundRefused;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.refund_RefundRefused.param.*;

//auto generated, do not edit

public class RefundRefundRefusedRequest extends DoudianOpMsgRequest<RefundRefundRefusedParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}