package com.doudian.open.msg.Refund_SpecialRefundSuccess;

import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.msg.Refund_SpecialRefundSuccess.param.*;

//auto generated, do not edit

public class RefundSpecialRefundSuccessRequest extends DoudianOpMsgRequest<RefundSpecialRefundSuccessParam> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}