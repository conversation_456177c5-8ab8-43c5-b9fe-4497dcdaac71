package com.doudian.open.api.product_getCategoryPropertyValue;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.product_getCategoryPropertyValue.data.*;

//auto generated, do not edit

public class ProductGetCategoryPropertyValueResponse extends DoudianOpResponse<ProductGetCategoryPropertyValueData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}